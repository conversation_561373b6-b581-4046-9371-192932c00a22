#------------------------------------------------
query listProductFiles($query: String!, $cursor: String, $limit: Int!) {
  products(
    query: $query
    first: $limit
    sortKey: UPDATED_AT
    after: $cursor
    reverse: false
  ) {
    nodes {
      id
      title
      hasOnlyDefaultVariant
      updatedAt
      media(first: 250, query: "media_type:IMAGE") {
        nodes {
          ... on MediaImage {
            id
            status
            image {
              url
            }
            originalSource {
              fileSize
            }
          }
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
#{
#  "query": "updated_at:>'2000-01-01T00:00:00Z'",
#  "limit":25
#}

#------------------------------------------------
query listFiles(
  $query: String!
  $sortKey: FileSortKeys!
  $limit: Int!
  $cursor: String
) {
  files(
    first: $limit
    query: $query
    sortKey: $sortKey
    after: $cursor
    reverse: false
  ) {
    nodes {
      ... on MediaImage {
        id
        mimeType
        status
        updatedAt
        image {
          url
        }
        originalSource {
          fileSize
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
#{
#  "query": "updated_at:>'2000-01-01T00:00:00Z' media_type:IMAGE",
#  "sortKey":"UPDATED_AT",
#  "limit": 10,
#  "cursor": "eyJsYXN0X2lkIjozMzc1MTU5ODEwNDcyOSwibGFzdF92YWx1ZSI6IjIwMjUtMDYtMjggMjA6NTE6MDIuMTUyODYzIn0="
#}

#------------------------------------------------
query findMasterImage($query: String!, $cursor: String) {
  files(first: 250, query: $query, sortKey: FILENAME, after: $cursor) {
    nodes {
      ... on MediaImage {
        id
        status
        image {
          url
        }
      }
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }
}
#{
#  "query": "filename:'bike.jpg' media_type:IMAGE original_upload_size:851474"
#}

#------------------------------------------------
query findImages(
  $query: String!
  $sortKey: FileSortKeys!
  $limit: Int!
  $cursor: String
) {
  files(first: $limit, query: $query, sortKey: $sortKey, after: $cursor) {
    nodes {
      ... on MediaImage {
        id
        status
        mimeType
        image {
          width
          height
          url
          thumbhash
        }
        originalSource {
          fileSize
          url
        }
      }
    }
    pageInfo {
      endCursor
    }
  }
}
#{
#  "query": "filename:'bike' media_type:IMAGE",
#  "sortKey":"FILENAME",
#  "limit": 100,
#  "cursor": "eyJsYXN0X2lkIjozMzUwNDU1ODE1Mzg4MSwibGFzdF92YWx1ZSI6MTc0ODcyMjg1NDAwMH0="
#}

#------------------------------------------------
query getProductVars($id: ID!, $limit: Int!, $cursor: String) {
  product(id: $id) {
    id
    hasOnlyDefaultVariant
    variants(first: $limit, after: $cursor, sortKey: ID) {
      nodes {
        id
        media(first: 1) {
          nodes {
            id
          }
        }
      }
      pageInfo {
        endCursor
        hasNextPage
      }
    }
  }
}
#{
#  "id": "gid://shopify/Product/6945980285081",
#  "limit":200,
#  "cursor": ""
#}

#------------------------------------------------
mutation relinkProduct(
  $productId: ID!
  $fileInput: [FileUpdateInput!]!
  $moveInput: [MoveInput!]!
  $attachMedia: [ProductVariantAppendMediaInput!]!
) {
  linkFiles: fileUpdate(files: $fileInput) {
    userErrors {
      message
    }
  }
  productReorderMedia(id: $productId, moves: $moveInput) {
    mediaUserErrors {
      message
    }
  }
  attach: productVariantAppendMedia(
    productId: $productId
    variantMedia: $attachMedia
  ) {
    userErrors {
      message
    }
  }
}

#------------------------------------------------
mutation fileArchive($files: [FileUpdateInput!]!) {
  fileUpdate(files: $files) {
    files {
      id
      alt
      fileStatus
    }
    userErrors {
      message
    }
  }
}
#{
#  "files": [
#    {
#      "id": "gid://shopify/MediaImage/999999",
#      "filename": "archived_image.jpg",
#      "originalSource": "https://pub-28fff66f18164dcc9cbaa6deb828ec94.r2.dev/archived.jpg"
#    }
#  ]
#}

#------------------------------------------------
mutation fileDelete($fileIds: [ID!]!) {
  fileDelete(fileIds: $fileIds) {
    deletedFileIds
  }
}
#{
#  "fileIds": [
#    "gid://shopify/MediaImage/1072273763"
#  ]
#}

#########################################################################
#------ findProductImages: Find product images by query

query findProductImages($query: String!, $limit: Int!) {
  products(query: $query, first: $limit, sortKey: ID, reverse: false) {
    nodes {
      id
      title
      hasOnlyDefaultVariant
      media(first: 250, query: "media_type:IMAGE") {
        nodes {
          ... on MediaImage {
            id
            status
            mimeType
            image {
              width
              height
              url
            }
            originalSource {
              fileSize
              url
            }
          }
        }
      }
    }
  }
}
#{
#  "query": "id:>1",
#  "limit": 25
#}

#---------------------------------------------

query findImageById($id: ID!) {
  node(id: $id) {
    ... on MediaImage {
      id
      status
      mimeType
      image {
        width
        height
        url
        thumbhash
      }
      originalSource {
        url
        fileSize
      }
    }
  }
}
#{
#  "id": "gid://shopify/MediaImage/33210382090393"
#}

#########################################
################################################

#------ callGetFileByNameFirst: Get media url by name

query callGetFileByNameFirst($query: String!) {
  files(first: 50, query: $query, sortKey: FILENAME) {
    nodes {
      ... on MediaImage {
        id
        image {
          url
        }
      }
    }
    pageInfo {
      endCursor
    }
  }
}
#{
#  "query": "filename:'test.jpg' media_type:IMAGE"
#}

query callGetFileByNameNext($query: String!, $cursor: String) {
  files(first: 50, query: $query, sortKey: FILENAME, after: $cursor) {
    nodes {
      ... on MediaImage {
        id
        image {
          url
        }
      }
    }
    pageInfo {
      endCursor
    }
  }
}
#{
#  "query": "filename:'test.jpg' media_type:IMAGE",
#	"cursor": ""
#}
