// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"context"
	"database/sql"
	"fmt"
)

type DBTX interface {
	ExecContext(context.Context, string, ...interface{}) (sql.Result, error)
	PrepareContext(context.Context, string) (*sql.Stmt, error)
	QueryContext(context.Context, string, ...interface{}) (*sql.Rows, error)
	QueryRowContext(context.Context, string, ...interface{}) *sql.Row
}

func New(db DBTX) *Queries {
	return &Queries{db: db}
}

func Prepare(ctx context.Context, db DBTX) (*Queries, error) {
	q := Queries{db: db}
	var err error
	if q.canUndoConfirmStmt, err = db.PrepareContext(ctx, canUndoConfirm); err != nil {
		return nil, fmt.Errorf("error preparing query CanUndoConfirm: %w", err)
	}
	if q.createAccountStmt, err = db.PrepareContext(ctx, createAccount); err != nil {
		return nil, fmt.Errorf("error preparing query CreateAccount: %w", err)
	}
	if q.createBackupStmt, err = db.PrepareContext(ctx, createBackup); err != nil {
		return nil, fmt.Errorf("error preparing query CreateBackup: %w", err)
	}
	if q.createBlockStmt, err = db.PrepareContext(ctx, createBlock); err != nil {
		return nil, fmt.Errorf("error preparing query CreateBlock: %w", err)
	}
	if q.createDoneImageStmt, err = db.PrepareContext(ctx, createDoneImage); err != nil {
		return nil, fmt.Errorf("error preparing query CreateDoneImage: %w", err)
	}
	if q.createDoneProductStmt, err = db.PrepareContext(ctx, createDoneProduct); err != nil {
		return nil, fmt.Errorf("error preparing query CreateDoneProduct: %w", err)
	}
	if q.createDoneRemovedStmt, err = db.PrepareContext(ctx, createDoneRemoved); err != nil {
		return nil, fmt.Errorf("error preparing query CreateDoneRemoved: %w", err)
	}
	if q.createDoneVariantStmt, err = db.PrepareContext(ctx, createDoneVariant); err != nil {
		return nil, fmt.Errorf("error preparing query CreateDoneVariant: %w", err)
	}
	if q.createImageStmt, err = db.PrepareContext(ctx, createImage); err != nil {
		return nil, fmt.Errorf("error preparing query CreateImage: %w", err)
	}
	if q.createProductStmt, err = db.PrepareContext(ctx, createProduct); err != nil {
		return nil, fmt.Errorf("error preparing query CreateProduct: %w", err)
	}
	if q.createProductImageStmt, err = db.PrepareContext(ctx, createProductImage); err != nil {
		return nil, fmt.Errorf("error preparing query CreateProductImage: %w", err)
	}
	if q.createScanStmt, err = db.PrepareContext(ctx, createScan); err != nil {
		return nil, fmt.Errorf("error preparing query CreateScan: %w", err)
	}
	if q.deleteImageStmt, err = db.PrepareContext(ctx, deleteImage); err != nil {
		return nil, fmt.Errorf("error preparing query DeleteImage: %w", err)
	}
	if q.deleteOldProductImagesStmt, err = db.PrepareContext(ctx, deleteOldProductImages); err != nil {
		return nil, fmt.Errorf("error preparing query DeleteOldProductImages: %w", err)
	}
	if q.deleteProductImageStmt, err = db.PrepareContext(ctx, deleteProductImage); err != nil {
		return nil, fmt.Errorf("error preparing query DeleteProductImage: %w", err)
	}
	if q.getAccountStmt, err = db.PrepareContext(ctx, getAccount); err != nil {
		return nil, fmt.Errorf("error preparing query GetAccount: %w", err)
	}
	if q.getBackupStmt, err = db.PrepareContext(ctx, getBackup); err != nil {
		return nil, fmt.Errorf("error preparing query GetBackup: %w", err)
	}
	if q.getDoneProductsStmt, err = db.PrepareContext(ctx, getDoneProducts); err != nil {
		return nil, fmt.Errorf("error preparing query GetDoneProducts: %w", err)
	}
	if q.getDoneRemovesStmt, err = db.PrepareContext(ctx, getDoneRemoves); err != nil {
		return nil, fmt.Errorf("error preparing query GetDoneRemoves: %w", err)
	}
	if q.getImageStmt, err = db.PrepareContext(ctx, getImage); err != nil {
		return nil, fmt.Errorf("error preparing query GetImage: %w", err)
	}
	if q.getImageMasterStmt, err = db.PrepareContext(ctx, getImageMaster); err != nil {
		return nil, fmt.Errorf("error preparing query GetImageMaster: %w", err)
	}
	if q.getScanStmt, err = db.PrepareContext(ctx, getScan); err != nil {
		return nil, fmt.Errorf("error preparing query GetScan: %w", err)
	}
	if q.imageToProcessStmt, err = db.PrepareContext(ctx, imageToProcess); err != nil {
		return nil, fmt.Errorf("error preparing query ImageToProcess: %w", err)
	}
	if q.imagesPendingStmt, err = db.PrepareContext(ctx, imagesPending); err != nil {
		return nil, fmt.Errorf("error preparing query ImagesPending: %w", err)
	}
	if q.isBlockedStmt, err = db.PrepareContext(ctx, isBlocked); err != nil {
		return nil, fmt.Errorf("error preparing query IsBlocked: %w", err)
	}
	if q.undoConfirmStmt, err = db.PrepareContext(ctx, undoConfirm); err != nil {
		return nil, fmt.Errorf("error preparing query UndoConfirm: %w", err)
	}
	if q.updateImageStatusStmt, err = db.PrepareContext(ctx, updateImageStatus); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateImageStatus: %w", err)
	}
	if q.updateImageTsStmt, err = db.PrepareContext(ctx, updateImageTs); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateImageTs: %w", err)
	}
	if q.updateProductTsStmt, err = db.PrepareContext(ctx, updateProductTs); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateProductTs: %w", err)
	}
	if q.updateScanFilesStmt, err = db.PrepareContext(ctx, updateScanFiles); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateScanFiles: %w", err)
	}
	if q.updateScanProductsStmt, err = db.PrepareContext(ctx, updateScanProducts); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateScanProducts: %w", err)
	}
	if q.updateScanStatusStmt, err = db.PrepareContext(ctx, updateScanStatus); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateScanStatus: %w", err)
	}
	return &q, nil
}

func (q *Queries) Close() error {
	var err error
	if q.canUndoConfirmStmt != nil {
		if cerr := q.canUndoConfirmStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing canUndoConfirmStmt: %w", cerr)
		}
	}
	if q.createAccountStmt != nil {
		if cerr := q.createAccountStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createAccountStmt: %w", cerr)
		}
	}
	if q.createBackupStmt != nil {
		if cerr := q.createBackupStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createBackupStmt: %w", cerr)
		}
	}
	if q.createBlockStmt != nil {
		if cerr := q.createBlockStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createBlockStmt: %w", cerr)
		}
	}
	if q.createDoneImageStmt != nil {
		if cerr := q.createDoneImageStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createDoneImageStmt: %w", cerr)
		}
	}
	if q.createDoneProductStmt != nil {
		if cerr := q.createDoneProductStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createDoneProductStmt: %w", cerr)
		}
	}
	if q.createDoneRemovedStmt != nil {
		if cerr := q.createDoneRemovedStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createDoneRemovedStmt: %w", cerr)
		}
	}
	if q.createDoneVariantStmt != nil {
		if cerr := q.createDoneVariantStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createDoneVariantStmt: %w", cerr)
		}
	}
	if q.createImageStmt != nil {
		if cerr := q.createImageStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createImageStmt: %w", cerr)
		}
	}
	if q.createProductStmt != nil {
		if cerr := q.createProductStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createProductStmt: %w", cerr)
		}
	}
	if q.createProductImageStmt != nil {
		if cerr := q.createProductImageStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createProductImageStmt: %w", cerr)
		}
	}
	if q.createScanStmt != nil {
		if cerr := q.createScanStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createScanStmt: %w", cerr)
		}
	}
	if q.deleteImageStmt != nil {
		if cerr := q.deleteImageStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing deleteImageStmt: %w", cerr)
		}
	}
	if q.deleteOldProductImagesStmt != nil {
		if cerr := q.deleteOldProductImagesStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing deleteOldProductImagesStmt: %w", cerr)
		}
	}
	if q.deleteProductImageStmt != nil {
		if cerr := q.deleteProductImageStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing deleteProductImageStmt: %w", cerr)
		}
	}
	if q.getAccountStmt != nil {
		if cerr := q.getAccountStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getAccountStmt: %w", cerr)
		}
	}
	if q.getBackupStmt != nil {
		if cerr := q.getBackupStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getBackupStmt: %w", cerr)
		}
	}
	if q.getDoneProductsStmt != nil {
		if cerr := q.getDoneProductsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getDoneProductsStmt: %w", cerr)
		}
	}
	if q.getDoneRemovesStmt != nil {
		if cerr := q.getDoneRemovesStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getDoneRemovesStmt: %w", cerr)
		}
	}
	if q.getImageStmt != nil {
		if cerr := q.getImageStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getImageStmt: %w", cerr)
		}
	}
	if q.getImageMasterStmt != nil {
		if cerr := q.getImageMasterStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getImageMasterStmt: %w", cerr)
		}
	}
	if q.getScanStmt != nil {
		if cerr := q.getScanStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getScanStmt: %w", cerr)
		}
	}
	if q.imageToProcessStmt != nil {
		if cerr := q.imageToProcessStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing imageToProcessStmt: %w", cerr)
		}
	}
	if q.imagesPendingStmt != nil {
		if cerr := q.imagesPendingStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing imagesPendingStmt: %w", cerr)
		}
	}
	if q.isBlockedStmt != nil {
		if cerr := q.isBlockedStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing isBlockedStmt: %w", cerr)
		}
	}
	if q.undoConfirmStmt != nil {
		if cerr := q.undoConfirmStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing undoConfirmStmt: %w", cerr)
		}
	}
	if q.updateImageStatusStmt != nil {
		if cerr := q.updateImageStatusStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateImageStatusStmt: %w", cerr)
		}
	}
	if q.updateImageTsStmt != nil {
		if cerr := q.updateImageTsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateImageTsStmt: %w", cerr)
		}
	}
	if q.updateProductTsStmt != nil {
		if cerr := q.updateProductTsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateProductTsStmt: %w", cerr)
		}
	}
	if q.updateScanFilesStmt != nil {
		if cerr := q.updateScanFilesStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateScanFilesStmt: %w", cerr)
		}
	}
	if q.updateScanProductsStmt != nil {
		if cerr := q.updateScanProductsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateScanProductsStmt: %w", cerr)
		}
	}
	if q.updateScanStatusStmt != nil {
		if cerr := q.updateScanStatusStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateScanStatusStmt: %w", cerr)
		}
	}
	return err
}

func (q *Queries) exec(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) (sql.Result, error) {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).ExecContext(ctx, args...)
	case stmt != nil:
		return stmt.ExecContext(ctx, args...)
	default:
		return q.db.ExecContext(ctx, query, args...)
	}
}

func (q *Queries) query(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) (*sql.Rows, error) {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).QueryContext(ctx, args...)
	case stmt != nil:
		return stmt.QueryContext(ctx, args...)
	default:
		return q.db.QueryContext(ctx, query, args...)
	}
}

func (q *Queries) queryRow(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) *sql.Row {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).QueryRowContext(ctx, args...)
	case stmt != nil:
		return stmt.QueryRowContext(ctx, args...)
	default:
		return q.db.QueryRowContext(ctx, query, args...)
	}
}

type Queries struct {
	db                         DBTX
	tx                         *sql.Tx
	canUndoConfirmStmt         *sql.Stmt
	createAccountStmt          *sql.Stmt
	createBackupStmt           *sql.Stmt
	createBlockStmt            *sql.Stmt
	createDoneImageStmt        *sql.Stmt
	createDoneProductStmt      *sql.Stmt
	createDoneRemovedStmt      *sql.Stmt
	createDoneVariantStmt      *sql.Stmt
	createImageStmt            *sql.Stmt
	createProductStmt          *sql.Stmt
	createProductImageStmt     *sql.Stmt
	createScanStmt             *sql.Stmt
	deleteImageStmt            *sql.Stmt
	deleteOldProductImagesStmt *sql.Stmt
	deleteProductImageStmt     *sql.Stmt
	getAccountStmt             *sql.Stmt
	getBackupStmt              *sql.Stmt
	getDoneProductsStmt        *sql.Stmt
	getDoneRemovesStmt         *sql.Stmt
	getImageStmt               *sql.Stmt
	getImageMasterStmt         *sql.Stmt
	getScanStmt                *sql.Stmt
	imageToProcessStmt         *sql.Stmt
	imagesPendingStmt          *sql.Stmt
	isBlockedStmt              *sql.Stmt
	undoConfirmStmt            *sql.Stmt
	updateImageStatusStmt      *sql.Stmt
	updateImageTsStmt          *sql.Stmt
	updateProductTsStmt        *sql.Stmt
	updateScanFilesStmt        *sql.Stmt
	updateScanProductsStmt     *sql.Stmt
	updateScanStatusStmt       *sql.Stmt
}

func (q *Queries) WithTx(tx *sql.Tx) *Queries {
	return &Queries{
		db:                         tx,
		tx:                         tx,
		canUndoConfirmStmt:         q.canUndoConfirmStmt,
		createAccountStmt:          q.createAccountStmt,
		createBackupStmt:           q.createBackupStmt,
		createBlockStmt:            q.createBlockStmt,
		createDoneImageStmt:        q.createDoneImageStmt,
		createDoneProductStmt:      q.createDoneProductStmt,
		createDoneRemovedStmt:      q.createDoneRemovedStmt,
		createDoneVariantStmt:      q.createDoneVariantStmt,
		createImageStmt:            q.createImageStmt,
		createProductStmt:          q.createProductStmt,
		createProductImageStmt:     q.createProductImageStmt,
		createScanStmt:             q.createScanStmt,
		deleteImageStmt:            q.deleteImageStmt,
		deleteOldProductImagesStmt: q.deleteOldProductImagesStmt,
		deleteProductImageStmt:     q.deleteProductImageStmt,
		getAccountStmt:             q.getAccountStmt,
		getBackupStmt:              q.getBackupStmt,
		getDoneProductsStmt:        q.getDoneProductsStmt,
		getDoneRemovesStmt:         q.getDoneRemovesStmt,
		getImageStmt:               q.getImageStmt,
		getImageMasterStmt:         q.getImageMasterStmt,
		getScanStmt:                q.getScanStmt,
		imageToProcessStmt:         q.imageToProcessStmt,
		imagesPendingStmt:          q.imagesPendingStmt,
		isBlockedStmt:              q.isBlockedStmt,
		undoConfirmStmt:            q.undoConfirmStmt,
		updateImageStatusStmt:      q.updateImageStatusStmt,
		updateImageTsStmt:          q.updateImageTsStmt,
		updateProductTsStmt:        q.updateProductTsStmt,
		updateScanFilesStmt:        q.updateScanFilesStmt,
		updateScanProductsStmt:     q.updateScanProductsStmt,
		updateScanStatusStmt:       q.updateScanStatusStmt,
	}
}
