<script lang="ts">
	import {
		type Duplicate,
		postConfirmImages,
		getImagesPending,
		postConfirmImagesUndo
	} from '$lib/api';
	import { onMount } from 'svelte';

	const CONFIRM_STATUS_NONE = 0;
	const CONFIRM_STATUS_APPROVE = 1;
	const CONFIRM_STATUS_SKIP = 2;

	// let { data }: PageProps = $props();
	// let images = $state<Duplicate[]>(data?.duplicates ?? []);
	let images = $state<Duplicate[] | null>(null);
	let canUndo = $state(false);
	//let imageCount = $state<ImageCount>({} as ImageCount);

	let allChecked = $state(false);
	let allIndeterminate = $state(false);
	let confirmStatus = $state(CONFIRM_STATUS_NONE);
	let countSelected = $state(0);

	function onSelectionChange(image: Duplicate) {
		if (!images) {
			return;
		}
		image.selected = !image.selected;
		let count = 0;
		images.forEach((image) => {
			if (image.selected) {
				count++;
			}
		});

		let allSelected = count == images.length;
		let allUnselected = count == 0;
		allChecked = allSelected;
		allIndeterminate = !allUnselected && !allSelected;
		countSelected = count;
	}

	function onAllChanged() {
		if (!images) {
			return;
		}
		allChecked = !allChecked;
		allIndeterminate = false;
		images.forEach((image) => {
			image.selected = allChecked;
		});
		if (allChecked) {
			countSelected = images.length;
		} else {
			countSelected = 0;
		}
	}

	async function reloadData() {
		console.log('reloadData');
		images = null;
		const response = await getImagesPending();
		images = response.duplicates;
		canUndo = response.canUndo;
		// images = (await getImagesPending()).duplicates;
		// console.log('images=' + JSON.stringify(images));
		//console.log('imageCount=' + JSON.stringify(imageCount));
	}

	function showToast(message: string, isError: boolean = false) {
		if (shopify) {
			shopify.toast.show(message, { isError });
		}
	}

	async function confirmImages(approve: boolean) {
		if (!images) {
			return;
		}
		const ids = images.filter((image) => image.selected).map((image) => image.dupId);
		if (ids.length == 0) {
			return;
		}

		confirmStatus = approve ? CONFIRM_STATUS_APPROVE : CONFIRM_STATUS_SKIP;

		console.log('confirmImages, approved=' + approve + ', ids=' + JSON.stringify(ids));
		await postConfirmImages(approve, ids);
		showToast(approve ? 'Will merge duplicates' : 'Duplicates will not be merged');

		const keepImages = images.filter((image) => !image.selected);
		allChecked = false;
		allIndeterminate = false;
		images = keepImages;
		if (keepImages.length == 0) {
			images = null;
			reloadData();
		}
		confirmStatus = CONFIRM_STATUS_NONE;
	}

	async function confirmImagesUndo() {
		await postConfirmImagesUndo();
		showToast('Undo confirmed');
		reloadData();
	}

	onMount(async () => {
		reloadData();
	});
</script>

<ui-title-bar title="Confirm duplicates">
	<button variant="breadcrumb">Home</button>
	<!-- {#if imageCount.approved && imageCount.blocked && imageCount.approved + imageCount.blocked > 0}
		<button variant="secondary">Undo</button>
	{/if} -->
	<!-- {#if imageCount.blocked && imageCount.blocked > 0}
		<button variant="secondary">Undo keep: {imageCount.blocked}</button>
	{/if} -->

	<button
		variant="secondary"
		loading={confirmStatus == CONFIRM_STATUS_SKIP ? '' : undefined}
		disabled={confirmStatus !== CONFIRM_STATUS_NONE}
		onclick={() => confirmImages(false)}
		>Cancel NNN pending
	</button>

	<button variant="secondary" loading={images == null ? '' : undefined} onclick={reloadData}>
		Refresh
	</button>

	<!-- <button
		variant="secondary"
		loading={confirmStatus == CONFIRM_STATUS_SKIP ? '' : undefined}
		disabled={confirmStatus !== CONFIRM_STATUS_NONE}
		onclick={() => confirmImages(false)}>Keep duplicates</button
	>
	<button
		variant="primary"
		tone="critical"
		loading={confirmStatus == CONFIRM_STATUS_APPROVE ? '' : undefined}
		disabled={confirmStatus !== CONFIRM_STATUS_NONE}
		onclick={() => confirmImages(true)}>Merge duplicates</button
	> -->
</ui-title-bar>

<s-page>
	<!-- <s-section>
		<s-button variant="secondary">Undo approved: {imageCount.approved}</s-button>
		<s-button variant="secondary">Undo blocked: {imageCount.blocked}</s-button>
	</s-section> -->

	<s-section>
		<s-stack direction="inline" gap="base" alignItems="center" justifyContent="space-between">
			<s-stack direction="inline" gap="base" alignItems="center" justifyContent="start">
				{#if canUndo}
					<s-button variant="secondary" onclick={confirmImagesUndo}>Undo</s-button>
				{/if}
			</s-stack>
			<s-stack direction="inline" gap="base" alignItems="center" justifyContent="end">
				<s-button
					variant="secondary"
					loading={confirmStatus == CONFIRM_STATUS_SKIP ? '' : undefined}
					disabled={confirmStatus !== CONFIRM_STATUS_NONE || countSelected == 0}
					onclick={() => confirmImages(false)}>Keep duplicates</s-button
				>
				<s-button
					variant="primary"
					tone="critical"
					loading={confirmStatus == CONFIRM_STATUS_APPROVE ? '' : undefined}
					disabled={confirmStatus !== CONFIRM_STATUS_NONE || countSelected == 0}
					onclick={() => confirmImages(true)}>Merge duplicates</s-button
				>
			</s-stack>
		</s-stack>
	</s-section>

	<!-- {#if images && images.length > 0}
		<s-section padding="none">
			<s-box background="subdued">
				<s-text>Hello</s-text>
			</s-box>
			<s-grid
				gridTemplateColumns="min-content min-content 1fr 1fr"
				gap="none"
				columnGap="base"
				justifyContent="center"
			>
				{#each images as image (image.dupId)}
					<s-grid-item>
						<s-checkbox checked={image.selected} onchange={() => onSelectionChange(image)}
						></s-checkbox>
					</s-grid-item>
					<s-grid-item>
						<img src={image.url} alt={image.name} class="thumb" />
					</s-grid-item>
					<s-grid-item>
						<s-stack direction="block" gap="none" alignItems="start" justifyContent="center">
							<s-link target="_blank" href="shopify://admin/content/files/{image.masterId}">
								{image.name}
							</s-link>
							<s-link
								target="_blank"
								href="shopify://admin/content/files/{image.dupId}"
								stopPropagation={true}
							>
								{image.dupUuid}
							</s-link>
						</s-stack>
					</s-grid-item>
					<s-grid-itemm>
						<s-link target="_blank" href="shopify://admin/products/{image.productId}">
							{image.productTitle}
						</s-link>
					</s-grid-itemm>
				{/each}
			</s-grid>
		</s-section>
	{/if} -->

	{#if !images}
		<s-stack direction="inline" gap="base" alignItems="center" justifyContent="center">
			<s-text color="subdued">Loading...</s-text>
		</s-stack>
	{:else if images.length == 0}
		<s-stack direction="block" gap="base" alignItems="center" justifyContent="center">
			<s-text>No images to confirm</s-text>
		</s-stack>
	{:else}
		<s-section padding="none">
			<s-table variant="table">
				<s-table-header-row>
					<s-table-header listSlot="primary">
						<s-checkbox
							indeterminate={allIndeterminate}
							checked={allChecked}
							onchange={onAllChanged}
						></s-checkbox>
					</s-table-header>
					<!-- {#if countSelected > 0}
						<s-table-header></s-table-header>
						<s-table-header>
							<s-button
								variant="secondary"
								loading={confirmStatus == CONFIRM_STATUS_SKIP ? '' : undefined}
								disabled={confirmStatus !== CONFIRM_STATUS_NONE}
								onclick={() => confirmImages(false)}>Keep duplicates</s-button
							>
							<s-button
								variant="primary"
								tone="critical"
								loading={confirmStatus == CONFIRM_STATUS_APPROVE ? '' : undefined}
								disabled={confirmStatus !== CONFIRM_STATUS_NONE}
								onclick={() => confirmImages(true)}>Merge duplicates</s-button
							>
						</s-table-header>
					{:else} -->
					<s-table-header>
						<s-grid gridTemplateColumns="minmax(0, 1fr) auto">
							<s-table-header>File</s-table-header>
							<s-stack direction="inline" gap="small"> </s-stack>
						</s-grid>
					</s-table-header>
					<s-table-header>Product</s-table-header>
					<!-- {/if} -->
				</s-table-header-row>
				<s-table-body>
					{#each images as image (image.dupId)}
						<s-table-row>
							<s-table-cell>
								<s-stack direction="inline" gap="base" alignItems="center" justifyContent="start">
									<s-checkbox checked={image.selected} onchange={() => onSelectionChange(image)}
									></s-checkbox>
									<img src={image.url} alt={image.name} class="thumb" />
								</s-stack>
							</s-table-cell>
							<s-table-cell>
								<s-stack direction="block" gap="none" alignItems="start" justifyContent="center">
									<s-link target="_blank" href="shopify://admin/content/files/{image.masterId}">
										{image.name}
									</s-link>
									<s-link
										target="_blank"
										href="shopify://admin/content/files/{image.dupId}"
										stopPropagation={true}
									>
										{image.dupUuid}
									</s-link>
								</s-stack>
							</s-table-cell>
							<s-table-cell>
								<s-link target="_blank" href="shopify://admin/products/{image.productId}">
									{image.productTitle}
								</s-link>
							</s-table-cell>
						</s-table-row>
					{/each}
				</s-table-body>
			</s-table>
		</s-section>
	{/if}
</s-page>

<style>
	.thumb {
		border: 1px solid #adb5bd;
		object-fit: contain;
		height: 60px;
		max-width: 90px;
		border-radius: 6%;
	}
</style>
