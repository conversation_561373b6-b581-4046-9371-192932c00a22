// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: query.sql

package db

import (
	"context"
	"database/sql"
)

const canUndoConfirm = `-- name: CanUndoConfirm :one


select count(*) from image where
    acc_id = ? and
    uuid is not null and
    (status = 1 or (status = 2 and updated > ?)) limit 1
`

type CanUndoConfirmParams struct {
	AccID   int64
	Updated int64
}

// -- name: CountImagesByStatus :one
// select count(*) from image where acc_id = ? and status = ? limit ?;
// -- name: CountImagesByStatus :many
// select status, count(status) from image where acc_id=? and uuid is not null group by status;
func (q *Queries) CanUndoConfirm(ctx context.Context, arg *CanUndoConfirmParams) (int64, error) {
	row := q.queryRow(ctx, q.canUndoConfirmStmt, canUndoConfirm, arg.AccID, arg.Updated)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createAccount = `-- name: CreateAccount :exec
INSERT OR REPLACE INTO account (id, shop, token, created)
VALUES (?, ?, ?, ?)
`

type CreateAccountParams struct {
	ID      int64
	Shop    string
	Token   string
	Created int64
}

func (q *Queries) CreateAccount(ctx context.Context, arg *CreateAccountParams) error {
	_, err := q.exec(ctx, q.createAccountStmt, createAccount,
		arg.ID,
		arg.Shop,
		arg.Token,
		arg.Created,
	)
	return err
}

const createBackup = `-- name: CreateBackup :exec



insert or replace into backup (acc_id, checksum, size, mime, updated)
values (?, ?, ?, ?, ?)
`

type CreateBackupParams struct {
	AccID    int64
	Checksum string
	Size     int64
	Mime     string
	Updated  int64
}

// -- name: CreateRemoved :exec
// insert or replace into removed (acc_id, dup_id, master_id, name, dup_uuid, url, checksum, size, created)
// values (?, ?, ?, ?, ?, ?, ?, ?, ?);
// -- name: CreateUnlinked :exec
// insert or replace into unlinked (acc_id, dup_id, master_id, product_id, title, position, created)
// values (?, ?, ?, ?, ?, ?, ?);
// -- name: CreateUnlinkedVar :exec
// insert or replace into unlinked_var (dup_id, product_id, variant_id)
// values (?, ?, ?);
func (q *Queries) CreateBackup(ctx context.Context, arg *CreateBackupParams) error {
	_, err := q.exec(ctx, q.createBackupStmt, createBackup,
		arg.AccID,
		arg.Checksum,
		arg.Size,
		arg.Mime,
		arg.Updated,
	)
	return err
}

const createBlock = `-- name: CreateBlock :exec

insert or replace into block (acc_id, id, updated)
values (?, ?, ?)
`

type CreateBlockParams struct {
	AccID   int64
	ID      int64
	Updated int64
}

// ---- block ------
func (q *Queries) CreateBlock(ctx context.Context, arg *CreateBlockParams) error {
	_, err := q.exec(ctx, q.createBlockStmt, createBlock, arg.AccID, arg.ID, arg.Updated)
	return err
}

const createDoneImage = `-- name: CreateDoneImage :exec

insert or replace into done_image (acc_id, dup_id, master_id, name, dup_uuid, checksum, size, mime, url, updated_at)
values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`

type CreateDoneImageParams struct {
	AccID     int64
	DupID     int64
	MasterID  int64
	Name      string
	DupUuid   string
	Checksum  string
	Size      int64
	Mime      string
	Url       string
	UpdatedAt int64
}

// ---- Backup ------
func (q *Queries) CreateDoneImage(ctx context.Context, arg *CreateDoneImageParams) error {
	_, err := q.exec(ctx, q.createDoneImageStmt, createDoneImage,
		arg.AccID,
		arg.DupID,
		arg.MasterID,
		arg.Name,
		arg.DupUuid,
		arg.Checksum,
		arg.Size,
		arg.Mime,
		arg.Url,
		arg.UpdatedAt,
	)
	return err
}

const createDoneProduct = `-- name: CreateDoneProduct :exec
insert or replace into done_product (acc_id, dup_id, product_id, title, position, created_at)
values (?, ?, ?, ?, ?, ?)
`

type CreateDoneProductParams struct {
	AccID     int64
	DupID     int64
	ProductID int64
	Title     string
	Position  int64
	CreatedAt int64
}

func (q *Queries) CreateDoneProduct(ctx context.Context, arg *CreateDoneProductParams) error {
	_, err := q.exec(ctx, q.createDoneProductStmt, createDoneProduct,
		arg.AccID,
		arg.DupID,
		arg.ProductID,
		arg.Title,
		arg.Position,
		arg.CreatedAt,
	)
	return err
}

const createDoneRemoved = `-- name: CreateDoneRemoved :exec
insert or replace into done_removed (acc_id, dup_id, archived, created_at)
values (?, ?, ?, ?)
`

type CreateDoneRemovedParams struct {
	AccID     int64
	DupID     int64
	Archived  bool
	CreatedAt int64
}

func (q *Queries) CreateDoneRemoved(ctx context.Context, arg *CreateDoneRemovedParams) error {
	_, err := q.exec(ctx, q.createDoneRemovedStmt, createDoneRemoved,
		arg.AccID,
		arg.DupID,
		arg.Archived,
		arg.CreatedAt,
	)
	return err
}

const createDoneVariant = `-- name: CreateDoneVariant :exec
insert or replace into done_variant (acc_id, dup_id, product_id, variant_id, created_at)
values (?, ?, ?, ?, ?)
`

type CreateDoneVariantParams struct {
	AccID     int64
	DupID     int64
	ProductID int64
	VariantID int64
	CreatedAt int64
}

func (q *Queries) CreateDoneVariant(ctx context.Context, arg *CreateDoneVariantParams) error {
	_, err := q.exec(ctx, q.createDoneVariantStmt, createDoneVariant,
		arg.AccID,
		arg.DupID,
		arg.ProductID,
		arg.VariantID,
		arg.CreatedAt,
	)
	return err
}

const createImage = `-- name: CreateImage :exec

insert or replace into image (acc_id, id, name, mime, url, size, checksum, uuid, status, updated)
values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`

type CreateImageParams struct {
	AccID    int64
	ID       int64
	Name     string
	Mime     string
	Url      string
	Size     int64
	Checksum string
	Uuid     sql.NullString
	Status   int64
	Updated  int64
}

// ---- image ------
func (q *Queries) CreateImage(ctx context.Context, arg *CreateImageParams) error {
	_, err := q.exec(ctx, q.createImageStmt, createImage,
		arg.AccID,
		arg.ID,
		arg.Name,
		arg.Mime,
		arg.Url,
		arg.Size,
		arg.Checksum,
		arg.Uuid,
		arg.Status,
		arg.Updated,
	)
	return err
}

const createProduct = `-- name: CreateProduct :exec
insert or replace into product (id, acc_id, title, updated)
values (?, ?, ?, ?)
`

type CreateProductParams struct {
	ID      int64
	AccID   int64
	Title   string
	Updated int64
}

func (q *Queries) CreateProduct(ctx context.Context, arg *CreateProductParams) error {
	_, err := q.exec(ctx, q.createProductStmt, createProduct,
		arg.ID,
		arg.AccID,
		arg.Title,
		arg.Updated,
	)
	return err
}

const createProductImage = `-- name: CreateProductImage :exec
insert or replace into product_image (product_id, image_id, acc_id, updated)
values (?, ?, ?, ?)
`

type CreateProductImageParams struct {
	ProductID int64
	ImageID   int64
	AccID     int64
	Updated   int64
}

func (q *Queries) CreateProductImage(ctx context.Context, arg *CreateProductImageParams) error {
	_, err := q.exec(ctx, q.createProductImageStmt, createProductImage,
		arg.ProductID,
		arg.ImageID,
		arg.AccID,
		arg.Updated,
	)
	return err
}

const createScan = `-- name: CreateScan :exec
insert or replace into scan (acc_id, status, updated_file, updated_product, updated)
values (?, ?, ?, ?, ?)
`

type CreateScanParams struct {
	AccID          int64
	Status         int64
	UpdatedFile    int64
	UpdatedProduct int64
	Updated        int64
}

func (q *Queries) CreateScan(ctx context.Context, arg *CreateScanParams) error {
	_, err := q.exec(ctx, q.createScanStmt, createScan,
		arg.AccID,
		arg.Status,
		arg.UpdatedFile,
		arg.UpdatedProduct,
		arg.Updated,
	)
	return err
}

const deleteImage = `-- name: DeleteImage :exec
delete from image where id = ?
`

func (q *Queries) DeleteImage(ctx context.Context, id int64) error {
	_, err := q.exec(ctx, q.deleteImageStmt, deleteImage, id)
	return err
}

const deleteOldProductImages = `-- name: DeleteOldProductImages :exec
delete from product_image where acc_id = ? and product_id = ? and updated < ?
`

type DeleteOldProductImagesParams struct {
	AccID     int64
	ProductID int64
	Updated   int64
}

func (q *Queries) DeleteOldProductImages(ctx context.Context, arg *DeleteOldProductImagesParams) error {
	_, err := q.exec(ctx, q.deleteOldProductImagesStmt, deleteOldProductImages, arg.AccID, arg.ProductID, arg.Updated)
	return err
}

const deleteProductImage = `-- name: DeleteProductImage :exec
delete from product_image where product_id = ? and image_id = ?
`

type DeleteProductImageParams struct {
	ProductID int64
	ImageID   int64
}

func (q *Queries) DeleteProductImage(ctx context.Context, arg *DeleteProductImageParams) error {
	_, err := q.exec(ctx, q.deleteProductImageStmt, deleteProductImage, arg.ProductID, arg.ImageID)
	return err
}

const getAccount = `-- name: GetAccount :one
SELECT id, shop, token, created FROM account
WHERE id = ? LIMIT 1
`

func (q *Queries) GetAccount(ctx context.Context, id int64) (Account, error) {
	row := q.queryRow(ctx, q.getAccountStmt, getAccount, id)
	var i Account
	err := row.Scan(
		&i.ID,
		&i.Shop,
		&i.Token,
		&i.Created,
	)
	return i, err
}

const getBackup = `-- name: GetBackup :one
select acc_id, checksum, size, mime, updated from backup where acc_id = ? and checksum = ? and size = ? and mime = ? limit 1
`

type GetBackupParams struct {
	AccID    int64
	Checksum string
	Size     int64
	Mime     string
}

func (q *Queries) GetBackup(ctx context.Context, arg *GetBackupParams) (Backup, error) {
	row := q.queryRow(ctx, q.getBackupStmt, getBackup,
		arg.AccID,
		arg.Checksum,
		arg.Size,
		arg.Mime,
	)
	var i Backup
	err := row.Scan(
		&i.AccID,
		&i.Checksum,
		&i.Size,
		&i.Mime,
		&i.Updated,
	)
	return i, err
}

const getDoneProducts = `-- name: GetDoneProducts :many

select
  di.master_id, di.dup_id, di.name, di.dup_uuid, di.mime,
  di.url, dp.product_id, dp.title, dp.created_at
from done_image di join done_product dp on di.dup_id = dp.dup_id
where di.acc_id = ?
order by dp.created_at asc limit 50
`

type GetDoneProductsRow struct {
	MasterID  int64
	DupID     int64
	Name      string
	DupUuid   string
	Mime      string
	Url       string
	ProductID int64
	Title     string
	CreatedAt int64
}

// ---- done ------
func (q *Queries) GetDoneProducts(ctx context.Context, accID int64) ([]GetDoneProductsRow, error) {
	rows, err := q.query(ctx, q.getDoneProductsStmt, getDoneProducts, accID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetDoneProductsRow{}
	for rows.Next() {
		var i GetDoneProductsRow
		if err := rows.Scan(
			&i.MasterID,
			&i.DupID,
			&i.Name,
			&i.DupUuid,
			&i.Mime,
			&i.Url,
			&i.ProductID,
			&i.Title,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getDoneRemoves = `-- name: GetDoneRemoves :many
select
  di.master_id, di.dup_id, di.name, di.dup_uuid, di.mime,
  di.url, dr.archived, dr.created_at
from done_image di join done_removed dr on di.dup_id = dr.dup_id
where di.acc_id = ?
order by dr.created_at asc limit 50
`

type GetDoneRemovesRow struct {
	MasterID  int64
	DupID     int64
	Name      string
	DupUuid   string
	Mime      string
	Url       string
	Archived  bool
	CreatedAt int64
}

func (q *Queries) GetDoneRemoves(ctx context.Context, accID int64) ([]GetDoneRemovesRow, error) {
	rows, err := q.query(ctx, q.getDoneRemovesStmt, getDoneRemoves, accID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetDoneRemovesRow{}
	for rows.Next() {
		var i GetDoneRemovesRow
		if err := rows.Scan(
			&i.MasterID,
			&i.DupID,
			&i.Name,
			&i.DupUuid,
			&i.Mime,
			&i.Url,
			&i.Archived,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getImage = `-- name: GetImage :one
select acc_id, id, name, mime, url, size, checksum, uuid, status, updated from image where id = ? limit 1
`

func (q *Queries) GetImage(ctx context.Context, id int64) (Image, error) {
	row := q.queryRow(ctx, q.getImageStmt, getImage, id)
	var i Image
	err := row.Scan(
		&i.AccID,
		&i.ID,
		&i.Name,
		&i.Mime,
		&i.Url,
		&i.Size,
		&i.Checksum,
		&i.Uuid,
		&i.Status,
		&i.Updated,
	)
	return i, err
}

const getImageMaster = `-- name: GetImageMaster :one
select acc_id, id, name, mime, url, size, checksum, uuid, status, updated from image where acc_id = ? and name = ? and uuid is null limit 1
`

type GetImageMasterParams struct {
	AccID int64
	Name  string
}

func (q *Queries) GetImageMaster(ctx context.Context, arg *GetImageMasterParams) (Image, error) {
	row := q.queryRow(ctx, q.getImageMasterStmt, getImageMaster, arg.AccID, arg.Name)
	var i Image
	err := row.Scan(
		&i.AccID,
		&i.ID,
		&i.Name,
		&i.Mime,
		&i.Url,
		&i.Size,
		&i.Checksum,
		&i.Uuid,
		&i.Status,
		&i.Updated,
	)
	return i, err
}

const getScan = `-- name: GetScan :one

select acc_id, status, updated_file, updated_product, updated from scan where acc_id = ? limit 1
`

// ---- Scan ------
func (q *Queries) GetScan(ctx context.Context, accID int64) (Scan, error) {
	row := q.queryRow(ctx, q.getScanStmt, getScan, accID)
	var i Scan
	err := row.Scan(
		&i.AccID,
		&i.Status,
		&i.UpdatedFile,
		&i.UpdatedProduct,
		&i.Updated,
	)
	return i, err
}

const imageToProcess = `-- name: ImageToProcess :one
select
  m.acc_id acc_id, m.id master_id, d.id dup_id, m.name name, d.uuid dup_uuid,
  m.url, m.checksum, m.size, m.mime, p.id product_id, p.title product_title, p.updated product_updated
from image m
inner join image d
on
  m.acc_id = d.acc_id and
  m.name = d.name and
  m.checksum = d.checksum and
  m.id != d.id and
  m.uuid is null and
  d.uuid is not null
left join product_image pi on pi.image_id = d.id
left join product p on pi.product_id = p.id
where
  m.acc_id = ? and
  d.status = 1 and
  d.updated < ?
order by d.updated, p.id
limit 1
`

type ImageToProcessParams struct {
	AccID   int64
	Updated int64
}

type ImageToProcessRow struct {
	AccID          int64
	MasterID       int64
	DupID          int64
	Name           string
	DupUuid        sql.NullString
	Url            string
	Checksum       string
	Size           int64
	Mime           string
	ProductID      sql.NullInt64
	ProductTitle   sql.NullString
	ProductUpdated sql.NullInt64
}

func (q *Queries) ImageToProcess(ctx context.Context, arg *ImageToProcessParams) (ImageToProcessRow, error) {
	row := q.queryRow(ctx, q.imageToProcessStmt, imageToProcess, arg.AccID, arg.Updated)
	var i ImageToProcessRow
	err := row.Scan(
		&i.AccID,
		&i.MasterID,
		&i.DupID,
		&i.Name,
		&i.DupUuid,
		&i.Url,
		&i.Checksum,
		&i.Size,
		&i.Mime,
		&i.ProductID,
		&i.ProductTitle,
		&i.ProductUpdated,
	)
	return i, err
}

const imagesPending = `-- name: ImagesPending :many
select
  m.acc_id acc_id, m.id master_id, d.id dup_id, pi.product_id, m.name name, d.uuid dup_uuid, p.title,
  m.url, m.checksum, m.size, m.mime
from image m
inner join image d
on
  m.acc_id = d.acc_id and
  m.name = d.name and
  m.checksum = d.checksum and
  m.id != d.id and
  m.uuid is null and
  d.uuid is not null
left join product_image pi on d.id = pi.image_id
left join product p on pi.product_id = p.id
where
  m.acc_id = ? and
  d.status = ?
order by m.name, p.id desc
limit ?
`

type ImagesPendingParams struct {
	AccID  int64
	Status int64
	Limit  int64
}

type ImagesPendingRow struct {
	AccID     int64
	MasterID  int64
	DupID     int64
	ProductID sql.NullInt64
	Name      string
	DupUuid   sql.NullString
	Title     sql.NullString
	Url       string
	Checksum  string
	Size      int64
	Mime      string
}

func (q *Queries) ImagesPending(ctx context.Context, arg *ImagesPendingParams) ([]ImagesPendingRow, error) {
	rows, err := q.query(ctx, q.imagesPendingStmt, imagesPending, arg.AccID, arg.Status, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []ImagesPendingRow{}
	for rows.Next() {
		var i ImagesPendingRow
		if err := rows.Scan(
			&i.AccID,
			&i.MasterID,
			&i.DupID,
			&i.ProductID,
			&i.Name,
			&i.DupUuid,
			&i.Title,
			&i.Url,
			&i.Checksum,
			&i.Size,
			&i.Mime,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const isBlocked = `-- name: IsBlocked :one
select count(*) from block where acc_id = ? and id = ? limit 1
`

type IsBlockedParams struct {
	AccID int64
	ID    int64
}

func (q *Queries) IsBlocked(ctx context.Context, arg *IsBlockedParams) (int64, error) {
	row := q.queryRow(ctx, q.isBlockedStmt, isBlocked, arg.AccID, arg.ID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const undoConfirm = `-- name: UndoConfirm :exec
update image set status = 0, updated = ? where
    acc_id = ? and
    uuid is not null and
    (status = 1 or (status = 2 and updated > ?))
`

type UndoConfirmParams struct {
	Updated   int64
	AccID     int64
	Updated_2 int64
}

func (q *Queries) UndoConfirm(ctx context.Context, arg *UndoConfirmParams) error {
	_, err := q.exec(ctx, q.undoConfirmStmt, undoConfirm, arg.Updated, arg.AccID, arg.Updated_2)
	return err
}

const updateImageStatus = `-- name: UpdateImageStatus :exec
update image set status = ?, updated = ? where acc_id = ? and id = ?
`

type UpdateImageStatusParams struct {
	Status  int64
	Updated int64
	AccID   int64
	ID      int64
}

func (q *Queries) UpdateImageStatus(ctx context.Context, arg *UpdateImageStatusParams) error {
	_, err := q.exec(ctx, q.updateImageStatusStmt, updateImageStatus,
		arg.Status,
		arg.Updated,
		arg.AccID,
		arg.ID,
	)
	return err
}

const updateImageTs = `-- name: UpdateImageTs :exec
update image set updated = ? where id = ?
`

type UpdateImageTsParams struct {
	Updated int64
	ID      int64
}

func (q *Queries) UpdateImageTs(ctx context.Context, arg *UpdateImageTsParams) error {
	_, err := q.exec(ctx, q.updateImageTsStmt, updateImageTs, arg.Updated, arg.ID)
	return err
}

const updateProductTs = `-- name: UpdateProductTs :exec
update product set updated = ? where id = ?
`

type UpdateProductTsParams struct {
	Updated int64
	ID      int64
}

func (q *Queries) UpdateProductTs(ctx context.Context, arg *UpdateProductTsParams) error {
	_, err := q.exec(ctx, q.updateProductTsStmt, updateProductTs, arg.Updated, arg.ID)
	return err
}

const updateScanFiles = `-- name: UpdateScanFiles :exec
update scan set updated_file = ?, updated = ? where acc_id = ?
`

type UpdateScanFilesParams struct {
	UpdatedFile int64
	Updated     int64
	AccID       int64
}

func (q *Queries) UpdateScanFiles(ctx context.Context, arg *UpdateScanFilesParams) error {
	_, err := q.exec(ctx, q.updateScanFilesStmt, updateScanFiles, arg.UpdatedFile, arg.Updated, arg.AccID)
	return err
}

const updateScanProducts = `-- name: UpdateScanProducts :exec
update scan set updated_product = ?, updated = ? where acc_id = ?
`

type UpdateScanProductsParams struct {
	UpdatedProduct int64
	Updated        int64
	AccID          int64
}

func (q *Queries) UpdateScanProducts(ctx context.Context, arg *UpdateScanProductsParams) error {
	_, err := q.exec(ctx, q.updateScanProductsStmt, updateScanProducts, arg.UpdatedProduct, arg.Updated, arg.AccID)
	return err
}

const updateScanStatus = `-- name: UpdateScanStatus :exec
update scan set status = ?, updated = ? where acc_id = ?
`

type UpdateScanStatusParams struct {
	Status  int64
	Updated int64
	AccID   int64
}

func (q *Queries) UpdateScanStatus(ctx context.Context, arg *UpdateScanStatusParams) error {
	_, err := q.exec(ctx, q.updateScanStatusStmt, updateScanStatus, arg.Status, arg.Updated, arg.AccID)
	return err
}
