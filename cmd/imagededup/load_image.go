package main

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/tobimadev/imagededup/internal/db"
	"github.com/tobimadev/imagededup/internal/shop"
	slogctx "github.com/veqryn/slog-context"
)

// const (
// 	MAX_SHOPIFY_ID = *****************

// 	FILES_PER_BATCH    = 10 // 250
// 	PRODUCTS_PER_BATCH = 10 // 50

// 	NAME_STATUS_NEW     = 0
// 	NAME_STATUS_REPLACE = 1
// 	NAME_STATUS_SKIP    = 2

// 	FILE_STATUS_NEW      = 0
// 	FILE_STATUS_APPROVED = 1
// 	FILE_STATUS_BLOCKED  = 2

// 	SCAN_STATUS_RUNNING = 0
// 	SCAN_STATUS_PAUSED  = 1
// 	SCAN_STATUS_DONE    = 2
// 	SCAN_STATUS_ERROR   = 3
// )

// func (srv *server) runLoadProducts(ctx context.Context) error {
// 	acc, err := loadAccount(ctx, srv, 1589414162180424936)
// 	if err != nil {
// 		return fmt.Errorf("loadAccount: %w", err)
// 	}

// 	ticker := time.NewTicker(5 * time.Second)
// 	defer ticker.Stop()
// 	timeNextRun := time.Now().Add(7 * time.Second)

// 	for {
// 		select {
// 		case <-ctx.Done():
// 			return nil
// 		case <-ticker.C:
// 			if time.Now().Before(timeNextRun) {
// 				continue
// 			}
// 			fmt.Printf("--- tick products ---\n")
// 			if err := srv.iterateProducts(ctx, acc); err != nil {
// 				slogctx.Error(ctx, "iterateProducts", slogctx.Err(err))
// 			}
// 			timeNextRun = time.Now().Add(59 * time.Second)
// 		}
// 	}
// }

func (srv *server) runLoadImages(ctx context.Context) error {
	acc, err := loadAccount(ctx, srv, 1589414162180424936)
	if err != nil {
		return fmt.Errorf("loadAccount: %w", err)
	}

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()
	timeNextRun := time.Now().Add(3 * time.Second)

	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			if time.Now().Before(timeNextRun) {
				continue
			}
			fmt.Printf("--- tick images ---\n")
			if err := srv.iterateImages(ctx, acc); err != nil {
				slogctx.Error(ctx, "iterateImages", slogctx.Err(err))
			}
			timeNextRun = time.Now().Add(49 * time.Second)
		}
	}
}

// func (srv *server) iterateProducts(ctx context.Context, acc *db.Account) error {
// 	slogctx.Info(ctx, "iterateProducts.start", slog.Int64("accId", acc.ID))
// 	cursor := ""
// 	prevProductID := int64(-1)
// 	prevCreatedProductID := int64(-1)
// 	createdImages := 0
// 	countLoops := 0
// 	startTs := timestamp()
// 	createdProductIDs := make([]int64, 0)

// 	scan, err := srv.queries.GetScan(ctx, acc.ID)
// 	if err != nil {
// 		return fmt.Errorf("GetScan: %w", err)
// 	}

// 	updatedProduct := timestampToTime(scan.UpdatedProduct)
// 	//fmt.Printf("updatedProduct=%s\n", updatedProduct.Format(time.RFC3339))
// 	//fmt.Printf("updatedProduct=%s\n", updatedProduct.UTC().Format(time.RFC3339))

// 	query := fmt.Sprintf("updated_at:>='%s'", updatedProduct.UTC().Format(time.RFC3339))
// 	fmt.Printf("query=%s\n", query)

// 	for {
// 		countLoops++
// 		// todo: End loop if too long? Keep last productID so we can restart from there.
// 		response, err := srv.shopClient.ListProductFiles(ctx, acc, query, cursor, PRODUCTS_PER_BATCH)
// 		if err != nil {
// 			return fmt.Errorf("ListProductFiles: %w", err)
// 		}
// 		if response == nil {
// 			break
// 		}
// 		cursor = response.Cursor
// 		updatedProduct = response.LastUpdated
// 		fmt.Printf("updatedProduct=%s\n", updatedProduct.Format(time.RFC3339))
// 		createdProductIDs = createdProductIDs[:0]

// 		for i := range response.ProductFiles {
// 			product := &response.ProductFiles[i]
// 			if prevProductID != product.ProductID {
// 				prevProductID = product.ProductID
// 			}

// 			masterName, uuid := splitDuplicateNameUuid(product.Filename)
// 			if uuid == "" {
// 				continue
// 			}
// 			fmt.Printf("\n### iterateProducts: product_id=%d, image_id=%d, filename=%s, masterName=%s, uuid=%s\n",
// 				product.ProductID, product.ImageID, product.Filename, masterName, uuid)

// 			slogctx.Info(ctx, "iterateProducts.foundDuplicate", slog.Int64("accId", acc.ID),
// 				slog.Int64("productId", product.ProductID), slog.Int64("imageId", product.ImageID),
// 				slog.String("filename", product.Filename), slog.String("masterName", masterName))

// 			if product.ProductID != prevCreatedProductID {
// 				if err := srv.queries.CreateProduct(ctx, &db.CreateProductParams{
// 					ID:      product.ProductID,
// 					AccID:   acc.ID,
// 					Title:   product.ProductTitle,
// 					Updated: timestamp(),
// 				}); err != nil {
// 					return fmt.Errorf("CreateProduct: %w", err)
// 				}
// 				prevCreatedProductID = product.ProductID
// 				createdProductIDs = append(createdProductIDs, product.ProductID)
// 			}

// 			if err := srv.queries.CreateProductImage(ctx, &db.CreateProductImageParams{
// 				ProductID: product.ProductID,
// 				ImageID:   product.ImageID,
// 				AccID:     acc.ID,
// 				Updated:   timestamp(),
// 			}); err != nil {
// 				return fmt.Errorf("CreateProductImage: %w", err)
// 			}
// 			createdImages++
// 		}

// 		for _, productID := range createdProductIDs {
// 			if err := srv.queries.DeleteOldProductImages(ctx, &db.DeleteOldProductImagesParams{
// 				AccID:     acc.ID,
// 				ProductID: productID,
// 				Updated:   startTs,
// 			}); err != nil {
// 				return fmt.Errorf("DeleteOldProductImages: %w", err)
// 			}
// 		}

// 		if countLoops > 0 && countLoops%20 == 0 {
// 			srv.updateScanProducts(ctx, acc, updatedProduct.Add(1*time.Second))
// 		}
// 		if !response.HasNextPage {
// 			break
// 		}
// 	}

// 	// todo: clear updated images

// 	// if err := srv.queries.DeleteOldProducts(ctx, &db.DeleteOldProductsParams{
// 	// 	AccID:   acc.ID,
// 	// 	Updated: startTs,
// 	// }); err != nil {
// 	// 	return fmt.Errorf("DeleteOldProducts: %w", err)
// 	// }

// 	slogctx.Info(ctx, "iterateProducts.done", slog.Int64("accId", acc.ID),
// 		slog.Int("countLoops", countLoops), slog.Int("createdImages", createdImages),
// 		slog.Int64("startTs", startTs))
// 	srv.updateScanProducts(ctx, acc, updatedProduct.Add(1*time.Second))
// 	return nil
// }

func (srv *server) iterateImages(ctx context.Context, acc *db.Account) error {
	scan, err := srv.queries.GetScan(ctx, acc.ID)
	if err != nil {
		return fmt.Errorf("GetScan: %w", err)
	}

	cursor := ""
	countErrors := 0
	countLoops := 0
	createdImages := 0

	updatedFile := timestampToTime(scan.UpdatedFile)
	query := fmt.Sprintf("updated_at:>='%s' media_type:IMAGE", updatedFile.UTC().Format(time.RFC3339))

	for {
		countLoops++
		response, err := srv.shopClient.ListFiles(ctx, acc, query, shop.FileSortKeysUpdatedAt, FILES_PER_BATCH, cursor)
		if err != nil {
			return fmt.Errorf("ListFiles: %w", err)
		}
		if response == nil {
			break
		}
		cursor = response.Cursor
		updatedFile = response.LastUpdated

		for i := range response.Files {
			file := &response.Files[i]
			created, err := srv.loadDuplicate(ctx, acc, file)
			if err != nil {
				slogctx.Error(ctx, "iterate_files.loadDuplicate", slogctx.Err(err), slog.Any("file", file))
				countErrors++
			}
			if created {
				createdImages++
			}
		}

		srv.updateScanFiles(ctx, acc, updatedFile.Add(1*time.Second))
		if !response.HasNextPage {
			break
		}
	}
	slogctx.Info(ctx, "iterateFiles.done", slog.Int64("accId", acc.ID),
		slog.Int("countLoops", countLoops), slog.Int("createdImages", createdImages))
	return nil
}

// func (srv *server) loadDuplicate(ctx context.Context, acc *db.Account, duplicate *shop.File) (bool, error) {
// 	fmt.Printf("\n----------------- loadDuplicate: filename=%s\n", duplicate.Filename)
// 	masterName, uuid := splitDuplicateNameUuid(duplicate.Filename)
// 	if uuid == "" {
// 		return false, nil
// 	}
// 	fmt.Printf("### findMaster: masterName=%s, uuid=%s\n", masterName, uuid)

// 	count, err := srv.queries.IsBlocked(ctx, &db.IsBlockedParams{
// 		AccID: acc.ID,
// 		ID:    duplicate.ID,
// 	})
// 	if err != nil {
// 		return false, fmt.Errorf("IsBlocked: %w", err)
// 	}
// 	if count > 0 {
// 		return false, nil
// 	}

// 	image, err := srv.updateImageInDB(ctx, acc, duplicate)
// 	if err != nil {
// 		return false, fmt.Errorf("updateImageInDB: %w", err)
// 	}

// 	masterFile, err := srv.shopClient.FindMasterImage(ctx, acc, masterName, int(duplicate.Size))
// 	if err != nil {
// 		return false, fmt.Errorf("FindMasterImage: %w", err)
// 	}
// 	if masterFile != nil {
// 		if _, err := srv.updateImageInDB(ctx, acc, masterFile); err != nil {
// 			return false, fmt.Errorf("updateImageInDB: %w", err)
// 		}
// 	}

// 	// todo: count created?
// 	return image != nil, nil
// }

// func (srv *server) updateImageInDB(ctx context.Context, acc *db.Account, shopFile *shop.File) (*db.Image, error) {
// 	masterName, uuid := splitDuplicateNameUuid(shopFile.Filename)

// 	image, err := srv.findImageInDB(ctx, shopFile.ID)
// 	if err != nil {
// 		return nil, fmt.Errorf("findImageById: %w", err)
// 	}

// 	limit_ts_backup := timestamp_at(time.Now().Add(-14 * 24 * time.Hour))
// 	if image != nil && image.Size == int64(shopFile.Size) && image.Name == masterName && image.Uuid.String == uuid && image.Updated > limit_ts_backup {
// 		fmt.Printf("found image in db correct size: id=%d, size=%d, filename=%s, uuid=%s\n", image.ID, image.Size, image.Name, image.Uuid.String)
// 		return image, nil
// 	}

// 	if image != nil {
// 		fmt.Printf("found image in db, but wrong size or too old: id=%d, filename=%s, sizeDb=%d, sizeShop=%d\n",
// 			image.ID, image.Name, image.Size, shopFile.Size)
// 	} else {
// 		fmt.Printf("did not find image in db: id=%d, size=%d, filename=%s\n", shopFile.ID, shopFile.Size, shopFile.Filename)
// 	}

// 	fmt.Printf("reload file from shop: filename=%s, size=%d\n", shopFile.Filename, shopFile.Size)

// 	shopImage, err := srv.shopClient.FindImageById(ctx, acc, shopFile.ID)
// 	if err != nil {
// 		return nil, fmt.Errorf("FindImageById: %w", err)
// 	}
// 	if shopImage == nil {
// 		fmt.Printf("did not find image in shop: id=%d\n", shopFile.ID)
// 		return nil, nil
// 	}
// 	checksum, err := srv.fetchImageChecksum(ctx, acc, shopImage)
// 	if err != nil {
// 		return nil, fmt.Errorf("fetchImageChecksum: %w", err)
// 	}

// 	image = &db.Image{
// 		AccID:    acc.ID,
// 		ID:       shopImage.ID,
// 		Name:     masterName,
// 		Mime:     shopImage.MimeType,
// 		Url:      shopImage.URL,
// 		Size:     shopImage.FileSize,
// 		Checksum: checksum,
// 		Uuid:     sql.NullString{String: uuid, Valid: uuid != ""},
// 		Status:   FILE_STATUS_NEW,
// 		Updated:  timestamp(),
// 	}
// 	if err := srv.createImage(ctx, image); err != nil {
// 		return nil, fmt.Errorf("createImage: %w", err)
// 	}
// 	return image, nil
// }

// func (srv *server) findImageInDB(ctx context.Context, imageID int64) (*db.Image, error) {
// 	image, err := srv.queries.GetImage(ctx, imageID)
// 	if err != nil {
// 		if errors.Is(err, sql.ErrNoRows) {
// 			return nil, nil
// 		}
// 		return nil, fmt.Errorf("FindFileById: %w", err)
// 	}
// 	return &image, nil
// }

// func (srv *server) fetchImageChecksum(ctx context.Context, acc *db.Account, image *shop.Image) (string, error) {
// 	fmt.Printf("fetchImageChecksum: image=%+v\n", image)
// 	body, err := srv.loadImageFromURL(ctx, image.OriginalSourceURL)
// 	if err != nil {
// 		return "", fmt.Errorf("loadImageFromURL: %w", err)
// 	}
// 	if image.FileSize != int64(len(body)) {
// 		return "", fmt.Errorf("filesize mismatch, expected %d, got %d", image.FileSize, len(body))
// 	}
// 	checksum := calcChecksum(body)

// 	if err := srv.backupImage(ctx, acc, image, body, checksum); err != nil {
// 		return "", fmt.Errorf("backupImage: %w", err)
// 	}
// 	return checksum, nil
// }

// func (srv *server) backupImage(ctx context.Context, acc *db.Account, image *shop.Image, body []byte, checksum string) error {
// 	// todo: Skip file size from primary key
// 	backup, err := srv.queries.GetBackup(ctx, &db.GetBackupParams{
// 		AccID:    acc.ID,
// 		Checksum: checksum,
// 		Size:     image.FileSize,
// 		Mime:     image.MimeType,
// 	})
// 	if err != nil {
// 		if !errors.Is(err, sql.ErrNoRows) {
// 			return fmt.Errorf("GetBackup: %w", err)
// 		}
// 	}

// 	limit_ts_backup := timestamp_at(time.Now().Add(-15 * 24 * time.Hour))
// 	if backup.Checksum != "" && backup.Updated > limit_ts_backup {
// 		fmt.Printf("image already backed up, filename=%s\n", image.Filename)
// 		return nil
// 	}

// 	ext, error := MimeToExt(image.MimeType)
// 	if error != nil {
// 		return fmt.Errorf("MimeToExt: %w", error)
// 	}
// 	fmt.Printf("#\n#\n# create backup: filename=%s\n", fmt.Sprintf("%d/%d/%s.%s", acc.ID, image.FileSize, checksum, ext))

// 	// todo: copy to R2
// 	imageID := fmt.Sprintf("%d/%s.%s", acc.ID, checksum, ext)
// 	if err := srv.uploadToBucket(ctx, bytes.NewReader(body), "pub-we", imageID, image.MimeType); err != nil {
// 		return fmt.Errorf("uploadToBucket: %w", err)
// 	}

// 	if err := srv.queries.CreateBackup(ctx, &db.CreateBackupParams{
// 		AccID:    acc.ID,
// 		Checksum: checksum,
// 		Size:     image.FileSize,
// 		Mime:     image.MimeType,
// 		Updated:  timestamp(),
// 	}); err != nil {
// 		return fmt.Errorf("CreateBackup: %w", err)
// 	}
// 	return nil
// }

// func (srv *server) loadImageFromURL(ctx context.Context, url string) ([]byte, error) {
// 	resp, err := srv.httpClient.Get(url)
// 	if err != nil {
// 		return nil, fmt.Errorf("http.Get: %w", err)
// 	}
// 	defer resp.Body.Close()
// 	body, err := io.ReadAll(resp.Body)
// 	if err != nil {
// 		return nil, fmt.Errorf("io.ReadAll: %w", err)
// 	}
// 	return body, nil
// }

// func calcChecksum(bytes []byte) string {
// 	checksum := md5.Sum(bytes)
// 	return hex.EncodeToString(checksum[:])
// }

// func (srv *server) createImage(ctx context.Context, image *db.Image) error {
// 	if err := srv.queries.CreateImage(ctx, &db.CreateImageParams{
// 		ID:       image.ID,
// 		AccID:    image.AccID,
// 		Url:      image.Url,
// 		Mime:     image.Mime,
// 		Name:     image.Name,
// 		Size:     image.Size,
// 		Checksum: image.Checksum,
// 		Uuid:     image.Uuid,
// 		Status:   image.Status,
// 		Updated:  timestamp(),
// 	}); err != nil {
// 		return fmt.Errorf("CreateFile: %w", err)
// 	}
// 	return nil
// }

// func splitDuplicateNameUuid(filename string) (string, string) {
// 	suffix := filepath.Ext(filename)
// 	filenameNoSuffix := strings.TrimSuffix(filename, suffix)
// 	if len(filenameNoSuffix) < 38 {
// 		return filename, ""
// 	}
// 	separator := filenameNoSuffix[len(filenameNoSuffix)-37]
// 	if separator != '-' && separator != '_' {
// 		return filename, ""
// 	}
// 	guid := filenameNoSuffix[len(filenameNoSuffix)-36:]
// 	if uuid.Validate(guid) != nil {
// 		return filename, ""
// 	}
// 	return filenameNoSuffix[0:len(filenameNoSuffix)-37] + suffix, guid
// }

// func (srv *server) updateScanProducts(ctx context.Context, acc *db.Account, updatedProduct time.Time) {
// 	if err := srv.queries.UpdateScanProducts(ctx, &db.UpdateScanProductsParams{
// 		AccID:          acc.ID,
// 		UpdatedProduct: timestamp_at(updatedProduct),
// 		Updated:        timestamp(),
// 	}); err != nil {
// 		slogctx.Error(ctx, "updateScanProducts", slogctx.Err(err), slog.Int64("accId", acc.ID))
// 	}
// }

func (srv *server) updateScanFiles(ctx context.Context, acc *db.Account, updatedFile time.Time) {
	if err := srv.queries.UpdateScanFiles(ctx, &db.UpdateScanFilesParams{
		AccID:       acc.ID,
		UpdatedFile: timestampAt(updatedFile),
		Updated:     timestamp(),
	}); err != nil {
		slogctx.Error(ctx, "updateScanFiles", slogctx.Err(err), slog.Int64("accId", acc.ID))
	}
}

// func (srv *server) updateScanStatus(ctx context.Context, acc *db.Account, status int) {
// 	if err := srv.queries.UpdateScanStatus(ctx, &db.UpdateScanStatusParams{
// 		AccID:   acc.ID,
// 		Status:  int64(status),
// 		Updated: timestamp(),
// 	}); err != nil {
// 		slogctx.Error(ctx, "updateScanStatus", slogctx.Err(err),
// 			slog.Int64("accId", acc.ID), slog.Int("status", status))
// 	}
// }

// func (srv *server) uploadToBucket(ctx context.Context, file io.Reader, bucket string, key string, mime string) error {
// 	start := time.Now()
// 	srv.tokensS3 <- true
// 	defer func() { <-srv.tokensS3 }()

// 	if _, err := srv.s3Client.PutObject(ctx, &s3.PutObjectInput{
// 		Bucket:      aws.String(bucket),
// 		Key:         aws.String(key),
// 		Body:        file,
// 		ContentType: aws.String(mime),
// 	}); err != nil {
// 		return fmt.Errorf("s3.PutObject: %w", err)
// 	}
// 	slogctx.Info(ctx, "uploadToBucket", slog.String("bucket", bucket), slog.String("key", key), slog.Int64("durationMs", time.Since(start).Milliseconds()))
// 	return nil
// }
