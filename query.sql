-- name: GetAccount :one
SELECT * FROM account
WHERE id = ? LIMIT 1;

-- name: CreateAccount :exec
INSERT OR REPLACE INTO account (id, shop, token, created)
VALUES (?, ?, ?, ?);

-- name: CreateProduct :exec
insert or replace into product (id, acc_id, title, updated)
values (?, ?, ?, ?);

-- name: CreateProductImage :exec 
insert or replace into product_image (product_id, image_id, acc_id, updated)
values (?, ?, ?, ?);

-- name: UpdateProductTs :exec
update product set updated = ? where id = ?;

-- name: DeleteProductImage :exec
delete from product_image where product_id = ? and image_id = ?;

-- name: DeleteOldProductImages :exec
delete from product_image where acc_id = ? and product_id = ? and updated < ?;


------ Scan ------

-- name: GetScan :one
select * from scan where acc_id = ? limit 1;

-- name: CreateScan :exec
insert or replace into scan (acc_id, status, updated_file, updated_product, updated)
values (?, ?, ?, ?, ?);

-- name: UpdateScanStatus :exec
update scan set status = ?, updated = ? where acc_id = ?;

-- name: UpdateScanFiles :exec
update scan set updated_file = ?, updated = ? where acc_id = ?;

-- name: UpdateScanProducts :exec
update scan set updated_product = ?, updated = ? where acc_id = ?;


------ Backup ------

-- name: CreateDoneImage :exec
insert or replace into done_image (acc_id, dup_id, master_id, name, dup_uuid, checksum, size, mime, url, updated_at)
values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: CreateDoneProduct :exec
insert or replace into done_product (acc_id, dup_id, product_id, title, position, created_at)
values (?, ?, ?, ?, ?, ?);

-- name: CreateDoneVariant :exec
insert or replace into done_variant (acc_id, dup_id, product_id, variant_id, created_at)
values (?, ?, ?, ?, ?);

-- name: CreateDoneRemoved :exec
insert or replace into done_removed (acc_id, dup_id, archived, created_at)
values (?, ?, ?, ?);

-- -- name: CreateRemoved :exec
-- insert or replace into removed (acc_id, dup_id, master_id, name, dup_uuid, url, checksum, size, created)
-- values (?, ?, ?, ?, ?, ?, ?, ?, ?);

-- -- name: CreateUnlinked :exec
-- insert or replace into unlinked (acc_id, dup_id, master_id, product_id, title, position, created)
-- values (?, ?, ?, ?, ?, ?, ?);

-- -- name: CreateUnlinkedVar :exec
-- insert or replace into unlinked_var (dup_id, product_id, variant_id)
-- values (?, ?, ?);

-- name: CreateBackup :exec
insert or replace into backup (acc_id, checksum, size, mime, updated)
values (?, ?, ?, ?, ?);

-- name: GetBackup :one
select * from backup where acc_id = ? and checksum = ? and size = ? and mime = ? limit 1;


------ image ------

-- name: CreateImage :exec
insert or replace into image (acc_id, id, name, mime, url, size, checksum, uuid, status, updated)
values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: GetImage :one
select * from image where id = ? limit 1;

-- name: UpdateImageTs :exec
update image set updated = ? where id = ?;

-- name: UpdateImageStatus :exec
update image set status = ?, updated = ? where acc_id = ? and id = ?;

-- name: DeleteImage :exec
delete from image where id = ?;

-- name: GetImageMaster :one
select * from image where acc_id = ? and name = ? and uuid is null limit 1;

-- name: ImagesPending :many
select
  m.acc_id acc_id, m.id master_id, d.id dup_id, pi.product_id, m.name name, d.uuid dup_uuid, p.title,
  m.url, m.checksum, m.size, m.mime
from image m
inner join image d
on
  m.acc_id = d.acc_id and
  m.name = d.name and
  m.checksum = d.checksum and
  m.id != d.id and
  m.uuid is null and
  d.uuid is not null
left join product_image pi on d.id = pi.image_id
left join product p on pi.product_id = p.id
where
  m.acc_id = ? and
  d.status = ?
order by m.name, p.id desc
limit ?;

-- name: ImageToProcess :one
select
  m.acc_id acc_id, m.id master_id, d.id dup_id, m.name name, d.uuid dup_uuid,
  m.url, m.checksum, m.size, m.mime, p.id product_id, p.title product_title, p.updated product_updated
from image m
inner join image d
on
  m.acc_id = d.acc_id and
  m.name = d.name and
  m.checksum = d.checksum and
  m.id != d.id and
  m.uuid is null and
  d.uuid is not null
left join product_image pi on pi.image_id = d.id
left join product p on pi.product_id = p.id
where
  m.acc_id = ? and
  d.status = 1 and
  d.updated < ?
order by d.updated, p.id
limit 1;

-- -- name: CountImagesByStatus :one
-- select count(*) from image where acc_id = ? and status = ? limit ?;

-- -- name: CountImagesByStatus :many
-- select status, count(status) from image where acc_id=? and uuid is not null group by status;

-- name: CanUndoConfirm :one
select count(*) from image where
    acc_id = ? and
    uuid is not null and
    (status = 1 or (status = 2 and updated > ?)) limit 1;

-- name: UndoConfirm :exec
update image set status = 0, updated = ? where
    acc_id = ? and
    uuid is not null and
    (status = 1 or (status = 2 and updated > ?));
    

------ block ------

-- name: CreateBlock :exec
insert or replace into block (acc_id, id, updated)
values (?, ?, ?);

-- name: IsBlocked :one
select count(*) from block where acc_id = ? and id = ? limit 1;


------ done ------

-- name: GetDoneProducts :many
select
  di.master_id, di.dup_id, di.name, di.dup_uuid, di.mime,
  di.url, dp.product_id, dp.title, dp.created_at
from done_image di join done_product dp on di.dup_id = dp.dup_id
where di.acc_id = ?
order by dp.created_at asc limit 50;

-- name: GetDoneRemoves :many
select
  di.master_id, di.dup_id, di.name, di.dup_uuid, di.mime,
  di.url, dr.archived, dr.created_at
from done_image di join done_removed dr on di.dup_id = dr.dup_id
where di.acc_id = ?
order by dr.created_at asc limit 50;
