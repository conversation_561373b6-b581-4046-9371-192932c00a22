package main

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log/slog"
	"slices"
	"time"

	"github.com/tobimadev/imagededup/internal/db"
	"github.com/tobimadev/imagededup/internal/shop"
	slogctx "github.com/veqryn/slog-context"
)

func (srv *server) runProcessDuplicates(ctx context.Context) error {
	acc, err := loadAccount(ctx, srv, 1589414162180424936)
	if err != nil {
		return fmt.Errorf("loadAccount: %w", err)
	}

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()
	timeNextRun := time.Now().Add(10 * time.Second)

	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			if time.Now().Before(timeNextRun) {
				continue
			}
			fmt.Printf("--- tick processDuplicates ---\n")
			if err := srv.processDuplicates(ctx, acc); err != nil {
				slogctx.Error(ctx, "processDuplicates", slogctx.Err(err))
			}
			timeNextRun = time.Now().Add(31 * time.Second)
		}
	}
}

func (srv *server) processDuplicates(ctx context.Context, acc *db.Account) error {
	for {
		fmt.Printf("--- processDuplicates\n")
		limit_ts := timestampAt(time.Now().Add(-60 * time.Second))
		duplicate, err := srv.queries.ImageToProcess(ctx, &db.ImageToProcessParams{
			AccID:   acc.ID,
			Updated: limit_ts,
		})
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				fmt.Printf("### processDuplicates: no more images to process\n")
				return nil
			}
			return fmt.Errorf("processDuplicates: %w", err)
		}
		fmt.Printf("### processDuplicates: master_id=%d, dup_id=%d, name=%s, uuid=%s\n",
			duplicate.MasterID, duplicate.DupID, duplicate.Name, duplicate.DupUuid.String)
		// if duplicate.MasterID <= 0 {
		// 	return nil // no more images to process
		// }

		ts := timestamp()
		if err := srv.queries.UpdateImageTs(ctx, &db.UpdateImageTsParams{
			Updated: ts,
			ID:      duplicate.DupID,
		}); err != nil {
			return fmt.Errorf("UpdateImageTs: %w", err)
		}

		productID := int64(0)
		if duplicate.ProductID.Valid && duplicate.ProductUpdated.Valid {
			fmt.Printf("### product_id=%d, product_updated=%d\n", duplicate.ProductID.Int64, duplicate.ProductUpdated.Int64)
			productID = duplicate.ProductID.Int64
			if duplicate.ProductUpdated.Int64 > limit_ts {
				continue
			}
			if err := srv.queries.UpdateProductTs(ctx, &db.UpdateProductTsParams{
				Updated: ts,
				ID:      productID,
			}); err != nil {
				return fmt.Errorf("UpdateProductTs: %w", err)
			}
		}

		fmt.Printf("\n------\nprocessDuplicates: dup_id=%d, productID=%d, name=%s, uuid=%s\n",
			duplicate.DupID, productID, duplicate.Name, duplicate.DupUuid.String)
		if err := srv.processDuplicate(ctx, acc, &duplicate); err != nil {
			slogctx.Error(ctx, "processDuplicate", slogctx.Err(err), slog.Any("duplicate", duplicate))
			return fmt.Errorf("processDuplicate: %w", err)
		}

		fmt.Printf("\n######### processDuplicate done ################\n")
		time.Sleep(3 * time.Second)
	}
}

func (srv *server) processDuplicate(ctx context.Context, acc *db.Account, duplicate *db.ImageToProcessRow) error {
	fmt.Printf("\n\n--------\nprocessDuplicate: master_id=%d, dup_id=%d, name=%s, uuid=%s\n",
		duplicate.MasterID, duplicate.DupID, duplicate.Name, duplicate.DupUuid.String)

	unchangedMaster, err := srv.verifImageUnchanged(ctx, acc, duplicate.MasterID, duplicate.Size)
	if err != nil {
		return fmt.Errorf("verifImageUnchanged.master: %w", err)
	}
	unchangedDup, err := srv.verifImageUnchanged(ctx, acc, duplicate.DupID, duplicate.Size)
	if err != nil {
		return fmt.Errorf("verifImageUnchanged.dup: %w", err)
	}
	fmt.Printf("unchangedMaster=%v, unchangedDup=%v\n", unchangedMaster, unchangedDup)
	if !unchangedMaster || !unchangedDup {
		return nil
	}

	if duplicate.ProductID.Valid {
		if err := srv.relinkDuplicate(ctx, acc, duplicate); err != nil {
			return fmt.Errorf("relinkDuplicate: %w", err)
		}
		return nil
	}
	// if err := srv.removeUnusedDuplicate(ctx, acc, duplicate); err != nil {
	// 	return fmt.Errorf("removeUnusedDuplicate: %w", err)
	// }
	if err := srv.archiveUnusedDuplicate(ctx, acc, duplicate); err != nil {
		return fmt.Errorf("archiveUnusedDuplicate: %w", err)
	}
	return nil
}

func (srv *server) verifImageUnchanged(ctx context.Context, acc *db.Account, imageID int64, size int64) (bool, error) {
	file, err := srv.shopClient.FindImageById(ctx, acc, imageID)
	if err != nil {
		return false, fmt.Errorf("FindImageById: %w", err)
	}
	if file == nil || file.FileSize != size {
		if err := srv.queries.DeleteImage(ctx, imageID); err != nil {
			return false, fmt.Errorf("DeleteFile: %w", err)
		}
		return false, nil
	}
	return true, nil
}

func (srv *server) removeUnusedDuplicate(ctx context.Context, acc *db.Account, duplicate *db.ImageToProcessRow) error {
	// Verify that image is unused
	query := fmt.Sprintf("id:%d used_in:none media_type:IMAGE", duplicate.DupID)
	files, err := srv.shopClient.ListFiles(ctx, acc, query, shop.FileSortKeysId, 1, "")
	if err != nil {
		return fmt.Errorf("ListFiles: %w", err)
	}
	if files == nil || len(files.Files) == 0 {
		slogctx.Info(ctx, "removeUnusedDuplicate, file not found or not unused",
			slog.Int64("accId", acc.ID), slog.Int64("dupId", duplicate.DupID))
		return nil
	}

	// todo: transaction
	if err := srv.queries.CreateDoneImage(ctx, &db.CreateDoneImageParams{
		AccID:     acc.ID,
		DupID:     duplicate.DupID,
		MasterID:  duplicate.MasterID,
		Name:      duplicate.Name,
		DupUuid:   duplicate.DupUuid.String,
		Checksum:  duplicate.Checksum,
		Size:      duplicate.Size,
		Mime:      duplicate.Mime,
		Url:       duplicate.Url,
		UpdatedAt: timestamp(),
	}); err != nil {
		return fmt.Errorf("CreateDoneImage: %w", err)
	}
	if err := srv.queries.CreateDoneRemoved(ctx, &db.CreateDoneRemovedParams{
		AccID:     acc.ID,
		DupID:     duplicate.DupID,
		Archived:  false,
		CreatedAt: timestamp(),
	}); err != nil {
		return fmt.Errorf("CreateDoneRemoved: %w", err)
	}
	if err := srv.shopClient.FileDelete(ctx, acc, duplicate.DupID); err != nil {
		return fmt.Errorf("FileDelete: %w", err)
	}
	if err := srv.queries.DeleteImage(ctx, duplicate.DupID); err != nil {
		return fmt.Errorf("DeleteImage: %w", err)
	}
	return nil
}

func (srv *server) archiveUnusedDuplicate(ctx context.Context, acc *db.Account, duplicate *db.ImageToProcessRow) error {
	urlArchivedJpg := "https://pub-28fff66f18164dcc9cbaa6deb828ec94.r2.dev/archived.jpg"
	// urlArchivedPng := "https://pub-28fff66f18164dcc9cbaa6deb828ec94.r2.dev/archived.png"

	// Verify that image is unused
	query := fmt.Sprintf("id:%d used_in:none media_type:IMAGE", duplicate.DupID)
	files, err := srv.shopClient.ListFiles(ctx, acc, query, shop.FileSortKeysId, 1, "")
	if err != nil {
		return fmt.Errorf("ListFiles: %w", err)
	}
	if files == nil || len(files.Files) == 0 {
		slogctx.Info(ctx, "removeUnusedDuplicate, file not found or not unused",
			slog.Int64("accId", acc.ID), slog.Int64("dupId", duplicate.DupID))
		return nil
	}

	// todo: transaction
	if err := srv.queries.CreateDoneImage(ctx, &db.CreateDoneImageParams{
		AccID:     acc.ID,
		DupID:     duplicate.DupID,
		MasterID:  duplicate.MasterID,
		Name:      duplicate.Name,
		DupUuid:   duplicate.DupUuid.String,
		Checksum:  duplicate.Checksum,
		Size:      duplicate.Size,
		Mime:      duplicate.Mime,
		Url:       duplicate.Url,
		UpdatedAt: timestamp(),
	}); err != nil {
		return fmt.Errorf("CreateDoneImage: %w", err)
	}
	if err := srv.queries.CreateDoneRemoved(ctx, &db.CreateDoneRemovedParams{
		AccID:     acc.ID,
		DupID:     duplicate.DupID,
		Archived:  true,
		CreatedAt: timestamp(),
	}); err != nil {
		return fmt.Errorf("CreateDoneRemoved: %w", err)
	}
	if err := srv.shopClient.FileArchive(ctx, acc, duplicate.DupID, duplicate.Name, duplicate.DupUuid.String, urlArchivedJpg); err != nil {
		return fmt.Errorf("FileDelete: %w", err)
	}
	if err := srv.queries.DeleteImage(ctx, duplicate.DupID); err != nil {
		return fmt.Errorf("DeleteImage: %w", err)
	}
	return nil
}

func (srv *server) relinkDuplicate(ctx context.Context, acc *db.Account, duplicate *db.ImageToProcessRow) error {
	productID := duplicate.ProductID.Int64
	fmt.Printf("### relinkDuplicate: master_id=%d, dup_id=%d, product_id=%d\n",
		duplicate.MasterID, duplicate.DupID, productID)

	if err := srv.queries.UpdateProductTs(ctx, &db.UpdateProductTsParams{
		Updated: timestamp(),
		ID:      productID,
	}); err != nil {
		return fmt.Errorf("UpdateProductTs: %w", err)
	}

	productFiles, err := srv.shopClient.ListProductFiles(ctx, acc, fmt.Sprintf("id:%d", productID), "", 1)
	if err != nil {
		return fmt.Errorf("ListProductFiles: %w", err)
	}
	if productFiles == nil || len(productFiles.ProductFiles) == 0 {
		return nil // product does not exist or no images to replace
	}

	variantImages := make([]shop.VariantImage, 0)
	if !productFiles.HasOnlyDefaultVariant {
		variantImages, err = srv.getVariantImages(ctx, acc, productID)
		if err != nil {
			return fmt.Errorf("getVariantImages: %w", err)
		}
	}
	fmt.Printf("productFiles: %+v\n", productFiles.ProductFiles)
	fmt.Printf("variantImages: %+v\n", variantImages)

	// todo: Remove unlinks if duplicate image has changed version

	existingImages := make([]int64, 0)
	for _, image := range productFiles.ProductFiles {
		existingImages = append(existingImages, image.ImageID)
	}

	position := slices.Index(existingImages, duplicate.DupID)
	if position < 0 {
		slogctx.Info(ctx, "relinkDuplicate, duplicate image not found in product",
			slog.Int64("accId", acc.ID), slog.Int64("productId", productID),
			slog.Int64("dupId", duplicate.DupID), slog.Int64("masterId", duplicate.MasterID))
		return nil
	}

	request := shop.RelinkRequest{
		ProductID:      productID,
		MasterID:       duplicate.MasterID,
		DupID:          duplicate.DupID,
		Position:       position,
		AttachVariants: make([]shop.VariantImage, 0),
	}

	if slices.Contains(existingImages, duplicate.MasterID) {
		request.MasterID = -1
	}

	for _, varImage := range variantImages {
		fmt.Printf("### varImage=%+v\n", varImage)
		if varImage.ImageID != duplicate.DupID {
			continue
		}
		request.AttachVariants = append(request.AttachVariants, shop.VariantImage{
			VarID:   varImage.VarID,
			ImageID: duplicate.MasterID,
		})
	}

	// todo: transaction
	if err := srv.queries.CreateDoneImage(ctx, &db.CreateDoneImageParams{
		AccID:     acc.ID,
		DupID:     duplicate.DupID,
		MasterID:  duplicate.MasterID,
		Name:      duplicate.Name,
		DupUuid:   duplicate.DupUuid.String,
		Checksum:  duplicate.Checksum,
		Size:      duplicate.Size,
		Mime:      duplicate.Mime,
		Url:       duplicate.Url,
		UpdatedAt: timestamp(),
	}); err != nil {
		return fmt.Errorf("CreateDoneImage: %w", err)
	}
	if err := srv.queries.CreateDoneProduct(ctx, &db.CreateDoneProductParams{
		AccID:     acc.ID,
		DupID:     duplicate.DupID,
		ProductID: productID,
		Title:     duplicate.ProductTitle.String,
		Position:  int64(position),
		CreatedAt: timestamp(),
	}); err != nil {
		return fmt.Errorf("CreateDoneProduct: %w", err)
	}
	for _, v := range request.AttachVariants {
		if err := srv.queries.CreateDoneVariant(ctx, &db.CreateDoneVariantParams{
			AccID:     acc.ID,
			DupID:     duplicate.DupID,
			ProductID: productID,
			VariantID: v.VarID,
			CreatedAt: timestamp(),
		}); err != nil {
			return fmt.Errorf("CreateDoneVariant: %w", err)
		}
	}

	if err := srv.shopClient.RelinkProduct(ctx, acc, &request); err != nil {
		return fmt.Errorf("RelinkProduct: %w", err)
	}
	if err := srv.queries.DeleteProductImage(ctx, &db.DeleteProductImageParams{
		ProductID: productID,
		ImageID:   duplicate.DupID,
	}); err != nil {
		return fmt.Errorf("DeleteProductImage: %w", err)
	}
	return nil
}

func (srv *server) getVariantImages(ctx context.Context, acc *db.Account, productID int64) ([]shop.VariantImage, error) {
	varImages := make([]shop.VariantImage, 0)
	cursor := ""

	// todo: limit loop
	for {
		variantImagesResult, err := srv.shopClient.GetVariantImages(ctx, acc, productID, 200, cursor)
		if err != nil {
			return nil, fmt.Errorf("GetVariantImages: %w", err)
		}
		if variantImagesResult == nil {
			break
		}
		varImages = append(varImages, variantImagesResult.VariantImages...)
		if !variantImagesResult.HasNextPage {
			break
		}
		cursor = variantImagesResult.Cursor
	}
	return varImages, nil
}
