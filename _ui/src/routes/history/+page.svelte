<script lang="ts">
	import { type Done, postConfirmImages, getImagesDone, type Duplicate } from '$lib/api';
	import { onMount } from 'svelte';

	const CONFIRM_STATUS_NONE = 0;
	const CONFIRM_STATUS_APPROVE = 1;
	const CONFIRM_STATUS_SKIP = 2;

	// let { data }: PageProps = $props();
	// let images = $state<Duplicate[]>(data?.duplicates ?? []);
	let images = $state<Done[] | null>(null);
	let duplicates = $state<Duplicate[] | null>(null);

	let allChecked = $state(false);
	let allIndeterminate = $state(false);
	let confirmStatus = $state(CONFIRM_STATUS_NONE);

	function onSelectionChange(image: Done) {
		if (!images) {
			return;
		}
		image.selected = !image.selected;
		let count = 0;
		images.forEach((image) => {
			if (image.selected) {
				count++;
			}
		});
		let allSelected = count == images.length;
		let allUnselected = count == 0;
		allChecked = allSelected;
		allIndeterminate = !allUnselected && !allSelected;
	}

	function onAllChanged() {
		if (!images) {
			return;
		}
		allChecked = !allChecked;
		allIndeterminate = false;
		images.forEach((image) => {
			image.selected = allChecked;
		});
	}

	async function reloadData() {
		images = null;
		duplicates = null;
		const response = await getImagesDone();
		images = response.dones;
		duplicates = response.duplicates;
	}

	function showToast(message: string, isError: boolean = false) {
		if (shopify) {
			shopify.toast.show(message, { isError });
		}
	}

	async function confirmImages(approve: boolean) {
		if (!images) {
			return;
		}
		const ids = images.filter((image) => image.selected).map((image) => image.dupId);
		if (ids.length == 0) {
			return;
		}

		confirmStatus = approve ? CONFIRM_STATUS_APPROVE : CONFIRM_STATUS_SKIP;

		console.log('confirmImages, approved=' + approve + ', ids=' + JSON.stringify(ids));
		await postConfirmImages(approve, ids);
		showToast(approve ? 'Will merge duplicates' : 'Duplicates will not be merged');

		const keepImages = images.filter((image) => !image.selected);
		allChecked = false;
		allIndeterminate = false;
		images = keepImages;
		if (keepImages.length == 0) {
			images = null;
			reloadData();
		}
		confirmStatus = CONFIRM_STATUS_NONE;
	}

	function clipString(str: string, maxLength: number = 45) {
		if (str.length <= maxLength) {
			return str;
		}
		return str.slice(0, maxLength) + '...';
	}

	onMount(async () => {
		reloadData();
	});
</script>

<ui-title-bar title="Confirm">
	<button variant="breadcrumb">Home</button>
	{#if duplicates && duplicates.length > 0}
		<button
			variant="secondary"
			loading={confirmStatus == CONFIRM_STATUS_SKIP ? '' : undefined}
			disabled={confirmStatus !== CONFIRM_STATUS_NONE}
			onclick={() => confirmImages(false)}
			>Cancel {duplicates.length} pending
		</button>
	{/if}
	<button variant="secondary" loading={images == null ? '' : undefined} onclick={reloadData}>
		Refresh
	</button>
	<!-- <button
		variant="primary"
		tone="critical"
		loading={confirmStatus == CONFIRM_STATUS_APPROVE ? '' : undefined}
		disabled={confirmStatus !== CONFIRM_STATUS_NONE}
		onclick={() => confirmImages(true)}>Merge duplicates</button
	> -->
</ui-title-bar>

<s-page>
	{#if !images}
		<s-stack direction="block" gap="base" alignItems="center" justifyContent="center">
			<s-spinner accessibilityLabel="Loading" size="large-100"></s-spinner>
		</s-stack>
	{:else if images.length == 0}
		<s-stack direction="block" gap="base" alignItems="center" justifyContent="center">
			<s-text>No images to confirm</s-text>
		</s-stack>
	{:else}
		<s-section padding="none">
			<s-table variant="table">
				<s-table-header-row>
					<s-table-header listSlot="primary">
						<s-checkbox
							indeterminate={allIndeterminate}
							checked={allChecked}
							onchange={onAllChanged}
						></s-checkbox>
					</s-table-header>
					<s-table-header>
						<s-grid gridTemplateColumns="minmax(0, 1fr) auto">
							<s-table-header>File</s-table-header>
							<s-stack direction="inline" gap="small"> </s-stack>
						</s-grid>
					</s-table-header>
					<s-table-header>Operation</s-table-header>
				</s-table-header-row>
				<s-table-body>
					{#each images as image (image.dupId + image.operation)}
						<s-table-row>
							<s-table-cell>
								<s-stack direction="inline" gap="base" alignItems="center" justifyContent="start">
									<s-checkbox checked={image.selected} onchange={() => onSelectionChange(image)}
									></s-checkbox>
									<img src={image.url} alt={image.name} class="thumb" />
								</s-stack>
							</s-table-cell>
							<s-table-cell>
								<s-stack direction="block" gap="none" alignItems="start" justifyContent="center">
									<s-link target="_blank" href="shopify://admin/content/files/{image.masterId}">
										{image.name}
									</s-link>
									<s-link
										target="_blank"
										href="shopify://admin/content/files/{image.dupId}"
										stopPropagation={true}
									>
										{image.dupUuid}
									</s-link>
									{#if image.productId}
										<s-link target="_blank" href="shopify://admin/products/{image.productId}">
											{clipString(image.productTitle)}
										</s-link>
									{/if}
								</s-stack>
							</s-table-cell>
							<!-- <s-table-cell>
								{#if image.productId}
									<s-link target="_blank" href="shopify://admin/products/{image.productId}">
										<s-icon type="product"></s-icon>
									</s-link>
								{/if}
							</s-table-cell>
							<s-table-cell>
								<s-badge
									tone={image.operation == 'removed'
										? 'caution'
										: image.operation == 'archived'
											? 'success'
											: 'info'}
								>
									{image.operation}
								</s-badge>
							</s-table-cell> -->
							<s-table-cell>
								<s-stack
									direction="inline"
									gap="small-300"
									alignItems="center"
									justifyContent="end"
								>
									{#if image.operation == 'relinked'}
										<s-link target="_blank" href="shopify://admin/products/{image.productId}">
											<s-icon type="product"></s-icon>
										</s-link>
									{/if}
									<s-badge
										tone={image.operation == 'removed'
											? 'caution'
											: image.operation == 'archived'
												? 'success'
												: 'info'}
									>
										{image.operation}
									</s-badge>
								</s-stack>
							</s-table-cell>
						</s-table-row>
					{/each}
				</s-table-body>
			</s-table>
		</s-section>
	{/if}
</s-page>

<style>
	.thumb {
		border: 1px solid #adb5bd;
		object-fit: contain;
		height: 40px;
		max-width: 80px;
		border-radius: 6%;
	}
</style>
