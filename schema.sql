create table account (
    id    bigint not null primary key,
    shop  text not null,
    token text not null,
    created     bigint not null
);

create table product (
    id        bigint not null primary key,
    acc_id    bigint not null,
    title text not null,
    updated bigint not null
);

create table product_image (
    product_id  bigint not null,
    image_id    bigint not null,
    acc_id      bigint not null,
    updated bigint not null,
    primary key (product_id, image_id)
    -- foreign key (product_id) references product(id) on delete cascade
);

create table image (
    acc_id    bigint not null,
    id  bigint not null primary key,
    name text not null,
    mime text not null,
    url text not null,
    size int not null,
    checksum text not null,
    uuid text,
    status int not null,
    updated bigint not null
);

create table block (
    acc_id bigint not null,
    id bigint not null,
    updated bigint not null,
    primary key (acc_id, id)
)

create table scan (
    acc_id bigint not null primary key,
    status int not null,
    updated_file bigint not null,
    updated_product bigint not null,
    updated bigint not null
);

create table backup (
    acc_id bigint not null,
    checksum text not null,
    size int not null,
    mime text not null,
    updated bigint not null,
    primary key (acc_id, checksum, size, mime)
);

-- create table removed (
--     acc_id bigint not null,
--     dup_id bigint not null,
--     master_id bigint not null,
--     name text not null,
--     dup_uuid text not null,
--     url text not null,
--     checksum text not null,
--     size int not null,
--     created bigint not null,
--     primary key (acc_id, dup_id)
-- );

-- create table unlinked (
--     acc_id bigint not null,
--     dup_id bigint not null,
--     master_id bigint not null,
--     product_id bigint not null,
--     title text not null,
--     position int not null,
--     created bigint not null,
--     primary key (acc_id, dup_id, product_id)
-- );

-- create table unlinked_var (
--     dup_id bigint not null,
--     product_id bigint not null,
--     variant_id bigint not null,
--     primary key (dup_id, product_id, variant_id)
-- );

-----------------------------------------
create table done_image (
    acc_id bigint not null,
    dup_id bigint not null,
    master_id bigint not null,
    name text not null,
    dup_uuid text not null,
    checksum text not null,
    size int not null,
    mime text not null,
    url text not null,
    updated_at bigint not null,
    primary key (acc_id, dup_id)
);

create table done_product (
    acc_id bigint not null,
    dup_id bigint not null,
    product_id bigint not null,
    title text not null,
    position int not null,
    created_at bigint not null,
    primary key (acc_id, dup_id, product_id)
);

create table done_removed (
    acc_id bigint not null,
    dup_id bigint not null,
    archived boolean not null,
    created_at bigint not null,
    primary key (acc_id, dup_id)
);

create table done_variant (
    acc_id bigint not null,
    dup_id bigint not null,
    product_id bigint not null,
    variant_id bigint not null,
    created_at bigint not null,
    primary key (acc_id, dup_id, product_id, variant_id)
)
