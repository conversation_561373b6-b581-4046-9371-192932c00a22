package main

// todo: Handle fuzzy filename search. Maybe by loopg through files two times. Second time to search for master filenames.
// todo: Delete file from db when file removed from shopify.
// Backup before delete
// Backup before relink
// DB table for backed up checksum
import (
	"context"
	"errors"
	"flag"
	"fmt"
	"log/slog"
	"net"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"time"

	"github.com/joho/godotenv"

	"github.com/tobimadev/imagededup/internal/db"
	"github.com/tobimadev/imagededup/internal/shop"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"

	slogctx "github.com/veqryn/slog-context"

	"database/sql"

	_ "modernc.org/sqlite"
)

// codesign --sign - --force --preserve-metadata=entitlements,requirements,flags,runtime ./target/clipper_web

// const (
// 	JOB_TYPE_MATCH = iota
// 	JOB_TYPE_THUMB
// )

// type Job struct {
// 	jobType int
// 	file    *types.File
// }

type server struct {
	httpClient  *http.Client
	shopClient  *shop.Shop
	queries     *db.Queries
	accCache    *AccountCache
	s3Client    *s3.Client
	props       *properties
	tokensS3    chan bool
	tokensThumb chan bool
	tokensMatch chan bool
}

type properties struct {
	isDev             bool
	port              string
	appKey            string
	appSecret         string
	dbPath            string
	s3AccountId       string
	s3AccessKeyId     string
	s3AccessKeySecret string
	poll              bool
}

func main() {
	if err := godotenv.Load(); err != nil {
		fmt.Printf("#\n#\n#\n#\n#\n#\nError loading .env file: %s\n", err)
	}

	port := flag.String("port", getEnvDef("PORT", "8080"), "listener port")
	dbPath := flag.String("db_path", getEnvDef("db_path", "imagededup-d.db"), "path to sqlite file")
	profile := flag.String("profile", getEnvDef("profile", "dev"), "profile, dev/stage/prod")
	appKey := flag.String("appKey", getEnvDef("appKey", "-"), "appKey")
	appSecret := flag.String("appSecret", getEnvDef("appSecret", "-"), "appSecret")
	s3AccountId := flag.String("s3AccountId", getEnvDef("s3AccountId", "-"), "s3AccountId")
	s3AccessKeyId := flag.String("s3AccessKeyId", getEnvDef("s3AccessKeyId", "-"), "s3AccessKeyId")
	s3AccessKeySecret := flag.String("s3AccessKeySecret", getEnvDef("s3AccessKeySecret", "-"), "s3AccessKeySecret")

	isDev := profile != nil && *profile == "dev"

	flag.Parse()
	props := &properties{
		port:              *port,
		appKey:            *appKey,
		appSecret:         *appSecret,
		isDev:             isDev,
		dbPath:            *dbPath,
		s3AccountId:       *s3AccountId,
		s3AccessKeyId:     *s3AccessKeyId,
		s3AccessKeySecret: *s3AccessKeySecret,
	}

	h := slogctx.NewHandler(slog.NewJSONHandler(os.Stdout, nil), nil)
	logger := slog.New(h).
		With(slog.String("service", "clipper")).
		With(slog.String("version", "1.0.0")).
		With(slog.String("profile", *profile))

	logger.Info(
		"properties",
		slog.String("port", props.port))
	slog.SetDefault(logger)

	ctx := slogctx.NewCtx(context.Background(), slog.Default())
	if err := run(ctx, props, logger); err != nil {
		slogctx.Error(ctx, "run", slogctx.Err(err))
		os.Exit(1)
	}
}

func run(ctx context.Context, props *properties, logger *slog.Logger) error {
	ctx, cancel := signal.NotifyContext(ctx, os.Interrupt)
	defer cancel()

	// PRAGMA busy_timeout       = 10000;
	// PRAGMA journal_mode       = WAL;
	// PRAGMA journal_size_limit = *********;
	// PRAGMA synchronous        = NORMAL;
	// PRAGMA foreign_keys       = ON;
	// PRAGMA temp_store         = MEMORY;
	// PRAGMA cache_size         = -16000;

	database, err := sql.Open("sqlite",
		fmt.Sprintf("file:%s?_pragma=foreign_keys(on)&_pragma=journal_mode(wal)&_pragma=synchronous(normal)&_busy_timeout(10000)", props.dbPath))
	if err != nil {
		return err
	}
	fmt.Printf("---- file:%s?_pragma=foreign_keys(on)&_pragma=journal_mode(wal)&_pragma=synchronous(normal)&_busy_timeout(10000)", props.dbPath)

	queries := db.New(database)
	account, err := queries.GetAccount(ctx, 1589414162180424936)
	if err != nil {
		return fmt.Errorf("GetAccount: %w", err)
	}
	fmt.Printf("---- account=%+v\n", account)

	httpClient := &http.Client{Timeout: time.Second * 8}

	shopClient := shop.CreateShop()

	cfg, err := config.LoadDefaultConfig(ctx,
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(props.s3AccessKeyId, props.s3AccessKeySecret, "")),
		config.WithRegion("auto")) //nbg1
	if err != nil {
		return fmt.Errorf("s3.LoadDefaultConfig: %w", err)
	}
	fmt.Printf("---- s3AccountId=%+v\n", props.s3AccountId)
	s3Client := s3.NewFromConfig(cfg, func(o *s3.Options) {
		o.BaseEndpoint = aws.String(fmt.Sprintf("https://%s.r2.cloudflarestorage.com", props.s3AccountId))
		//o.BaseEndpoint = aws.String("https://nbg1.your-objectstorage.com")
	})

	srv := server{
		httpClient:  httpClient,
		shopClient:  shopClient,
		queries:     queries,
		accCache:    NewAccountCache(),
		s3Client:    s3Client,
		props:       props,
		tokensS3:    make(chan bool, 10), // todo: 10
		tokensThumb: make(chan bool, 2),
		tokensMatch: make(chan bool, 10),
	}

	httpHandler := NewHttpMux(&srv)
	httpServer := &http.Server{
		Addr:    net.JoinHostPort("", props.port),
		Handler: httpHandler,
	}

	go func() {
		logger.Info("httpServer listening", slog.String("address", httpServer.Addr))
		if err := httpServer.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			slogctx.Error(ctx, "httpServer listen", slogctx.Err(err))
		}
	}()

	var wg sync.WaitGroup

	wg.Add(1)
	go func() {
		defer wg.Done()
		srv.runLoadProducts(ctx)
	}()

	// wg.Add(1)
	// go func() {
	// 	defer wg.Done()
	// 	srv.runLoadImages(ctx)
	// }()

	wg.Add(1)
	go func() {
		defer wg.Done()
		srv.runProcessDuplicates(ctx)
	}()

	// for i := 0; i < 10; i++ {
	// 	wg.Add(1)
	// 	go func() {
	// 		defer wg.Done()
	// 		srv.jobWorker(ctx)
	// 	}()
	// }

	wg.Add(1)
	go func() {
		defer wg.Done()
		<-ctx.Done()
		shutdownCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
		defer cancel()
		if err := httpServer.Shutdown(shutdownCtx); err != nil {
			slogctx.Error(ctx, "httpServer shutdown", slogctx.Err(err))
		}
		//close(srv.jobChan)
	}()

	wg.Wait()
	return nil
}

func getEnvDef(name string, def string) string {
	v := os.Getenv(name)
	if v == "" {
		return def
	}
	return v
}
