export const API_BASE_URL = 'http://localhost:8080';
//const API_BASE_URL = 'http://***************:8080';
//const API_BASE_URL = '';

export interface Duplicate {
	masterId: number;
	dupId: number;
	//filename: string;
	name: string;
	dupUuid: string;
	url: string;
	mime: string;
	productId: number;
	productTitle: string;
	selected: boolean;
}

export interface Done {
	masterId: number;
	dupId: number;
	name: string;
	dupUuid: string;
	url: string;
	mime: string;
	productId: number;
	productTitle: string;
	operation: string;
	createdAt: number;
	selected: boolean;
}

// export interface ImageCount {
// 	new: number;
// 	blocked: number;
// 	approved: number;
// }

export interface GetImagesPendingResponse {
	duplicates: Duplicate[];
	canUndo: boolean;
	// count: ImageCount;
}

export interface GetImagesDoneResponse {
	duplicates: Duplicate[];
	dones: Done[];
}

export async function getImagesPending(): Promise<GetImagesPendingResponse> {
	return await get('/api/images/pending');
}

export async function getImagesDone(): Promise<GetImagesDoneResponse> {
	return await get('/api/images/done');
}

export async function postConfirmImages(approve: boolean, ids: number[]) {
	return await post('/api/images/confirm', {
		approve: approve,
		ids: ids
	});
}

export async function postConfirmImagesUndo() {
	return await post('/api/images/confirm/undo', {});
}

async function get<T>(path: string): Promise<T> {
	console.log('#### get', path);
	const response = await fetch(`${API_BASE_URL}${path}`, {
		method: 'GET',
		headers: await getHeaders()
	});
	if (!response.ok) {
		throw new Error('http error response status ' + response.status);
	}
	console.log('#### get response', response);
	return await response.json();
}

async function post<T>(path: string, body: any): Promise<T> {
	const response = await fetch(`${API_BASE_URL}${path}`, {
		method: 'POST',
		headers: await getHeaders(),
		body: JSON.stringify(body)
	});
	if (!response.ok) {
		throw new Error('http error response status ' + response.status);
	}
	return await response.json();
}

async function getHeaders() {
	var token = 'dev-token';
	if ('shopify' in window) {
		token = await shopify.idToken();
	}
	return {
		Authorization: token,
		'Content-Type': 'application/json',
		Accept: 'application/json'
	};
}
