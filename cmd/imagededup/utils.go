package main

import (
	"crypto/sha256"
	"encoding/binary"
	"fmt"
	"math/rand"
	"path/filepath"
	"strings"
	"time"
)

func timestamp() int64 {
	return time.Now().UnixMilli()
}

func timestampAt(t time.Time) int64 {
	return t.UnixMilli()
}

func timestampToTime(ts int64) time.Time {
	return time.UnixMilli(ts)
}

// func FilenameAndVersionFromURL(s string) (string, int64, error) {
// 	u, err := url.Parse(s)
// 	if err != nil {
// 		return "", 0, fmt.Errorf("parse url: %w", err)
// 	}

// 	filename := path.Base(u.Path)
// 	v, err := strconv.ParseInt(u.Query().Get("v"), 10, 64)
// 	if err != nil {
// 		return "", 0, fmt.Errorf("parse version, url=%s, err: %w", s, err)
// 	}
// 	return filename, v, nil
// }

// func FilenameFromURL(s string) (string, error) {
// 	u, err := url.Parse(s)
// 	if err != nil {
// 		return "", fmt.Errorf("parse url: %w", err)
// 	}

// 	filename := path.Base(u.Path)
// 	//v, err := strconv.ParseInt(u.Query().Get("v"), 10, 64)
// 	//if err != nil {
// 	//	return "", 0, fmt.Errorf("parse version, url=%s, err: %w", s, err)
// 	//}
// 	return filename, nil
// }

func ToCanonicalFilename(filename string, ext string) string {
	oldExt := filepath.Ext(filename)
	if oldExt == ext {
		return filename
	}
	return strings.TrimSuffix(filename, oldExt) + "." + ext
}

const MimeAvif = "image/avif"
const MimeWebp = "image/webp"
const MimeJpeg = "image/jpeg"
const MimePng = "image/png"
const MimeGif = "image/gif"

var allowedMimes = map[string]string{
	"image/avif": "avif",
	"image/webp": "webp",
	"image/jpeg": "jpg",
	"image/png":  "png",
	"image/gif":  "gif",
}

func MimeToExt(mime string) (string, error) {
	ext, found := allowedMimes[mime]
	if !found {
		return "", fmt.Errorf("unsupported mime: %s", mime)
	}
	return ext, nil
}

// .jpeg to .jpg
func ToShopifyFilename(filename string) string {
	ext := filepath.Ext(filename)
	if strings.ToLower(ext) == ".jpeg" {
		filename = strings.TrimSuffix(filename, ext) + ".jpg"
	}
	return filename
}

func GenerateID(size int) string {
	buff := make([]byte, size)
	choices := len(idbuff)

	for i := 0; i < size; i++ {
		buff[i] = idbuff[rand.Intn(choices)]
	}
	return string(buff)
}

func GenerateNumericID() int64 {
	h := sha256.New()
	h.Write([]byte(GenerateID(16)))
	buf := h.Sum(nil)
	hash := int64(binary.BigEndian.Uint64(buf[0:8]))
	if hash < 0 {
		hash = -hash
	}
	return hash
}

const (
	idbuff = "0123456789abcdefghijkmnpqrstuvwxyz"
)
