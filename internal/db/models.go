// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"database/sql"
)

type Account struct {
	ID      int64
	Shop    string
	Token   string
	Created int64
}

type Backup struct {
	AccID    int64
	Checksum string
	Size     int64
	Mime     string
	Updated  int64
}

type Block struct {
	AccID   int64
	ID      int64
	Updated int64
}

type DoneImage struct {
	AccID     int64
	DupID     int64
	MasterID  int64
	Name      string
	DupUuid   string
	Checksum  string
	Size      int64
	Mime      string
	Url       string
	UpdatedAt int64
}

type DoneProduct struct {
	AccID     int64
	DupID     int64
	ProductID int64
	Title     string
	Position  int64
	CreatedAt int64
}

type DoneRemoved struct {
	AccID     int64
	DupID     int64
	Archived  bool
	CreatedAt int64
}

type DoneVariant struct {
	AccID     int64
	DupID     int64
	ProductID int64
	VariantID int64
	CreatedAt int64
}

type Image struct {
	AccID    int64
	ID       int64
	Name     string
	Mime     string
	Url      string
	Size     int64
	Checksum string
	Uuid     sql.NullString
	Status   int64
	Updated  int64
}

type Product struct {
	ID      int64
	AccID   int64
	Title   string
	Updated int64
}

type ProductImage struct {
	ProductID int64
	ImageID   int64
	AccID     int64
	Updated   int64
}

type Scan struct {
	AccID          int64
	Status         int64
	UpdatedFile    int64
	UpdatedProduct int64
	Updated        int64
}
