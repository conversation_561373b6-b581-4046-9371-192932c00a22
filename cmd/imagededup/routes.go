package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"slices"
	"time"

	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/tobimadev/imagededup/internal/db"
	slogctx "github.com/veqryn/slog-context"
)

func addRoutes(
	mux *http.ServeMux,
	srv *server,
) {
	// mux.Handle("GET /api/images/pending", handleAuth(srv.handleGetPendingImages(), srv))
	// mux.Handle("POST /api/images/confirm", handleAuth(srv.handlePostConfirmImages(), srv))
	// mux.Handle("POST /api/images/match", handleAuth(srv.handlePostMatchImages(), srv))

	// mux.Handle("POST /api/images/presign", handleAuth(srv.handlePostPresignImage(), srv))
	// mux.Handle("POST /api/images/upload/{imageId}", handleAuth(srv.handlePostUploadImage(), srv))

	// mux.Handle("POST /api/settings", handleAuth(srv.handlePostSettings(), srv))
	// mux.Handle("GET /api/settings", handleAuth(srv.handleGetSettings(), srv))

	// mux.Handle("GET /api/status", handleAuth(srv.handleGetStatus(), srv))
	mux.Handle("GET /api/images/pending", handleAuth(srv.getImagesPending(), srv))
	mux.Handle("GET /api/images/done", handleAuth(srv.getImagesDone(), srv))
	mux.Handle("POST /api/images/confirm", handleAuth(srv.postImagesConfirm(), srv))
	mux.Handle("POST /api/images/confirm/undo", handleAuth(srv.postImagesConfirmUndo(), srv))

	mux.Handle("GET /metrics", promhttp.Handler())
}

type Duplicate struct {
	MasterID     int64  `json:"masterId"`
	DupID        int64  `json:"dupId"`
	Name         string `json:"name"`
	DupUuid      string `json:"dupUuid"`
	URL          string `json:"url"`
	Mime         string `json:"mime"`
	ProductID    int64  `json:"productId"`
	ProductTitle string `json:"productTitle"`
}

type Done struct {
	Duplicate
	Operation string `json:"operation"`
	CreatedAt int64  `json:"createdAt"`
	//CreatedAt    string `json:"createdAt"`
}

// type ScanStatus struct {
// 	Running        bool  `json:"running"`
// 	UpdatedFile    int64 `json:"updatedFile"`
// 	UpdatedProduct int64 `json:"updatedProduct"`
// 	Updated        int64 `json:"updated"`
// }

// type ImageCount struct {
// 	New      int `json:"new"`
// 	Blocked  int `json:"blocked"`
// 	Approved int `json:"approved"`
// }

type GetImagesPendingResponse struct {
	Duplicates []Duplicate `json:"duplicates"`
	CanUndo    bool        `json:"canUndo"`
	// Count      ImageCount  `json:"count"`
	// ScanStatus ScanStatus  `json:"scanStatus"`
}

type GetImagesDoneResponse struct {
	Duplicates []Duplicate `json:"duplicates"`
	Dones      []Done      `json:"dones"`
	// ScanStatus ScanStatus  `json:"scanStatus"`
}

type PostImagesConfirmRequest struct {
	Approve bool    `json:"approve"`
	IDs     []int64 `json:"ids"`
}

type PostImagesConfirmResponse struct {
}

type PostImagesConfirmUndoResponse struct {
}

func (srv *server) getImagesPending() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		fmt.Printf("### sleep\n")
		time.Sleep(800 * time.Millisecond)

		ctx := r.Context()
		acc, err := getContextAccount(ctx)
		if err != nil {
			authError(ctx, w, "Unauthorized", fmt.Errorf("handleImagesPending.getContextAccount: %w", err))
			return
		}

		images, err := srv.queries.ImagesPending(ctx, &db.ImagesPendingParams{
			AccID:  acc.ID,
			Status: FILE_STATUS_NEW,
			Limit:  8,
		})
		if err != nil {
			internalError(ctx, w, "database error", fmt.Errorf("handleImagesPending.imagesPending: %w", err))
			return
		}

		// count, err := srv.getImageCountByStatus(ctx, acc.ID)
		// if err != nil {
		// 	internalError(ctx, w, "database error", fmt.Errorf("handleImagesPending.getImageCountByStatus: %w", err))
		// 	return
		// }

		canUndo, err := srv.queries.CanUndoConfirm(ctx, &db.CanUndoConfirmParams{
			AccID:   acc.ID,
			Updated: timestampAt(time.Now().Add(-5 * time.Minute)),
		})
		if err != nil {
			internalError(ctx, w, "database error", fmt.Errorf("handleImagesPending.CanUndoConfirm: %w", err))
			return
		}

		duplicates := make([]Duplicate, 0, len(images))
		uniqueDupIDs := make([]int64, 0, len(images))

		for i := range images {
			image := &images[i]
			if slices.Contains(uniqueDupIDs, image.DupID) {
				continue
			}
			uniqueDupIDs = append(uniqueDupIDs, image.DupID)

			//suffix := filepath.Ext(image.Name)
			//noSuffix := strings.TrimSuffix(image.Name, suffix)

			duplicates = append(duplicates, Duplicate{
				MasterID:  image.MasterID,
				DupID:     image.DupID,
				ProductID: image.ProductID.Int64,
				Name:      image.Name,
				//DupName:      noSuffix + "_" + image.DupUuid.String + suffix,
				DupUuid:      image.DupUuid.String,
				ProductTitle: image.Title.String,
				URL:          image.Url + "&height=60",
				Mime:         image.Mime,
			})
		}
		response := GetImagesPendingResponse{
			Duplicates: duplicates,
			CanUndo:    canUndo > 0,
		}
		encodeOk(ctx, w, response)
	}
}

// func (srv *server) getImageCountByStatus(ctx context.Context, accID int64) (*ImageCount, error) {
// 	counts, err := srv.queries.CountImagesByStatus(ctx, accID)
// 	if err != nil {
// 		return nil, err
// 	}
// 	count := ImageCount{}
// 	for _, c := range counts {
// 		switch c.Status {
// 		case FILE_STATUS_NEW:
// 			count.New = int(c.Count)
// 		case FILE_STATUS_BLOCKED:
// 			count.Blocked = int(c.Count)
// 		case FILE_STATUS_APPROVED:
// 			count.Approved = int(c.Count)
// 		}
// 	}
// 	return &count, nil
// }

func (srv *server) getImagesDone() http.HandlerFunc {

	return func(w http.ResponseWriter, r *http.Request) {
		fmt.Printf("### sleep\n")
		time.Sleep(800 * time.Millisecond)
		ctx := r.Context()
		acc, err := getContextAccount(ctx)
		if err != nil {
			authError(ctx, w, "Unauthorized", fmt.Errorf("getContextAccount: %w", err))
			return
		}

		imagesPending, err := srv.queries.ImagesPending(ctx, &db.ImagesPendingParams{
			AccID:  acc.ID,
			Status: FILE_STATUS_APPROVED,
			Limit:  50,
		})
		if err != nil {
			internalError(ctx, w, "database error", fmt.Errorf("getImagesDone.imagesPending: %w", err))
			return
		}

		duplicates := make([]Duplicate, 0, len(imagesPending))
		uniqueDupIDs := make([]int64, 0, len(imagesPending))

		for i := range imagesPending {
			image := &imagesPending[i]
			if slices.Contains(uniqueDupIDs, image.DupID) {
				continue
			}
			uniqueDupIDs = append(uniqueDupIDs, image.DupID)

			duplicates = append(duplicates, Duplicate{
				MasterID:     image.MasterID,
				DupID:        image.DupID,
				ProductID:    image.ProductID.Int64,
				Name:         image.Name,
				DupUuid:      image.DupUuid.String,
				ProductTitle: image.Title.String,
				URL:          image.Url + "&height=60",
				Mime:         image.Mime,
			})
		}

		doneProducts, err := srv.queries.GetDoneProducts(ctx, acc.ID)
		if err != nil {
			internalError(ctx, w, "database error", fmt.Errorf("GetDoneProduct: %w", err))
			return
		}

		doneRemoves, err := srv.queries.GetDoneRemoves(ctx, acc.ID)
		if err != nil {
			internalError(ctx, w, "database error", fmt.Errorf("GetDoneRemove: %w", err))
			return
		}

		dones := make([]Done, 0, len(doneProducts)+len(doneRemoves))
		for i := range doneProducts {
			doneProduct := &doneProducts[i]
			dones = append(dones, Done{
				Duplicate: Duplicate{
					MasterID:     doneProduct.MasterID,
					DupID:        doneProduct.DupID,
					Name:         doneProduct.Name,
					DupUuid:      doneProduct.DupUuid,
					URL:          doneProduct.Url,
					Mime:         doneProduct.Mime,
					ProductID:    doneProduct.ProductID,
					ProductTitle: doneProduct.Title,
				},
				Operation: "relinked",
				CreatedAt: doneProduct.CreatedAt,
			})
		}
		for i := range doneRemoves {
			doneRemove := &doneRemoves[i]
			op := "removed"
			if doneRemove.Archived {
				op = "archived"
			}
			dones = append(dones, Done{
				Duplicate: Duplicate{
					MasterID:     doneRemove.MasterID,
					DupID:        doneRemove.DupID,
					Name:         doneRemove.Name,
					DupUuid:      doneRemove.DupUuid,
					URL:          doneRemove.Url,
					Mime:         doneRemove.Mime,
					ProductID:    0,
					ProductTitle: "",
				},
				Operation: op,
				CreatedAt: doneRemove.CreatedAt,
			})
		}

		slices.SortFunc(dones, func(a, b Done) int {
			return int(b.CreatedAt - a.CreatedAt)
		})

		// slices.SortFunc(dones, func(a, b Done) int {
		// 	if b.Duplicate.DupID == a.Duplicate.DupID {
		// 		return int(b.CreatedAt - a.CreatedAt)
		// 	}
		// 	return int(b.Duplicate.DupID - a.Duplicate.DupID)
		// })

		encodeOk(ctx, w, &GetImagesDoneResponse{
			Duplicates: duplicates,
			Dones:      dones,
		})
	}
}

// func (srv *server) doGetStatus(ctx context.Context, acc *db.Account, w http.ResponseWriter) {
// 	duplictates, err := srv.queries.ImagesToConfirm(ctx, &db.ImagesToConfirmParams{
// 		AccID: acc.ID,
// 		Limit: 50,
// 	})
// 	if err != nil {
// 		internalError(ctx, w, "database error", fmt.Errorf("NextDuplicate: %w", err))
// 		return
// 	}

// 	scan, err := srv.queries.GetScan(ctx, acc.ID)
// 	if err != nil {
// 		internalError(ctx, w, "database error", fmt.Errorf("GetScan: %w", err))
// 		return
// 	}

// 	response := GetStatusResponse{
// 		Duplicates: make([]Duplicate, 0, len(duplictates)),
// 		ScanStatus: ScanStatus{
// 			Running:        scan.Status == SCAN_STATUS_RUNNING,
// 			UpdatedFile:    scan.UpdatedFile,
// 			UpdatedProduct: scan.UpdatedProduct,
// 			Updated:        scan.Updated,
// 		},
// 	}

// 	uniqueDupIDs := make([]int64, 0, len(duplictates))

// 	for i := range duplictates {
// 		d := &duplictates[i]
// 		if slices.Contains(uniqueDupIDs, d.DupID) {
// 			continue
// 		}
// 		uniqueDupIDs = append(uniqueDupIDs, d.DupID)
// 		response.Duplicates = append(response.Duplicates, Duplicate{
// 			MasterID:     d.MasterID,
// 			DupID:        d.DupID,
// 			ProductID:    d.ProductID.Int64,
// 			Name:         d.Name,
// 			ProductTitle: d.Title.String,
// 			DupUuid:      d.DupUuid.String,
// 			URL:          d.Url,
// 			Mime:         d.Mime,
// 		})
// 	}
// 	encodeOk(ctx, w, response)
// }

func (srv *server) postImagesConfirm() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		fmt.Printf("### sleep\n")
		time.Sleep(800 * time.Millisecond)

		ctx, acc, request, err := parseAuthRequest[PostImagesConfirmRequest](w, r)
		if err != nil {
			return
		}
		fmt.Printf("handleImagesConfirm: %+v\n", request)

		uniqIDs := make([]int64, 0, len(request.IDs))
		for _, id := range request.IDs {
			if slices.Contains(uniqIDs, id) {
				continue
			}
			uniqIDs = append(uniqIDs, id)
		}
		fmt.Printf("handleImagesConfirm uniqueIDs: %+v\n", uniqIDs)

		status := FILE_STATUS_APPROVED
		if !request.Approve {
			status = FILE_STATUS_BLOCKED
		}

		for _, id := range uniqIDs {
			if err := srv.queries.UpdateImageStatus(ctx, &db.UpdateImageStatusParams{
				Status:  int64(status),
				Updated: timestamp(),
				AccID:   acc.ID,
				ID:      id,
			}); err != nil {
				internalError(ctx, w, "database error", fmt.Errorf("UpdateImageStatus: %w", err))
				return
			}
			if status == FILE_STATUS_BLOCKED {
				if err := srv.queries.CreateBlock(ctx, &db.CreateBlockParams{
					AccID:   acc.ID,
					ID:      id,
					Updated: timestamp(),
				}); err != nil {
					internalError(ctx, w, "database error", fmt.Errorf("CreateBlock: %w", err))
					return
				}
			}
		}
		encodeOk(ctx, w, PostImagesConfirmResponse{})
		//srv.doGetStatus(ctx, acc, w)
	}
}

func (srv *server) postImagesConfirmUndo() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		fmt.Printf("### sleep\n")
		time.Sleep(800 * time.Millisecond)

		ctx := r.Context()
		acc, err := getContextAccount(ctx)
		if err != nil {
			authError(ctx, w, "Unauthorized", fmt.Errorf("getContextAccount: %w", err))
			return
		}

		if err := srv.queries.UndoConfirm(ctx, &db.UndoConfirmParams{
			Updated:   timestamp(),
			AccID:     acc.ID,
			Updated_2: timestampAt(time.Now().Add(-5 * time.Minute)), // todo: use constant,
		}); err != nil {
			internalError(ctx, w, "database error", fmt.Errorf("postImagesConfirmUndo.UndoConfirm: %w", err))
			return
		}

		encodeOk(ctx, w, PostImagesConfirmUndoResponse{})
		//srv.doGetStatus(ctx, acc, w)
	}
}

// func (srv *server) handleGetPendingImages() http.HandlerFunc {

// 	type Response struct {
// 		Images   []db.PendingImage `json:"images"`
// 		Counters db.ImageCounter   `json:"counters"`
// 	}

// 	return func(w http.ResponseWriter, r *http.Request) {
// 		ctx := r.Context()
// 		acc, err := getContextAccount(ctx)
// 		if err != nil {
// 			authError(ctx, w, "Unauthorized", fmt.Errorf("getContextAccount: %w", err))
// 			return
// 		}

// 		images := make([]db.PendingImage, 0, 125)

// 		tempImages, err := db.GetPendingFiles(ctx, srv.dbpool, acc.ID, types.OpManualReplace, 50)
// 		if err != nil {
// 			internalError(ctx, w, "database error", fmt.Errorf("GetPendingFiles.OpManualReplace: %w", err))
// 			return
// 		}
// 		images = append(images, tempImages...)

// 		tempImages, err = db.GetPendingFiles(ctx, srv.dbpool, acc.ID, types.OpManualCreate, 50)
// 		if err != nil {
// 			internalError(ctx, w, "database error", fmt.Errorf("GetPendingFiles.OpManualCreate: %w", err))
// 			return
// 		}
// 		images = append(images, tempImages...)

// 		tempImages, err = db.GetPendingFiles(ctx, srv.dbpool, acc.ID, types.OpManualMatch, 25)
// 		if err != nil {
// 			internalError(ctx, w, "database error", fmt.Errorf("GetPendingFiles.OpManualMatch: %w", err))
// 			return
// 		}
// 		images = append(images, tempImages...)

// 		counters, err := db.CountPendingFiles(ctx, srv.dbpool, acc.ID)
// 		if err != nil {
// 			internalError(ctx, w, "database error", fmt.Errorf("CountPendingFiles: %w", err))
// 			return
// 		}
// 		encodeOk(ctx, w, &Response{Images: images, Counters: counters})
// 	}
// }

// type confirmImagesRequest struct {
// 	Operation int      `json:"operation"`
// 	Skip      bool     `json:"skip"`
// 	IDs       []string `json:"ids"`
// }

// func (srv *server) handlePostConfirmImages() http.HandlerFunc {

// 	type Response struct {
// 		Images   []db.PendingImage `json:"images"`
// 		Counters db.ImageCounter   `json:"counters"`
// 	}

// 	return func(w http.ResponseWriter, r *http.Request) {
// 		ctx, acc, request, err := parseAuthRequest[confirmImagesRequest](w, r)
// 		if err != nil {
// 			return
// 		}
// 		if err := srv.confirmImages(ctx, acc, request); err != nil {
// 			internalError(ctx, w, "database error", fmt.Errorf("confirmImages: %w", err))
// 			return
// 		}

// 		counters, err := db.CountPendingFiles(ctx, srv.dbpool, acc.ID)
// 		if err != nil {
// 			internalError(ctx, w, "database error", fmt.Errorf("CountPendingFiles: %w", err))
// 			return
// 		}
// 		encodeOk(ctx, w, &Response{Counters: counters})
// 	}
// }

// type presignImageRequest struct {
// 	Filename string `json:"filename"`
// 	Size     int64  `json:"size"`
// 	Mime     string `json:"mime"`
// }

// type presignImageResponse struct {
// 	ImageID string `json:"imageId"`
// 	//Filename string `json:"filename"`
// 	URL     string `json:"url"`
// 	IsDup   bool   `json:"isDup"`
// 	IsError bool   `json:"isError"`
// 	Message string `json:"message"`
// }

// func (srv *server) handlePostPresignImage() http.HandlerFunc {

// 	return func(w http.ResponseWriter, r *http.Request) {
// 		ctx, acc, request, err := parseAuthRequest[presignImageRequest](w, r)
// 		if err != nil {
// 			return
// 		}
// 		response, err := srv.presignImage(ctx, acc, request)
// 		if err != nil {
// 			internalError(ctx, w, "server error", fmt.Errorf("presignImage: %w", err))
// 			return
// 		}
// 		encodeOk(ctx, w, response)
// 	}
// }

// func (srv *server) handlePostUploadImage() http.HandlerFunc {

// 	return func(w http.ResponseWriter, r *http.Request) {
// 		start := time.Now()
// 		ctx := r.Context()
// 		_, err := getContextAccount(ctx)
// 		if err != nil {
// 			authError(ctx, w, "Unauthorized", fmt.Errorf("getContextAccount: %w", err))
// 			return
// 		}

// 		imageID := r.PathValue("imageId")
// 		fmt.Printf("handlePostUploadImage: imageID=%s\n", imageID)

// 		body, error := io.ReadAll(r.Body)
// 		if error != nil {
// 			internalError(ctx, w, "server error", fmt.Errorf("handlePostUploadImage.ReadAll: %w", err))
// 			return
// 		}
// 		r.Body.Close()
// 		fmt.Printf("body.size=%d\n", len(body))

// 		fmt.Printf("---------------------- GetFile\n")
// 		file, err := db.GetFile(ctx, srv.dbpool, imageID)
// 		fmt.Printf("++++++++++++++++++++++ GetFile\n")
// 		if err != nil {
// 			internalError(ctx, w, "database error", fmt.Errorf("handlePostUploadImage.getFile: %w", err))
// 			return
// 		}
// 		if file == nil {
// 			notFoundError(ctx, w, "image file not found", fmt.Errorf("handlePostUploadImage.getFile: file not found"))
// 			return
// 		}

// 		slogctx.Info(ctx, "handlePostUploadImage", slog.Int64("durationMs", time.Since(start).Milliseconds()))
// 		fmt.Printf("### handlePostUploadImage=%d\n", time.Since(start).Milliseconds())
// 	}
// }

// func (srv *server) handlePostMatchImages() http.HandlerFunc {

// 	type MatchImagesRequest struct {
// 		MatchProds []string `json:"matchProds"`
// 	}

// 	type MatchImageResponse struct {
// 		ProdTitle string `json:"prodTitle"`
// 		VarTitle  string `json:"varTitle"`
// 		ProdID    int64  `json:"prodId"`
// 		VarID     int64  `json:"varId"`
// 	}

// 	type MatchImagesResponse struct {
// 		Images []MatchImageResponse `json:"images"`
// 	}

// 	return func(w http.ResponseWriter, r *http.Request) {
// 		ctx, acc, request, err := parseAuthRequest[MatchImagesRequest](w, r)
// 		if err != nil {
// 			return
// 		}

// 		response := MatchImagesResponse{
// 			Images: make([]MatchImageResponse, len(request.MatchProds)),
// 		}
// 		if len(request.MatchProds) == 0 {
// 			encodeOk(ctx, w, response)
// 			return
// 		}

// 		variants, err := srv.matchImages(ctx, acc, request.MatchProds)
// 		if err != nil {
// 			internalError(ctx, w, "database error", fmt.Errorf("handlePostMatchImages.matchImages: %w", err))
// 			return
// 		}

// 		for i, variant := range variants {
// 			if variant != nil {
// 				response.Images[i] = MatchImageResponse{
// 					ProdTitle: variant.ProdTitle,
// 					VarTitle:  variant.VarTitle,
// 					ProdID:    variant.ProductID,
// 					VarID:     variant.VariantID,
// 				}
// 			}
// 		}
// 		encodeOk(ctx, w, response)
// 	}
// }

// func (srv *server) handlePostSettings() http.HandlerFunc {

// 	type settingsRequest struct {
// 		Settings map[string]string `json:"settings"`
// 	}

// 	return func(w http.ResponseWriter, r *http.Request) {
// 		ctx, acc, request, err := parseAuthRequest[settingsRequest](w, r)
// 		if err != nil {
// 			return
// 		}
// 		if len(request.Settings) == 0 {
// 			return
// 		}

// 		conn, err := srv.dbpool.Take(ctx)
// 		if err != nil {
// 			internalError(ctx, w, "database error", fmt.Errorf("handlePostSettings: %w", err))
// 		}
// 		defer srv.dbpool.Put(conn)

// 		for name, value := range request.Settings {
// 			if err := db.PutAccountSetting(ctx, conn, acc.ID, name, value); err != nil {
// 				internalError(ctx, w, "server error", fmt.Errorf("db.PutAccountSetting: %w", err))
// 				return
// 			}
// 		}
// 	}
// }

// func (srv *server) handleGetSettings() http.HandlerFunc {

// 	type Response struct {
// 		Settings map[string]string `json:"settings"`
// 	}

// 	return func(w http.ResponseWriter, r *http.Request) {
// 		ctx := r.Context()
// 		acc, err := getContextAccount(ctx)

// 		if err != nil {
// 			authError(ctx, w, "Unauthorized", fmt.Errorf("getContextAccount: %w", err))
// 			return
// 		}

// 		settings, err := db.GetAccountSettingsPool(ctx, srv.dbpool, acc.ID)
// 		if err != nil {
// 			internalError(ctx, w, "database error", fmt.Errorf("GetAccountSettingsPool: %w", err))
// 			return
// 		}
// 		encodeOk(ctx, w, &Response{Settings: settings})
// 	}
// }

func parseAuthRequest[T any](w http.ResponseWriter, r *http.Request) (context.Context, *db.Account, *T, error) {
	ctx := r.Context()
	acc, err := getContextAccount(ctx)
	if err != nil {
		authError(ctx, w, "Unauthorized", fmt.Errorf("getContextAccount: %w", err))
		return nil, nil, nil, err
	}

	request, err := decode[T](r.Body)
	if err != nil {
		badRequestError(ctx, w, "jsonDecode", fmt.Errorf("decode: %w", err))
		return nil, nil, nil, err
	}
	return ctx, acc, &request, nil
}

func encodeOk[T any](ctx context.Context, w http.ResponseWriter, v T) {
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(v); err != nil {
		slogctx.Error(ctx, "json encode error", fmt.Errorf("encodeOk: %w", err))
	}
}

func decode[T any](rc io.ReadCloser) (T, error) {
	var v T
	if err := json.NewDecoder(rc).Decode(&v); err != nil {
		return v, fmt.Errorf("decode: %w", err)
	}
	return v, nil
}

func authError(ctx context.Context, w http.ResponseWriter, msg string, err error) {
	slogctx.Error(ctx, "httpAuthError", slogctx.Err(err), slog.String("msg", msg))
	http.Error(w, msg, http.StatusUnauthorized)
}

func internalError(ctx context.Context, w http.ResponseWriter, msg string, err error) {
	slogctx.Error(ctx, "httpInternalError", slogctx.Err(err), slog.String("msg", msg))
	http.Error(w, msg, http.StatusInternalServerError)
}

func badRequestError(ctx context.Context, w http.ResponseWriter, msg string, err error) {
	slogctx.Error(ctx, "httpBadRequest", slogctx.Err(err), slog.String("msg", msg))
	http.Error(w, msg, http.StatusBadRequest)
}

func notFoundError(ctx context.Context, w http.ResponseWriter, msg string, err error) {
	slogctx.Error(ctx, "httpNotFound", slogctx.Err(err), slog.String("msg", msg))
	http.Error(w, msg, http.StatusNotFound)
}
