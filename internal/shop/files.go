package shop

import (
	"context"
	"fmt"
	"time"

	"github.com/tobimadev/imagededup/internal/db"
)

type ListFilesResponse struct {
	Files       []File
	HasNextPage bool
	LastUpdated time.Time
	Cursor      string
}

type File struct {
	ID       int64
	URL      string
	Filename string
	Size     int
}

func (s *Shop) ListFiles(ctx context.Context, acc *db.Account, query string, sortKey FileSortKeys, limit int, cursor string) (*ListFilesResponse, error) {
	fmt.Printf("shop::ListFiles: query=%s, sortKey=%s, limit=%d, cursor=%s\n", query, sortKey, limit, cursor)
	var queryCursor *string = nil
	if cursor != "" {
		queryCursor = &cursor
	}

	gql := s.getClient(acc)
	response, err := listFiles(ctx, gql, query, sortKey, limit, queryCursor)
	if err != nil {
		return nil, fmt.Errorf("listFiles: %w", err)
	}
	if response == nil || len(response.Files.Nodes) == 0 {
		return nil, nil
	}

	files := make([]File, 0, len(response.Files.Nodes))
	lastUpdated := time.Time{}

	for _, node := range response.Files.Nodes {
		n, ok := node.(*listFilesFilesFileConnectionNodesMediaImage)
		if !ok {
			continue
		}
		lastUpdated = n.UpdatedAt
		if n.Status != MediaStatusReady {
			fmt.Printf("Skip image not ready: id=%s, status=%s\n", n.Id, n.Status)
			continue
		}

		filename, _, err := FilenameAndVersionFromURL(n.Image.Url)
		if err != nil {
			return nil, fmt.Errorf("FilenameAndVersionFromURL: %w", err)
		}
		filesize := 0
		if n.OriginalSource.FileSize != nil {
			filesize = *n.OriginalSource.FileSize
		}
		files = append(files, File{
			ID:       toShopifyID(n.Id),
			URL:      n.Image.Url,
			Filename: filename,
			Size:     filesize,
		})
	}

	endCursor := ""
	if response.Files.PageInfo.EndCursor != nil {
		endCursor = *response.Files.PageInfo.EndCursor
	}
	return &ListFilesResponse{
		Files:       files,
		LastUpdated: lastUpdated,
		Cursor:      endCursor,
		HasNextPage: response.Files.PageInfo.HasNextPage,
	}, nil
}

func (s *Shop) FindMasterImage(ctx context.Context, acc *db.Account, masterName string, filesize int) (*File, error) {
	var queryCursor *string = nil
	query := fmt.Sprintf("filename:'%s' media_type:IMAGE original_upload_size:%d", masterName, filesize)
	gql := s.getClient(acc)
	countLoops := 0

	for {
		countLoops++
		response, err := findMasterImage(ctx, gql, query, queryCursor)
		if err != nil {
			return nil, fmt.Errorf("findMasterImage: %w", err)
		}
		if response == nil || len(response.Files.Nodes) == 0 {
			return nil, nil
		}

		for _, node := range response.Files.Nodes {
			n, ok := node.(*findMasterImageFilesFileConnectionNodesMediaImage)
			if !ok {
				continue
			}
			if n.Status != MediaStatusReady {
				fmt.Printf("Skip image not ready: accId=%d, id=%s, status=%s\n", acc.ID, n.Id, n.Status)
				continue
			}

			filename, _, err := FilenameAndVersionFromURL(n.Image.Url)
			if err != nil {
				return nil, fmt.Errorf("FilenameAndVersionFromURL: %w", err)
			}
			if filename == masterName {
				return &File{
					ID:       toShopifyID(n.Id),
					URL:      n.Image.Url,
					Filename: filename,
					Size:     filesize,
				}, nil
			}
		}

		queryCursor = response.Files.PageInfo.EndCursor
		if !response.Files.PageInfo.HasNextPage {
			return nil, nil
		}
		if countLoops > 5 {
			fmt.Printf("FindMasterImage: too many loops, countLoops=%d, accId=%d, masterName=%s, filesize=%d\n",
				countLoops, acc.ID, masterName, filesize)
			return nil, nil
		}
	}
}

type ListProductFilesResponse struct {
	ProductFiles          []ProductFile
	CountProducts         int
	HasOnlyDefaultVariant bool
	HasNextPage           bool
	LastUpdated           time.Time
	Cursor                string
}

// type File struct {
// 	ID       int64
// 	URL      string
// 	Filename string
// 	Size     int
// }

type ProductFile struct {
	ProductID    int64
	ProductTitle string
	ImageID      int64
	Filename     string
	URL          string
	Size         int
	// Updated      time.Time
}

func (s *Shop) ListProductFiles(ctx context.Context, acc *db.Account, query string, cursor string, limit int) (*ListProductFilesResponse, error) {
	fmt.Printf("shop::ListProductFiles: query=%s, limit=%d\n", query, limit)
	var queryCursor *string = nil
	if cursor != "" {
		queryCursor = &cursor
	}

	gql := s.getClient(acc)
	response, err := listProductFiles(ctx, gql, query, queryCursor, limit)
	if err != nil {
		return nil, fmt.Errorf("ListProductFiles: %w", err)
	}
	if response == nil || len(response.Products.Nodes) == 0 {
		return nil, nil
	}

	productFiles := make([]ProductFile, 0, len(response.Products.Nodes))
	hasOnlyDefaultVariant := true

	lastUpdated := time.Time{}

	for _, node := range response.Products.Nodes {
		hasOnlyDefaultVariant = hasOnlyDefaultVariant && node.HasOnlyDefaultVariant
		lastUpdated = node.UpdatedAt
		if len(node.Media.Nodes) == 0 {
			continue
		}
		for _, mediaNode := range node.Media.Nodes {
			n, ok := mediaNode.(*listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage)
			if !ok {
				continue
			}
			if n.Status != MediaStatusReady {
				fmt.Printf("Skip image not ready: id=%s, status=%s\n", n.Id, n.Status)
				continue
			}

			filename, _, err := FilenameAndVersionFromURL(n.Image.Url)
			if err != nil {
				return nil, fmt.Errorf("FilenameAndVersionFromURL: %w", err)
			}
			filesize := 0
			if n.OriginalSource.FileSize != nil {
				filesize = *n.OriginalSource.FileSize
			}
			productFiles = append(productFiles, ProductFile{
				ProductID:    toShopifyID(node.Id),
				ProductTitle: node.Title,
				ImageID:      toShopifyID(n.Id),
				Filename:     filename,
				Size:         filesize,
				URL:          n.Image.Url,
			})
		}
	}
	endCursor := ""
	if response.Products.PageInfo.EndCursor != nil {
		endCursor = *response.Products.PageInfo.EndCursor
	}
	return &ListProductFilesResponse{
		ProductFiles:          productFiles,
		CountProducts:         len(response.Products.Nodes),
		HasOnlyDefaultVariant: hasOnlyDefaultVariant,
		HasNextPage:           response.Products.PageInfo.HasNextPage,
		LastUpdated:           lastUpdated,
		Cursor:                endCursor,
	}, nil
}
