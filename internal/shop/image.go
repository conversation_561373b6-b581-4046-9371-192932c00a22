package shop

import (
	"context"
	"fmt"
	"net/url"
	"path"
	"strconv"
	"strings"

	"github.com/google/uuid"
	"github.com/tobimadev/imagededup/internal/db"
)

type FindImagesResponse struct {
	Cursor string
	Images []Image
}

type Image struct {
	ID                int64
	Status            MediaStatus
	MimeType          string
	Width             int
	Height            int
	URL               string
	Thumbhash         string
	FileSize          int64
	OriginalSourceURL string
	Filename          string
	Version           int64
}

func (s *Shop) FindImages(ctx context.Context, acc *db.Account, query string, sortKey FileSortKeys, limit int, cursor string) (*FindImagesResponse, error) {
	fmt.Printf("shop::FindImages: query=%s, sortKey=%s, limit=%d, cursor=%s\n", query, sortKey, limit, cursor)
	var queryCursor *string = nil
	if cursor != "" {
		queryCursor = &cursor
	}

	gql := s.getClient(acc)
	response, err := findImages(ctx, gql, query, sortKey, limit, queryCursor)
	if err != nil {
		return nil, fmt.Errorf("findImages: %w", err)
	}
	if response == nil || len(response.Files.Nodes) == 0 {
		return nil, nil
	}

	images := make([]Image, 0, len(response.Files.Nodes))
	for _, node := range response.Files.Nodes {
		n, ok := node.(*findImagesFilesFileConnectionNodesMediaImage)
		if !ok {
			continue
		}

		filename, version, err := FilenameAndVersionFromURL(n.Image.Url)
		if err != nil {
			return nil, fmt.Errorf("FilenameAndVersionFromURL: %w", err)
		}
		var thumbhash string
		if n.Image.Thumbhash != nil {
			thumbhash = *n.Image.Thumbhash
		} else {
			thumbhash = "unknown-" + uuid.New().String()
		}

		// todo: check for nil
		images = append(images, Image{
			ID:                toShopifyID(n.Id),
			Status:            n.Status,
			URL:               n.Image.Url,
			MimeType:          *n.MimeType,
			Width:             *n.Image.Width,
			Height:            *n.Image.Height,
			Thumbhash:         thumbhash,
			FileSize:          int64(*n.OriginalSource.FileSize),
			OriginalSourceURL: *n.OriginalSource.Url,
			Filename:          filename,
			Version:           version,
		})
	}

	endCursor := ""
	if response.Files.PageInfo.EndCursor != nil {
		endCursor = *response.Files.PageInfo.EndCursor
	}

	return &FindImagesResponse{
		Cursor: endCursor,
		Images: images,
	}, nil
}

func (s *Shop) FindImageById(ctx context.Context, acc *db.Account, id int64) (*Image, error) {
	fmt.Printf("shop::FindImageById: id=%d\n", id)
	gql := s.getClient(acc)
	response, err := findImageById(ctx, gql, ToMediaID(id))
	if err != nil {
		return nil, fmt.Errorf("findImageById: %w", err)
	}
	if response == nil || response.Node == nil {
		return nil, nil
	}

	node := response.Node
	n, ok := (*node).(*findImageByIdNodeMediaImage)
	if !ok {
		// todo: log error
		return nil, nil
	}

	filename, version, err := FilenameAndVersionFromURL(n.Image.Url)
	if err != nil {
		return nil, fmt.Errorf("FilenameAndVersionFromURL: %w", err)
	}
	var thumbhash string
	if n.Image.Thumbhash != nil {
		thumbhash = *n.Image.Thumbhash
	} else {
		thumbhash = "unknown-" + uuid.New().String()
	}

	image := &Image{
		ID:                toShopifyID(n.Id),
		Status:            n.Status,
		URL:               n.Image.Url,
		MimeType:          *n.MimeType,
		Width:             *n.Image.Width,
		Height:            *n.Image.Height,
		Thumbhash:         thumbhash,
		FileSize:          int64(*n.OriginalSource.FileSize),
		OriginalSourceURL: *n.OriginalSource.Url,
		Filename:          filename,
		Version:           version,
	}
	return image, nil
}

type FindProductImagesResponse struct {
	LastProductID         int64
	HasOnlyDefaultVariant bool
	ProductImages         []ProductImage
}

type ProductImage struct {
	Image
	ProductID int64
	Title     string
}

func (s *Shop) FindProductImages(ctx context.Context, acc *db.Account, query string, limit int) (*FindProductImagesResponse, error) {
	fmt.Printf("shop::FindProductImages: query=%s, limit=%d\n", query, limit)

	gql := s.getClient(acc)
	response, err := findProductImages(ctx, gql, query, limit)
	if err != nil {
		return nil, fmt.Errorf("findProductImages: %w", err)
	}
	if response == nil || len(response.Products.Nodes) == 0 {
		return nil, nil
	}

	productImages := make([]ProductImage, 0, len(response.Products.Nodes))
	lastProductID := int64(-1)
	hasOnlyDefaultVariant := true

	for _, node := range response.Products.Nodes {
		lastProductID = toShopifyID(node.Id)
		hasOnlyDefaultVariant = hasOnlyDefaultVariant && node.HasOnlyDefaultVariant
		if len(node.Media.Nodes) == 0 {
			continue
		}
		for _, mediaNode := range node.Media.Nodes {
			n, ok := mediaNode.(*findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage)
			if !ok {
				continue
			}

			filename, version, err := FilenameAndVersionFromURL(n.Image.Url)
			if err != nil {
				return nil, fmt.Errorf("FilenameAndVersionFromURL: %w", err)
			}

			// todo: handle nil
			productImages = append(productImages, ProductImage{
				Image: Image{
					ID:                toShopifyID(n.Id),
					Status:            n.Status,
					URL:               n.Image.Url,
					MimeType:          *n.MimeType,
					Width:             *n.Image.Width,
					Height:            *n.Image.Height,
					FileSize:          int64(*n.OriginalSource.FileSize),
					OriginalSourceURL: *n.OriginalSource.Url,
					Filename:          filename,
					Version:           version,
				},
				ProductID: lastProductID,
				Title:     node.Title,
			})
		}
	}
	return &FindProductImagesResponse{
		LastProductID:         lastProductID,
		HasOnlyDefaultVariant: hasOnlyDefaultVariant,
		ProductImages:         productImages,
	}, nil
}

func (s *Shop) FileDelete(ctx context.Context, acc *db.Account, imageID int64) error {
	fmt.Printf("shop::FileDelete: imageID=%d\n", imageID)
	gql := s.getClient(acc)
	if _, err := fileDelete(ctx, gql, []string{ToMediaID(imageID)}); err != nil {
		return fmt.Errorf("fileDelete: %w", err)
	}
	return nil
}

func (s *Shop) FileArchive(ctx context.Context, acc *db.Account, imageID int64, filename string, dupUuid string, archivedUrl string) error {
	fmt.Printf("shop::FileArchive: imageID=%d, filename=%s, dupUuid=%s, archivedUrl=%s\n", imageID, filename, dupUuid, archivedUrl)
	gql := s.getClient(acc)
	ext := path.Ext(filename)
	noSuffix := strings.TrimSuffix(filename, ext)
	archivedFilename := fmt.Sprintf("archived_%s_%s%s", noSuffix, dupUuid, ext)
	response, err := fileArchive(ctx, gql, []FileUpdateInput{
		{
			Id:             ToMediaID(imageID),
			Filename:       &archivedFilename,
			OriginalSource: &archivedUrl,
		},
	})
	if err != nil {
		if response != nil && len(response.FileUpdate.UserErrors) > 0 {
			return fmt.Errorf("fileArchive: %s", response.FileUpdate.UserErrors[0].Message)
		}
		return fmt.Errorf("fileArchive: %w", err)
	}
	fmt.Printf("shop::FileArchive: response=%+v\n", response)
	return nil
}

func FilenameAndVersionFromURL(s string) (string, int64, error) {
	u, err := url.Parse(s)
	if err != nil {
		return "", 0, fmt.Errorf("parse url: %w", err)
	}

	filename := path.Base(u.Path)
	v, err := strconv.ParseInt(u.Query().Get("v"), 10, 64)
	if err != nil {
		return "", 0, fmt.Errorf("parse version, url=%s, err: %w", s, err)
	}
	return filename, v, nil
}

func FilenameFromURL(s string) (string, error) {
	u, err := url.Parse(s)
	if err != nil {
		return "", fmt.Errorf("parse url: %w", err)
	}
	return path.Base(u.Path), nil
}
