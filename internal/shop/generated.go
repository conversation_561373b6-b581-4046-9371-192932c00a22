// Code generated by github.com/Khan/genqlient, DO NOT EDIT.

package shop

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Khan/genqlient/graphql"
)

// The set of valid sort keys for the File query.
type FileSortKeys string

const (
	// Sort by the `created_at` value.
	FileSortKeysCreatedAt FileSortKeys = "CREATED_AT"
	// Sort by the `filename` value.
	FileSortKeysFilename FileSortKeys = "FILENAME"
	// Sort by the `id` value.
	FileSortKeysId FileSortKeys = "ID"
	// Sort by the `original_upload_size` value.
	FileSortKeysOriginalUploadSize FileSortKeys = "ORIGINAL_UPLOAD_SIZE"
	// Sort by relevance to the search terms when the `query` parameter is specified on the connection.
	// Don't use this sort key when no search query is specified.
	FileSortKeysRelevance FileSortKeys = "RELEVANCE"
	// Sort by the `updated_at` value.
	FileSortKeysUpdatedAt FileSortKeys = "UPDATED_AT"
)

var AllFileSortKeys = []FileSortKeys{
	FileSortKeysCreatedAt,
	FileSortKeysFilename,
	FileSortKeysId,
	FileSortKeysOriginalUploadSize,
	FileSortKeysRelevance,
	FileSortKeysUpdatedAt,
}

// The possible statuses for a file object.
type FileStatus string

const (
	// File has been uploaded but hasn't been processed.
	FileStatusUploaded FileStatus = "UPLOADED"
	// File is being processed.
	FileStatusProcessing FileStatus = "PROCESSING"
	// File is ready to be displayed.
	FileStatusReady FileStatus = "READY"
	// File processing has failed.
	FileStatusFailed FileStatus = "FAILED"
)

var AllFileStatus = []FileStatus{
	FileStatusUploaded,
	FileStatusProcessing,
	FileStatusReady,
	FileStatusFailed,
}

// The input fields that are required to update a file object.
type FileUpdateInput struct {
	// The ID of the file to be updated.
	Id string `json:"id"`
	// The alternative text description of the file.
	Alt *string `json:"alt"`
	// The source from which to update a media image or generic file.
	// An external URL (for images only) or a
	// [staged upload URL](https://shopify.dev/api/admin-graphql/latest/mutations/stageduploadscreate).
	OriginalSource *string `json:"originalSource"`
	// The source from which to update the media preview image.
	// May be an external URL or a
	// [staged upload URL](https://shopify.dev/api/admin-graphql/latest/mutations/stageduploadscreate).
	PreviewImageSource *string `json:"previewImageSource"`
	// The name of the file including its extension.
	Filename *string `json:"filename"`
	// The IDs of the references to add to the file. Currently only accepts product IDs.
	ReferencesToAdd []string `json:"referencesToAdd"`
	// The IDs of the references to remove from the file. Currently only accepts product IDs.
	ReferencesToRemove []string `json:"referencesToRemove"`
}

// GetId returns FileUpdateInput.Id, and is useful for accessing the field via an interface.
func (v *FileUpdateInput) GetId() string { return v.Id }

// GetAlt returns FileUpdateInput.Alt, and is useful for accessing the field via an interface.
func (v *FileUpdateInput) GetAlt() *string { return v.Alt }

// GetOriginalSource returns FileUpdateInput.OriginalSource, and is useful for accessing the field via an interface.
func (v *FileUpdateInput) GetOriginalSource() *string { return v.OriginalSource }

// GetPreviewImageSource returns FileUpdateInput.PreviewImageSource, and is useful for accessing the field via an interface.
func (v *FileUpdateInput) GetPreviewImageSource() *string { return v.PreviewImageSource }

// GetFilename returns FileUpdateInput.Filename, and is useful for accessing the field via an interface.
func (v *FileUpdateInput) GetFilename() *string { return v.Filename }

// GetReferencesToAdd returns FileUpdateInput.ReferencesToAdd, and is useful for accessing the field via an interface.
func (v *FileUpdateInput) GetReferencesToAdd() []string { return v.ReferencesToAdd }

// GetReferencesToRemove returns FileUpdateInput.ReferencesToRemove, and is useful for accessing the field via an interface.
func (v *FileUpdateInput) GetReferencesToRemove() []string { return v.ReferencesToRemove }

// The possible statuses for a media object.
type MediaStatus string

const (
	// Media has been uploaded but not yet processed.
	MediaStatusUploaded MediaStatus = "UPLOADED"
	// Media is being processed.
	MediaStatusProcessing MediaStatus = "PROCESSING"
	// Media is ready to be displayed.
	MediaStatusReady MediaStatus = "READY"
	// Media processing has failed.
	MediaStatusFailed MediaStatus = "FAILED"
)

var AllMediaStatus = []MediaStatus{
	MediaStatusUploaded,
	MediaStatusProcessing,
	MediaStatusReady,
	MediaStatusFailed,
}

// The input fields for a single move of an object to a specific position in a set, using a zero-based index.
type MoveInput struct {
	// The ID of the object to be moved.
	Id string `json:"id"`
	// The new position of the object in the set.
	NewPosition string `json:"newPosition"`
}

// GetId returns MoveInput.Id, and is useful for accessing the field via an interface.
func (v *MoveInput) GetId() string { return v.Id }

// GetNewPosition returns MoveInput.NewPosition, and is useful for accessing the field via an interface.
func (v *MoveInput) GetNewPosition() string { return v.NewPosition }

// The input fields required to append media to a single variant.
type ProductVariantAppendMediaInput struct {
	// Specifies the variant to which media will be appended.
	VariantId string `json:"variantId"`
	// Specifies the media to append to the variant.
	MediaIds []string `json:"mediaIds"`
}

// GetVariantId returns ProductVariantAppendMediaInput.VariantId, and is useful for accessing the field via an interface.
func (v *ProductVariantAppendMediaInput) GetVariantId() string { return v.VariantId }

// GetMediaIds returns ProductVariantAppendMediaInput.MediaIds, and is useful for accessing the field via an interface.
func (v *ProductVariantAppendMediaInput) GetMediaIds() []string { return v.MediaIds }

// __callGetFileByNameFirstInput is used internally by genqlient
type __callGetFileByNameFirstInput struct {
	Query string `json:"query"`
}

// GetQuery returns __callGetFileByNameFirstInput.Query, and is useful for accessing the field via an interface.
func (v *__callGetFileByNameFirstInput) GetQuery() string { return v.Query }

// __callGetFileByNameNextInput is used internally by genqlient
type __callGetFileByNameNextInput struct {
	Query  string  `json:"query"`
	Cursor *string `json:"cursor"`
}

// GetQuery returns __callGetFileByNameNextInput.Query, and is useful for accessing the field via an interface.
func (v *__callGetFileByNameNextInput) GetQuery() string { return v.Query }

// GetCursor returns __callGetFileByNameNextInput.Cursor, and is useful for accessing the field via an interface.
func (v *__callGetFileByNameNextInput) GetCursor() *string { return v.Cursor }

// __fileArchiveInput is used internally by genqlient
type __fileArchiveInput struct {
	Files []FileUpdateInput `json:"files"`
}

// GetFiles returns __fileArchiveInput.Files, and is useful for accessing the field via an interface.
func (v *__fileArchiveInput) GetFiles() []FileUpdateInput { return v.Files }

// __fileDeleteInput is used internally by genqlient
type __fileDeleteInput struct {
	FileIds []string `json:"fileIds"`
}

// GetFileIds returns __fileDeleteInput.FileIds, and is useful for accessing the field via an interface.
func (v *__fileDeleteInput) GetFileIds() []string { return v.FileIds }

// __findImageByIdInput is used internally by genqlient
type __findImageByIdInput struct {
	Id string `json:"id"`
}

// GetId returns __findImageByIdInput.Id, and is useful for accessing the field via an interface.
func (v *__findImageByIdInput) GetId() string { return v.Id }

// __findImagesInput is used internally by genqlient
type __findImagesInput struct {
	Query   string       `json:"query"`
	SortKey FileSortKeys `json:"sortKey"`
	Limit   int          `json:"limit"`
	Cursor  *string      `json:"cursor"`
}

// GetQuery returns __findImagesInput.Query, and is useful for accessing the field via an interface.
func (v *__findImagesInput) GetQuery() string { return v.Query }

// GetSortKey returns __findImagesInput.SortKey, and is useful for accessing the field via an interface.
func (v *__findImagesInput) GetSortKey() FileSortKeys { return v.SortKey }

// GetLimit returns __findImagesInput.Limit, and is useful for accessing the field via an interface.
func (v *__findImagesInput) GetLimit() int { return v.Limit }

// GetCursor returns __findImagesInput.Cursor, and is useful for accessing the field via an interface.
func (v *__findImagesInput) GetCursor() *string { return v.Cursor }

// __findMasterImageInput is used internally by genqlient
type __findMasterImageInput struct {
	Query  string  `json:"query"`
	Cursor *string `json:"cursor"`
}

// GetQuery returns __findMasterImageInput.Query, and is useful for accessing the field via an interface.
func (v *__findMasterImageInput) GetQuery() string { return v.Query }

// GetCursor returns __findMasterImageInput.Cursor, and is useful for accessing the field via an interface.
func (v *__findMasterImageInput) GetCursor() *string { return v.Cursor }

// __findProductImagesInput is used internally by genqlient
type __findProductImagesInput struct {
	Query string `json:"query"`
	Limit int    `json:"limit"`
}

// GetQuery returns __findProductImagesInput.Query, and is useful for accessing the field via an interface.
func (v *__findProductImagesInput) GetQuery() string { return v.Query }

// GetLimit returns __findProductImagesInput.Limit, and is useful for accessing the field via an interface.
func (v *__findProductImagesInput) GetLimit() int { return v.Limit }

// __getProductVarsInput is used internally by genqlient
type __getProductVarsInput struct {
	Id     string  `json:"id"`
	Limit  int     `json:"limit"`
	Cursor *string `json:"cursor"`
}

// GetId returns __getProductVarsInput.Id, and is useful for accessing the field via an interface.
func (v *__getProductVarsInput) GetId() string { return v.Id }

// GetLimit returns __getProductVarsInput.Limit, and is useful for accessing the field via an interface.
func (v *__getProductVarsInput) GetLimit() int { return v.Limit }

// GetCursor returns __getProductVarsInput.Cursor, and is useful for accessing the field via an interface.
func (v *__getProductVarsInput) GetCursor() *string { return v.Cursor }

// __listFilesInput is used internally by genqlient
type __listFilesInput struct {
	Query   string       `json:"query"`
	SortKey FileSortKeys `json:"sortKey"`
	Limit   int          `json:"limit"`
	Cursor  *string      `json:"cursor"`
}

// GetQuery returns __listFilesInput.Query, and is useful for accessing the field via an interface.
func (v *__listFilesInput) GetQuery() string { return v.Query }

// GetSortKey returns __listFilesInput.SortKey, and is useful for accessing the field via an interface.
func (v *__listFilesInput) GetSortKey() FileSortKeys { return v.SortKey }

// GetLimit returns __listFilesInput.Limit, and is useful for accessing the field via an interface.
func (v *__listFilesInput) GetLimit() int { return v.Limit }

// GetCursor returns __listFilesInput.Cursor, and is useful for accessing the field via an interface.
func (v *__listFilesInput) GetCursor() *string { return v.Cursor }

// __listProductFilesInput is used internally by genqlient
type __listProductFilesInput struct {
	Query  string  `json:"query"`
	Cursor *string `json:"cursor"`
	Limit  int     `json:"limit"`
}

// GetQuery returns __listProductFilesInput.Query, and is useful for accessing the field via an interface.
func (v *__listProductFilesInput) GetQuery() string { return v.Query }

// GetCursor returns __listProductFilesInput.Cursor, and is useful for accessing the field via an interface.
func (v *__listProductFilesInput) GetCursor() *string { return v.Cursor }

// GetLimit returns __listProductFilesInput.Limit, and is useful for accessing the field via an interface.
func (v *__listProductFilesInput) GetLimit() int { return v.Limit }

// __relinkProductInput is used internally by genqlient
type __relinkProductInput struct {
	ProductId   string                           `json:"productId"`
	FileInput   []FileUpdateInput                `json:"fileInput"`
	MoveInput   []MoveInput                      `json:"moveInput"`
	AttachMedia []ProductVariantAppendMediaInput `json:"attachMedia"`
}

// GetProductId returns __relinkProductInput.ProductId, and is useful for accessing the field via an interface.
func (v *__relinkProductInput) GetProductId() string { return v.ProductId }

// GetFileInput returns __relinkProductInput.FileInput, and is useful for accessing the field via an interface.
func (v *__relinkProductInput) GetFileInput() []FileUpdateInput { return v.FileInput }

// GetMoveInput returns __relinkProductInput.MoveInput, and is useful for accessing the field via an interface.
func (v *__relinkProductInput) GetMoveInput() []MoveInput { return v.MoveInput }

// GetAttachMedia returns __relinkProductInput.AttachMedia, and is useful for accessing the field via an interface.
func (v *__relinkProductInput) GetAttachMedia() []ProductVariantAppendMediaInput {
	return v.AttachMedia
}

// callGetFileByNameFirstFilesFileConnection includes the requested fields of the GraphQL type FileConnection.
// The GraphQL type's documentation follows.
//
// An auto-generated type for paginating through multiple Files.
type callGetFileByNameFirstFilesFileConnection struct {
	// A list of nodes that are contained in FileEdge. You can fetch data about an
	// individual node, or you can follow the edges to fetch data about a collection
	// of related nodes. At each node, you specify the fields that you want to retrieve.
	Nodes []callGetFileByNameFirstFilesFileConnectionNodesFile `json:"-"`
	// An object that’s used to retrieve [cursor
	// information](https://shopify.dev/api/usage/pagination-graphql) about the current page.
	PageInfo callGetFileByNameFirstFilesFileConnectionPageInfo `json:"pageInfo"`
}

// GetNodes returns callGetFileByNameFirstFilesFileConnection.Nodes, and is useful for accessing the field via an interface.
func (v *callGetFileByNameFirstFilesFileConnection) GetNodes() []callGetFileByNameFirstFilesFileConnectionNodesFile {
	return v.Nodes
}

// GetPageInfo returns callGetFileByNameFirstFilesFileConnection.PageInfo, and is useful for accessing the field via an interface.
func (v *callGetFileByNameFirstFilesFileConnection) GetPageInfo() callGetFileByNameFirstFilesFileConnectionPageInfo {
	return v.PageInfo
}

func (v *callGetFileByNameFirstFilesFileConnection) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*callGetFileByNameFirstFilesFileConnection
		Nodes []json.RawMessage `json:"nodes"`
		graphql.NoUnmarshalJSON
	}
	firstPass.callGetFileByNameFirstFilesFileConnection = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Nodes
		src := firstPass.Nodes
		*dst = make(
			[]callGetFileByNameFirstFilesFileConnectionNodesFile,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			if len(src) != 0 && string(src) != "null" {
				err = __unmarshalcallGetFileByNameFirstFilesFileConnectionNodesFile(
					src, dst)
				if err != nil {
					return fmt.Errorf(
						"unable to unmarshal callGetFileByNameFirstFilesFileConnection.Nodes: %w", err)
				}
			}
		}
	}
	return nil
}

type __premarshalcallGetFileByNameFirstFilesFileConnection struct {
	Nodes []json.RawMessage `json:"nodes"`

	PageInfo callGetFileByNameFirstFilesFileConnectionPageInfo `json:"pageInfo"`
}

func (v *callGetFileByNameFirstFilesFileConnection) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *callGetFileByNameFirstFilesFileConnection) __premarshalJSON() (*__premarshalcallGetFileByNameFirstFilesFileConnection, error) {
	var retval __premarshalcallGetFileByNameFirstFilesFileConnection

	{

		dst := &retval.Nodes
		src := v.Nodes
		*dst = make(
			[]json.RawMessage,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			var err error
			*dst, err = __marshalcallGetFileByNameFirstFilesFileConnectionNodesFile(
				&src)
			if err != nil {
				return nil, fmt.Errorf(
					"unable to marshal callGetFileByNameFirstFilesFileConnection.Nodes: %w", err)
			}
		}
	}
	retval.PageInfo = v.PageInfo
	return &retval, nil
}

// callGetFileByNameFirstFilesFileConnectionNodesExternalVideo includes the requested fields of the GraphQL type ExternalVideo.
// The GraphQL type's documentation follows.
//
// Represents a video hosted outside of Shopify.
type callGetFileByNameFirstFilesFileConnectionNodesExternalVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns callGetFileByNameFirstFilesFileConnectionNodesExternalVideo.Typename, and is useful for accessing the field via an interface.
func (v *callGetFileByNameFirstFilesFileConnectionNodesExternalVideo) GetTypename() *string {
	return v.Typename
}

// callGetFileByNameFirstFilesFileConnectionNodesFile includes the requested fields of the GraphQL interface File.
//
// callGetFileByNameFirstFilesFileConnectionNodesFile is implemented by the following types:
// callGetFileByNameFirstFilesFileConnectionNodesExternalVideo
// callGetFileByNameFirstFilesFileConnectionNodesGenericFile
// callGetFileByNameFirstFilesFileConnectionNodesMediaImage
// callGetFileByNameFirstFilesFileConnectionNodesModel3d
// callGetFileByNameFirstFilesFileConnectionNodesVideo
// The GraphQL type's documentation follows.
//
// A file interface.
type callGetFileByNameFirstFilesFileConnectionNodesFile interface {
	implementsGraphQLInterfacecallGetFileByNameFirstFilesFileConnectionNodesFile()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() *string
}

func (v *callGetFileByNameFirstFilesFileConnectionNodesExternalVideo) implementsGraphQLInterfacecallGetFileByNameFirstFilesFileConnectionNodesFile() {
}
func (v *callGetFileByNameFirstFilesFileConnectionNodesGenericFile) implementsGraphQLInterfacecallGetFileByNameFirstFilesFileConnectionNodesFile() {
}
func (v *callGetFileByNameFirstFilesFileConnectionNodesMediaImage) implementsGraphQLInterfacecallGetFileByNameFirstFilesFileConnectionNodesFile() {
}
func (v *callGetFileByNameFirstFilesFileConnectionNodesModel3d) implementsGraphQLInterfacecallGetFileByNameFirstFilesFileConnectionNodesFile() {
}
func (v *callGetFileByNameFirstFilesFileConnectionNodesVideo) implementsGraphQLInterfacecallGetFileByNameFirstFilesFileConnectionNodesFile() {
}

func __unmarshalcallGetFileByNameFirstFilesFileConnectionNodesFile(b []byte, v *callGetFileByNameFirstFilesFileConnectionNodesFile) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "ExternalVideo":
		*v = new(callGetFileByNameFirstFilesFileConnectionNodesExternalVideo)
		return json.Unmarshal(b, *v)
	case "GenericFile":
		*v = new(callGetFileByNameFirstFilesFileConnectionNodesGenericFile)
		return json.Unmarshal(b, *v)
	case "MediaImage":
		*v = new(callGetFileByNameFirstFilesFileConnectionNodesMediaImage)
		return json.Unmarshal(b, *v)
	case "Model3d":
		*v = new(callGetFileByNameFirstFilesFileConnectionNodesModel3d)
		return json.Unmarshal(b, *v)
	case "Video":
		*v = new(callGetFileByNameFirstFilesFileConnectionNodesVideo)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing File.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for callGetFileByNameFirstFilesFileConnectionNodesFile: "%v"`, tn.TypeName)
	}
}

func __marshalcallGetFileByNameFirstFilesFileConnectionNodesFile(v *callGetFileByNameFirstFilesFileConnectionNodesFile) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *callGetFileByNameFirstFilesFileConnectionNodesExternalVideo:
		typename = "ExternalVideo"

		result := struct {
			TypeName string `json:"__typename"`
			*callGetFileByNameFirstFilesFileConnectionNodesExternalVideo
		}{typename, v}
		return json.Marshal(result)
	case *callGetFileByNameFirstFilesFileConnectionNodesGenericFile:
		typename = "GenericFile"

		result := struct {
			TypeName string `json:"__typename"`
			*callGetFileByNameFirstFilesFileConnectionNodesGenericFile
		}{typename, v}
		return json.Marshal(result)
	case *callGetFileByNameFirstFilesFileConnectionNodesMediaImage:
		typename = "MediaImage"

		result := struct {
			TypeName string `json:"__typename"`
			*callGetFileByNameFirstFilesFileConnectionNodesMediaImage
		}{typename, v}
		return json.Marshal(result)
	case *callGetFileByNameFirstFilesFileConnectionNodesModel3d:
		typename = "Model3d"

		result := struct {
			TypeName string `json:"__typename"`
			*callGetFileByNameFirstFilesFileConnectionNodesModel3d
		}{typename, v}
		return json.Marshal(result)
	case *callGetFileByNameFirstFilesFileConnectionNodesVideo:
		typename = "Video"

		result := struct {
			TypeName string `json:"__typename"`
			*callGetFileByNameFirstFilesFileConnectionNodesVideo
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for callGetFileByNameFirstFilesFileConnectionNodesFile: "%T"`, v)
	}
}

// callGetFileByNameFirstFilesFileConnectionNodesGenericFile includes the requested fields of the GraphQL type GenericFile.
// The GraphQL type's documentation follows.
//
// Represents any file other than HTML.
type callGetFileByNameFirstFilesFileConnectionNodesGenericFile struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns callGetFileByNameFirstFilesFileConnectionNodesGenericFile.Typename, and is useful for accessing the field via an interface.
func (v *callGetFileByNameFirstFilesFileConnectionNodesGenericFile) GetTypename() *string {
	return v.Typename
}

// callGetFileByNameFirstFilesFileConnectionNodesMediaImage includes the requested fields of the GraphQL type MediaImage.
// The GraphQL type's documentation follows.
//
// The `MediaImage` object represents an image hosted on Shopify's
// [content delivery network (CDN)](https://shopify.dev/docs/storefronts/themes/best-practices/performance/platform#shopify-cdn).
// Shopify CDN is a content system that serves as the primary way to store,
// manage, and deliver visual content for products, variants, and other resources across the Shopify platform.
//
// The `MediaImage` object provides information to:
//
// - Store and display product and variant images across online stores, admin interfaces, and mobile apps.
// - Retrieve visual branding elements, including logos, banners, favicons, and background images in checkout flows.
// - Retrieve signed URLs for secure, time-limited access to original image files.
//
// Each `MediaImage` object provides both the processed image data (with automatic optimization and CDN delivery)
// and access to the original source file. The image processing is handled asynchronously, so images
// might not be immediately available after upload. The
// [`status`](https://shopify.dev/docs/api/admin-graphql/latest/objects/mediaimage#field-MediaImage.fields.status)
// field indicates when processing is complete and the image is ready for use.
//
// The `MediaImage` object implements the [`Media`](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/Media)
// interface alongside other media types, like videos and 3D models.
//
// Learn about
// managing media for [products](https://shopify.dev/docs/apps/build/online-store/product-media),
// [product variants](https://shopify.dev/docs/apps/build/online-store/product-variant-media), and
// [asynchronous media management](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components#asynchronous-media-management).
type callGetFileByNameFirstFilesFileConnectionNodesMediaImage struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// The image for the media. Returns `null` until `status` is `READY`.
	Image *callGetFileByNameFirstFilesFileConnectionNodesMediaImageImage `json:"image"`
}

// GetTypename returns callGetFileByNameFirstFilesFileConnectionNodesMediaImage.Typename, and is useful for accessing the field via an interface.
func (v *callGetFileByNameFirstFilesFileConnectionNodesMediaImage) GetTypename() *string {
	return v.Typename
}

// GetId returns callGetFileByNameFirstFilesFileConnectionNodesMediaImage.Id, and is useful for accessing the field via an interface.
func (v *callGetFileByNameFirstFilesFileConnectionNodesMediaImage) GetId() string { return v.Id }

// GetImage returns callGetFileByNameFirstFilesFileConnectionNodesMediaImage.Image, and is useful for accessing the field via an interface.
func (v *callGetFileByNameFirstFilesFileConnectionNodesMediaImage) GetImage() *callGetFileByNameFirstFilesFileConnectionNodesMediaImageImage {
	return v.Image
}

// callGetFileByNameFirstFilesFileConnectionNodesMediaImageImage includes the requested fields of the GraphQL type Image.
// The GraphQL type's documentation follows.
//
// Represents an image resource.
type callGetFileByNameFirstFilesFileConnectionNodesMediaImageImage struct {
	// The location of the image as a URL.
	//
	// If no transform options are specified, then the original image will be preserved including any pre-applied transforms.
	//
	// All transformation options are considered "best-effort". Any transformation
	// that the original image type doesn't support will be ignored.
	//
	// If you need multiple variations of the same image, then you can use [GraphQL
	// aliases](https://graphql.org/learn/queries/#aliases).
	Url string `json:"url"`
}

// GetUrl returns callGetFileByNameFirstFilesFileConnectionNodesMediaImageImage.Url, and is useful for accessing the field via an interface.
func (v *callGetFileByNameFirstFilesFileConnectionNodesMediaImageImage) GetUrl() string { return v.Url }

// callGetFileByNameFirstFilesFileConnectionNodesModel3d includes the requested fields of the GraphQL type Model3d.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted 3D model.
type callGetFileByNameFirstFilesFileConnectionNodesModel3d struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns callGetFileByNameFirstFilesFileConnectionNodesModel3d.Typename, and is useful for accessing the field via an interface.
func (v *callGetFileByNameFirstFilesFileConnectionNodesModel3d) GetTypename() *string {
	return v.Typename
}

// callGetFileByNameFirstFilesFileConnectionNodesVideo includes the requested fields of the GraphQL type Video.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted video.
type callGetFileByNameFirstFilesFileConnectionNodesVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns callGetFileByNameFirstFilesFileConnectionNodesVideo.Typename, and is useful for accessing the field via an interface.
func (v *callGetFileByNameFirstFilesFileConnectionNodesVideo) GetTypename() *string {
	return v.Typename
}

// callGetFileByNameFirstFilesFileConnectionPageInfo includes the requested fields of the GraphQL type PageInfo.
// The GraphQL type's documentation follows.
//
// Returns information about pagination in a connection, in accordance with the
// [Relay specification](https://relay.dev/graphql/connections.htm#sec-undefined.PageInfo).
// For more information, please read our [GraphQL Pagination Usage Guide](https://shopify.dev/api/usage/pagination-graphql).
type callGetFileByNameFirstFilesFileConnectionPageInfo struct {
	// The cursor corresponding to the last node in edges.
	EndCursor *string `json:"endCursor"`
}

// GetEndCursor returns callGetFileByNameFirstFilesFileConnectionPageInfo.EndCursor, and is useful for accessing the field via an interface.
func (v *callGetFileByNameFirstFilesFileConnectionPageInfo) GetEndCursor() *string {
	return v.EndCursor
}

// callGetFileByNameFirstResponse is returned by callGetFileByNameFirst on success.
type callGetFileByNameFirstResponse struct {
	// Retrieves a paginated list of files that have been uploaded to a Shopify store. Files represent digital assets
	// that merchants can upload to their store for various purposes including product images, marketing materials,
	// documents, and brand assets.
	//
	// Use the `files` query to retrieve information associated with the following workflows:
	//
	// - [Managing product media and images](https://shopify.dev/docs/apps/build/online-store/product-media)
	// - [Theme development and asset management](https://shopify.dev/docs/storefronts/themes/store/success/brand-assets)
	// - Brand asset management and [checkout branding](https://shopify.dev/docs/apps/build/checkout/styling/add-favicon)
	//
	// Files can include multiple [content types](https://shopify.dev/docs/api/admin-graphql/latest/enums/FileContentType),
	// such as images, videos, 3D models, and generic files. Each file has
	// properties like dimensions, file size, alt text for accessibility, and upload status. Files can be filtered
	// by [media type](https://shopify.dev/docs/api/admin-graphql/latest/enums/MediaContentType) and can be associated with
	// [products](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product),
	// [themes](https://shopify.dev/docs/api/admin-graphql/latest/objects/OnlineStoreTheme),
	// and other store resources.
	Files callGetFileByNameFirstFilesFileConnection `json:"files"`
}

// GetFiles returns callGetFileByNameFirstResponse.Files, and is useful for accessing the field via an interface.
func (v *callGetFileByNameFirstResponse) GetFiles() callGetFileByNameFirstFilesFileConnection {
	return v.Files
}

// callGetFileByNameNextFilesFileConnection includes the requested fields of the GraphQL type FileConnection.
// The GraphQL type's documentation follows.
//
// An auto-generated type for paginating through multiple Files.
type callGetFileByNameNextFilesFileConnection struct {
	// A list of nodes that are contained in FileEdge. You can fetch data about an
	// individual node, or you can follow the edges to fetch data about a collection
	// of related nodes. At each node, you specify the fields that you want to retrieve.
	Nodes []callGetFileByNameNextFilesFileConnectionNodesFile `json:"-"`
	// An object that’s used to retrieve [cursor
	// information](https://shopify.dev/api/usage/pagination-graphql) about the current page.
	PageInfo callGetFileByNameNextFilesFileConnectionPageInfo `json:"pageInfo"`
}

// GetNodes returns callGetFileByNameNextFilesFileConnection.Nodes, and is useful for accessing the field via an interface.
func (v *callGetFileByNameNextFilesFileConnection) GetNodes() []callGetFileByNameNextFilesFileConnectionNodesFile {
	return v.Nodes
}

// GetPageInfo returns callGetFileByNameNextFilesFileConnection.PageInfo, and is useful for accessing the field via an interface.
func (v *callGetFileByNameNextFilesFileConnection) GetPageInfo() callGetFileByNameNextFilesFileConnectionPageInfo {
	return v.PageInfo
}

func (v *callGetFileByNameNextFilesFileConnection) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*callGetFileByNameNextFilesFileConnection
		Nodes []json.RawMessage `json:"nodes"`
		graphql.NoUnmarshalJSON
	}
	firstPass.callGetFileByNameNextFilesFileConnection = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Nodes
		src := firstPass.Nodes
		*dst = make(
			[]callGetFileByNameNextFilesFileConnectionNodesFile,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			if len(src) != 0 && string(src) != "null" {
				err = __unmarshalcallGetFileByNameNextFilesFileConnectionNodesFile(
					src, dst)
				if err != nil {
					return fmt.Errorf(
						"unable to unmarshal callGetFileByNameNextFilesFileConnection.Nodes: %w", err)
				}
			}
		}
	}
	return nil
}

type __premarshalcallGetFileByNameNextFilesFileConnection struct {
	Nodes []json.RawMessage `json:"nodes"`

	PageInfo callGetFileByNameNextFilesFileConnectionPageInfo `json:"pageInfo"`
}

func (v *callGetFileByNameNextFilesFileConnection) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *callGetFileByNameNextFilesFileConnection) __premarshalJSON() (*__premarshalcallGetFileByNameNextFilesFileConnection, error) {
	var retval __premarshalcallGetFileByNameNextFilesFileConnection

	{

		dst := &retval.Nodes
		src := v.Nodes
		*dst = make(
			[]json.RawMessage,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			var err error
			*dst, err = __marshalcallGetFileByNameNextFilesFileConnectionNodesFile(
				&src)
			if err != nil {
				return nil, fmt.Errorf(
					"unable to marshal callGetFileByNameNextFilesFileConnection.Nodes: %w", err)
			}
		}
	}
	retval.PageInfo = v.PageInfo
	return &retval, nil
}

// callGetFileByNameNextFilesFileConnectionNodesExternalVideo includes the requested fields of the GraphQL type ExternalVideo.
// The GraphQL type's documentation follows.
//
// Represents a video hosted outside of Shopify.
type callGetFileByNameNextFilesFileConnectionNodesExternalVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns callGetFileByNameNextFilesFileConnectionNodesExternalVideo.Typename, and is useful for accessing the field via an interface.
func (v *callGetFileByNameNextFilesFileConnectionNodesExternalVideo) GetTypename() *string {
	return v.Typename
}

// callGetFileByNameNextFilesFileConnectionNodesFile includes the requested fields of the GraphQL interface File.
//
// callGetFileByNameNextFilesFileConnectionNodesFile is implemented by the following types:
// callGetFileByNameNextFilesFileConnectionNodesExternalVideo
// callGetFileByNameNextFilesFileConnectionNodesGenericFile
// callGetFileByNameNextFilesFileConnectionNodesMediaImage
// callGetFileByNameNextFilesFileConnectionNodesModel3d
// callGetFileByNameNextFilesFileConnectionNodesVideo
// The GraphQL type's documentation follows.
//
// A file interface.
type callGetFileByNameNextFilesFileConnectionNodesFile interface {
	implementsGraphQLInterfacecallGetFileByNameNextFilesFileConnectionNodesFile()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() *string
}

func (v *callGetFileByNameNextFilesFileConnectionNodesExternalVideo) implementsGraphQLInterfacecallGetFileByNameNextFilesFileConnectionNodesFile() {
}
func (v *callGetFileByNameNextFilesFileConnectionNodesGenericFile) implementsGraphQLInterfacecallGetFileByNameNextFilesFileConnectionNodesFile() {
}
func (v *callGetFileByNameNextFilesFileConnectionNodesMediaImage) implementsGraphQLInterfacecallGetFileByNameNextFilesFileConnectionNodesFile() {
}
func (v *callGetFileByNameNextFilesFileConnectionNodesModel3d) implementsGraphQLInterfacecallGetFileByNameNextFilesFileConnectionNodesFile() {
}
func (v *callGetFileByNameNextFilesFileConnectionNodesVideo) implementsGraphQLInterfacecallGetFileByNameNextFilesFileConnectionNodesFile() {
}

func __unmarshalcallGetFileByNameNextFilesFileConnectionNodesFile(b []byte, v *callGetFileByNameNextFilesFileConnectionNodesFile) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "ExternalVideo":
		*v = new(callGetFileByNameNextFilesFileConnectionNodesExternalVideo)
		return json.Unmarshal(b, *v)
	case "GenericFile":
		*v = new(callGetFileByNameNextFilesFileConnectionNodesGenericFile)
		return json.Unmarshal(b, *v)
	case "MediaImage":
		*v = new(callGetFileByNameNextFilesFileConnectionNodesMediaImage)
		return json.Unmarshal(b, *v)
	case "Model3d":
		*v = new(callGetFileByNameNextFilesFileConnectionNodesModel3d)
		return json.Unmarshal(b, *v)
	case "Video":
		*v = new(callGetFileByNameNextFilesFileConnectionNodesVideo)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing File.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for callGetFileByNameNextFilesFileConnectionNodesFile: "%v"`, tn.TypeName)
	}
}

func __marshalcallGetFileByNameNextFilesFileConnectionNodesFile(v *callGetFileByNameNextFilesFileConnectionNodesFile) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *callGetFileByNameNextFilesFileConnectionNodesExternalVideo:
		typename = "ExternalVideo"

		result := struct {
			TypeName string `json:"__typename"`
			*callGetFileByNameNextFilesFileConnectionNodesExternalVideo
		}{typename, v}
		return json.Marshal(result)
	case *callGetFileByNameNextFilesFileConnectionNodesGenericFile:
		typename = "GenericFile"

		result := struct {
			TypeName string `json:"__typename"`
			*callGetFileByNameNextFilesFileConnectionNodesGenericFile
		}{typename, v}
		return json.Marshal(result)
	case *callGetFileByNameNextFilesFileConnectionNodesMediaImage:
		typename = "MediaImage"

		result := struct {
			TypeName string `json:"__typename"`
			*callGetFileByNameNextFilesFileConnectionNodesMediaImage
		}{typename, v}
		return json.Marshal(result)
	case *callGetFileByNameNextFilesFileConnectionNodesModel3d:
		typename = "Model3d"

		result := struct {
			TypeName string `json:"__typename"`
			*callGetFileByNameNextFilesFileConnectionNodesModel3d
		}{typename, v}
		return json.Marshal(result)
	case *callGetFileByNameNextFilesFileConnectionNodesVideo:
		typename = "Video"

		result := struct {
			TypeName string `json:"__typename"`
			*callGetFileByNameNextFilesFileConnectionNodesVideo
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for callGetFileByNameNextFilesFileConnectionNodesFile: "%T"`, v)
	}
}

// callGetFileByNameNextFilesFileConnectionNodesGenericFile includes the requested fields of the GraphQL type GenericFile.
// The GraphQL type's documentation follows.
//
// Represents any file other than HTML.
type callGetFileByNameNextFilesFileConnectionNodesGenericFile struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns callGetFileByNameNextFilesFileConnectionNodesGenericFile.Typename, and is useful for accessing the field via an interface.
func (v *callGetFileByNameNextFilesFileConnectionNodesGenericFile) GetTypename() *string {
	return v.Typename
}

// callGetFileByNameNextFilesFileConnectionNodesMediaImage includes the requested fields of the GraphQL type MediaImage.
// The GraphQL type's documentation follows.
//
// The `MediaImage` object represents an image hosted on Shopify's
// [content delivery network (CDN)](https://shopify.dev/docs/storefronts/themes/best-practices/performance/platform#shopify-cdn).
// Shopify CDN is a content system that serves as the primary way to store,
// manage, and deliver visual content for products, variants, and other resources across the Shopify platform.
//
// The `MediaImage` object provides information to:
//
// - Store and display product and variant images across online stores, admin interfaces, and mobile apps.
// - Retrieve visual branding elements, including logos, banners, favicons, and background images in checkout flows.
// - Retrieve signed URLs for secure, time-limited access to original image files.
//
// Each `MediaImage` object provides both the processed image data (with automatic optimization and CDN delivery)
// and access to the original source file. The image processing is handled asynchronously, so images
// might not be immediately available after upload. The
// [`status`](https://shopify.dev/docs/api/admin-graphql/latest/objects/mediaimage#field-MediaImage.fields.status)
// field indicates when processing is complete and the image is ready for use.
//
// The `MediaImage` object implements the [`Media`](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/Media)
// interface alongside other media types, like videos and 3D models.
//
// Learn about
// managing media for [products](https://shopify.dev/docs/apps/build/online-store/product-media),
// [product variants](https://shopify.dev/docs/apps/build/online-store/product-variant-media), and
// [asynchronous media management](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components#asynchronous-media-management).
type callGetFileByNameNextFilesFileConnectionNodesMediaImage struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// The image for the media. Returns `null` until `status` is `READY`.
	Image *callGetFileByNameNextFilesFileConnectionNodesMediaImageImage `json:"image"`
}

// GetTypename returns callGetFileByNameNextFilesFileConnectionNodesMediaImage.Typename, and is useful for accessing the field via an interface.
func (v *callGetFileByNameNextFilesFileConnectionNodesMediaImage) GetTypename() *string {
	return v.Typename
}

// GetId returns callGetFileByNameNextFilesFileConnectionNodesMediaImage.Id, and is useful for accessing the field via an interface.
func (v *callGetFileByNameNextFilesFileConnectionNodesMediaImage) GetId() string { return v.Id }

// GetImage returns callGetFileByNameNextFilesFileConnectionNodesMediaImage.Image, and is useful for accessing the field via an interface.
func (v *callGetFileByNameNextFilesFileConnectionNodesMediaImage) GetImage() *callGetFileByNameNextFilesFileConnectionNodesMediaImageImage {
	return v.Image
}

// callGetFileByNameNextFilesFileConnectionNodesMediaImageImage includes the requested fields of the GraphQL type Image.
// The GraphQL type's documentation follows.
//
// Represents an image resource.
type callGetFileByNameNextFilesFileConnectionNodesMediaImageImage struct {
	// The location of the image as a URL.
	//
	// If no transform options are specified, then the original image will be preserved including any pre-applied transforms.
	//
	// All transformation options are considered "best-effort". Any transformation
	// that the original image type doesn't support will be ignored.
	//
	// If you need multiple variations of the same image, then you can use [GraphQL
	// aliases](https://graphql.org/learn/queries/#aliases).
	Url string `json:"url"`
}

// GetUrl returns callGetFileByNameNextFilesFileConnectionNodesMediaImageImage.Url, and is useful for accessing the field via an interface.
func (v *callGetFileByNameNextFilesFileConnectionNodesMediaImageImage) GetUrl() string { return v.Url }

// callGetFileByNameNextFilesFileConnectionNodesModel3d includes the requested fields of the GraphQL type Model3d.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted 3D model.
type callGetFileByNameNextFilesFileConnectionNodesModel3d struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns callGetFileByNameNextFilesFileConnectionNodesModel3d.Typename, and is useful for accessing the field via an interface.
func (v *callGetFileByNameNextFilesFileConnectionNodesModel3d) GetTypename() *string {
	return v.Typename
}

// callGetFileByNameNextFilesFileConnectionNodesVideo includes the requested fields of the GraphQL type Video.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted video.
type callGetFileByNameNextFilesFileConnectionNodesVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns callGetFileByNameNextFilesFileConnectionNodesVideo.Typename, and is useful for accessing the field via an interface.
func (v *callGetFileByNameNextFilesFileConnectionNodesVideo) GetTypename() *string { return v.Typename }

// callGetFileByNameNextFilesFileConnectionPageInfo includes the requested fields of the GraphQL type PageInfo.
// The GraphQL type's documentation follows.
//
// Returns information about pagination in a connection, in accordance with the
// [Relay specification](https://relay.dev/graphql/connections.htm#sec-undefined.PageInfo).
// For more information, please read our [GraphQL Pagination Usage Guide](https://shopify.dev/api/usage/pagination-graphql).
type callGetFileByNameNextFilesFileConnectionPageInfo struct {
	// The cursor corresponding to the last node in edges.
	EndCursor *string `json:"endCursor"`
}

// GetEndCursor returns callGetFileByNameNextFilesFileConnectionPageInfo.EndCursor, and is useful for accessing the field via an interface.
func (v *callGetFileByNameNextFilesFileConnectionPageInfo) GetEndCursor() *string { return v.EndCursor }

// callGetFileByNameNextResponse is returned by callGetFileByNameNext on success.
type callGetFileByNameNextResponse struct {
	// Retrieves a paginated list of files that have been uploaded to a Shopify store. Files represent digital assets
	// that merchants can upload to their store for various purposes including product images, marketing materials,
	// documents, and brand assets.
	//
	// Use the `files` query to retrieve information associated with the following workflows:
	//
	// - [Managing product media and images](https://shopify.dev/docs/apps/build/online-store/product-media)
	// - [Theme development and asset management](https://shopify.dev/docs/storefronts/themes/store/success/brand-assets)
	// - Brand asset management and [checkout branding](https://shopify.dev/docs/apps/build/checkout/styling/add-favicon)
	//
	// Files can include multiple [content types](https://shopify.dev/docs/api/admin-graphql/latest/enums/FileContentType),
	// such as images, videos, 3D models, and generic files. Each file has
	// properties like dimensions, file size, alt text for accessibility, and upload status. Files can be filtered
	// by [media type](https://shopify.dev/docs/api/admin-graphql/latest/enums/MediaContentType) and can be associated with
	// [products](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product),
	// [themes](https://shopify.dev/docs/api/admin-graphql/latest/objects/OnlineStoreTheme),
	// and other store resources.
	Files callGetFileByNameNextFilesFileConnection `json:"files"`
}

// GetFiles returns callGetFileByNameNextResponse.Files, and is useful for accessing the field via an interface.
func (v *callGetFileByNameNextResponse) GetFiles() callGetFileByNameNextFilesFileConnection {
	return v.Files
}

// fileArchiveFileUpdateFileUpdatePayload includes the requested fields of the GraphQL type FileUpdatePayload.
// The GraphQL type's documentation follows.
//
// Return type for `fileUpdate` mutation.
type fileArchiveFileUpdateFileUpdatePayload struct {
	// The list of updated files.
	Files []fileArchiveFileUpdateFileUpdatePayloadFilesFile `json:"-"`
	// The list of errors that occurred from executing the mutation.
	UserErrors []fileArchiveFileUpdateFileUpdatePayloadUserErrorsFilesUserError `json:"userErrors"`
}

// GetFiles returns fileArchiveFileUpdateFileUpdatePayload.Files, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayload) GetFiles() []fileArchiveFileUpdateFileUpdatePayloadFilesFile {
	return v.Files
}

// GetUserErrors returns fileArchiveFileUpdateFileUpdatePayload.UserErrors, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayload) GetUserErrors() []fileArchiveFileUpdateFileUpdatePayloadUserErrorsFilesUserError {
	return v.UserErrors
}

func (v *fileArchiveFileUpdateFileUpdatePayload) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*fileArchiveFileUpdateFileUpdatePayload
		Files []json.RawMessage `json:"files"`
		graphql.NoUnmarshalJSON
	}
	firstPass.fileArchiveFileUpdateFileUpdatePayload = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Files
		src := firstPass.Files
		*dst = make(
			[]fileArchiveFileUpdateFileUpdatePayloadFilesFile,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			if len(src) != 0 && string(src) != "null" {
				err = __unmarshalfileArchiveFileUpdateFileUpdatePayloadFilesFile(
					src, dst)
				if err != nil {
					return fmt.Errorf(
						"unable to unmarshal fileArchiveFileUpdateFileUpdatePayload.Files: %w", err)
				}
			}
		}
	}
	return nil
}

type __premarshalfileArchiveFileUpdateFileUpdatePayload struct {
	Files []json.RawMessage `json:"files"`

	UserErrors []fileArchiveFileUpdateFileUpdatePayloadUserErrorsFilesUserError `json:"userErrors"`
}

func (v *fileArchiveFileUpdateFileUpdatePayload) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *fileArchiveFileUpdateFileUpdatePayload) __premarshalJSON() (*__premarshalfileArchiveFileUpdateFileUpdatePayload, error) {
	var retval __premarshalfileArchiveFileUpdateFileUpdatePayload

	{

		dst := &retval.Files
		src := v.Files
		*dst = make(
			[]json.RawMessage,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			var err error
			*dst, err = __marshalfileArchiveFileUpdateFileUpdatePayloadFilesFile(
				&src)
			if err != nil {
				return nil, fmt.Errorf(
					"unable to marshal fileArchiveFileUpdateFileUpdatePayload.Files: %w", err)
			}
		}
	}
	retval.UserErrors = v.UserErrors
	return &retval, nil
}

// fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo includes the requested fields of the GraphQL type ExternalVideo.
// The GraphQL type's documentation follows.
//
// Represents a video hosted outside of Shopify.
type fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// A word or phrase to describe the contents or the function of a file.
	Alt *string `json:"alt"`
	// The status of the file.
	FileStatus FileStatus `json:"fileStatus"`
}

// GetTypename returns fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo.Typename, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo) GetTypename() *string {
	return v.Typename
}

// GetId returns fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo.Id, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo) GetId() string { return v.Id }

// GetAlt returns fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo.Alt, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo) GetAlt() *string { return v.Alt }

// GetFileStatus returns fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo.FileStatus, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo) GetFileStatus() FileStatus {
	return v.FileStatus
}

// fileArchiveFileUpdateFileUpdatePayloadFilesFile includes the requested fields of the GraphQL interface File.
//
// fileArchiveFileUpdateFileUpdatePayloadFilesFile is implemented by the following types:
// fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo
// fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile
// fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage
// fileArchiveFileUpdateFileUpdatePayloadFilesModel3d
// fileArchiveFileUpdateFileUpdatePayloadFilesVideo
// The GraphQL type's documentation follows.
//
// A file interface.
type fileArchiveFileUpdateFileUpdatePayloadFilesFile interface {
	implementsGraphQLInterfacefileArchiveFileUpdateFileUpdatePayloadFilesFile()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() *string
	// GetId returns the interface-field "id" from its implementation.
	// The GraphQL interface field's documentation follows.
	//
	// A globally-unique ID.
	GetId() string
	// GetAlt returns the interface-field "alt" from its implementation.
	// The GraphQL interface field's documentation follows.
	//
	// A word or phrase to describe the contents or the function of a file.
	GetAlt() *string
	// GetFileStatus returns the interface-field "fileStatus" from its implementation.
	// The GraphQL interface field's documentation follows.
	//
	// The status of the file.
	GetFileStatus() FileStatus
}

func (v *fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo) implementsGraphQLInterfacefileArchiveFileUpdateFileUpdatePayloadFilesFile() {
}
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile) implementsGraphQLInterfacefileArchiveFileUpdateFileUpdatePayloadFilesFile() {
}
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage) implementsGraphQLInterfacefileArchiveFileUpdateFileUpdatePayloadFilesFile() {
}
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesModel3d) implementsGraphQLInterfacefileArchiveFileUpdateFileUpdatePayloadFilesFile() {
}
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesVideo) implementsGraphQLInterfacefileArchiveFileUpdateFileUpdatePayloadFilesFile() {
}

func __unmarshalfileArchiveFileUpdateFileUpdatePayloadFilesFile(b []byte, v *fileArchiveFileUpdateFileUpdatePayloadFilesFile) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "ExternalVideo":
		*v = new(fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo)
		return json.Unmarshal(b, *v)
	case "GenericFile":
		*v = new(fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile)
		return json.Unmarshal(b, *v)
	case "MediaImage":
		*v = new(fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage)
		return json.Unmarshal(b, *v)
	case "Model3d":
		*v = new(fileArchiveFileUpdateFileUpdatePayloadFilesModel3d)
		return json.Unmarshal(b, *v)
	case "Video":
		*v = new(fileArchiveFileUpdateFileUpdatePayloadFilesVideo)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing File.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for fileArchiveFileUpdateFileUpdatePayloadFilesFile: "%v"`, tn.TypeName)
	}
}

func __marshalfileArchiveFileUpdateFileUpdatePayloadFilesFile(v *fileArchiveFileUpdateFileUpdatePayloadFilesFile) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo:
		typename = "ExternalVideo"

		result := struct {
			TypeName string `json:"__typename"`
			*fileArchiveFileUpdateFileUpdatePayloadFilesExternalVideo
		}{typename, v}
		return json.Marshal(result)
	case *fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile:
		typename = "GenericFile"

		result := struct {
			TypeName string `json:"__typename"`
			*fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile
		}{typename, v}
		return json.Marshal(result)
	case *fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage:
		typename = "MediaImage"

		result := struct {
			TypeName string `json:"__typename"`
			*fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage
		}{typename, v}
		return json.Marshal(result)
	case *fileArchiveFileUpdateFileUpdatePayloadFilesModel3d:
		typename = "Model3d"

		result := struct {
			TypeName string `json:"__typename"`
			*fileArchiveFileUpdateFileUpdatePayloadFilesModel3d
		}{typename, v}
		return json.Marshal(result)
	case *fileArchiveFileUpdateFileUpdatePayloadFilesVideo:
		typename = "Video"

		result := struct {
			TypeName string `json:"__typename"`
			*fileArchiveFileUpdateFileUpdatePayloadFilesVideo
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for fileArchiveFileUpdateFileUpdatePayloadFilesFile: "%T"`, v)
	}
}

// fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile includes the requested fields of the GraphQL type GenericFile.
// The GraphQL type's documentation follows.
//
// Represents any file other than HTML.
type fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// A word or phrase to describe the contents or the function of a file.
	Alt *string `json:"alt"`
	// The status of the file.
	FileStatus FileStatus `json:"fileStatus"`
}

// GetTypename returns fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile.Typename, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile) GetTypename() *string {
	return v.Typename
}

// GetId returns fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile.Id, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile) GetId() string { return v.Id }

// GetAlt returns fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile.Alt, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile) GetAlt() *string { return v.Alt }

// GetFileStatus returns fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile.FileStatus, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesGenericFile) GetFileStatus() FileStatus {
	return v.FileStatus
}

// fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage includes the requested fields of the GraphQL type MediaImage.
// The GraphQL type's documentation follows.
//
// The `MediaImage` object represents an image hosted on Shopify's
// [content delivery network (CDN)](https://shopify.dev/docs/storefronts/themes/best-practices/performance/platform#shopify-cdn).
// Shopify CDN is a content system that serves as the primary way to store,
// manage, and deliver visual content for products, variants, and other resources across the Shopify platform.
//
// The `MediaImage` object provides information to:
//
// - Store and display product and variant images across online stores, admin interfaces, and mobile apps.
// - Retrieve visual branding elements, including logos, banners, favicons, and background images in checkout flows.
// - Retrieve signed URLs for secure, time-limited access to original image files.
//
// Each `MediaImage` object provides both the processed image data (with automatic optimization and CDN delivery)
// and access to the original source file. The image processing is handled asynchronously, so images
// might not be immediately available after upload. The
// [`status`](https://shopify.dev/docs/api/admin-graphql/latest/objects/mediaimage#field-MediaImage.fields.status)
// field indicates when processing is complete and the image is ready for use.
//
// The `MediaImage` object implements the [`Media`](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/Media)
// interface alongside other media types, like videos and 3D models.
//
// Learn about
// managing media for [products](https://shopify.dev/docs/apps/build/online-store/product-media),
// [product variants](https://shopify.dev/docs/apps/build/online-store/product-variant-media), and
// [asynchronous media management](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components#asynchronous-media-management).
type fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// A word or phrase to describe the contents or the function of a file.
	Alt *string `json:"alt"`
	// The status of the file.
	FileStatus FileStatus `json:"fileStatus"`
}

// GetTypename returns fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage.Typename, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage) GetTypename() *string {
	return v.Typename
}

// GetId returns fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage.Id, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage) GetId() string { return v.Id }

// GetAlt returns fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage.Alt, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage) GetAlt() *string { return v.Alt }

// GetFileStatus returns fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage.FileStatus, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesMediaImage) GetFileStatus() FileStatus {
	return v.FileStatus
}

// fileArchiveFileUpdateFileUpdatePayloadFilesModel3d includes the requested fields of the GraphQL type Model3d.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted 3D model.
type fileArchiveFileUpdateFileUpdatePayloadFilesModel3d struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// A word or phrase to describe the contents or the function of a file.
	Alt *string `json:"alt"`
	// The status of the file.
	FileStatus FileStatus `json:"fileStatus"`
}

// GetTypename returns fileArchiveFileUpdateFileUpdatePayloadFilesModel3d.Typename, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesModel3d) GetTypename() *string { return v.Typename }

// GetId returns fileArchiveFileUpdateFileUpdatePayloadFilesModel3d.Id, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesModel3d) GetId() string { return v.Id }

// GetAlt returns fileArchiveFileUpdateFileUpdatePayloadFilesModel3d.Alt, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesModel3d) GetAlt() *string { return v.Alt }

// GetFileStatus returns fileArchiveFileUpdateFileUpdatePayloadFilesModel3d.FileStatus, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesModel3d) GetFileStatus() FileStatus {
	return v.FileStatus
}

// fileArchiveFileUpdateFileUpdatePayloadFilesVideo includes the requested fields of the GraphQL type Video.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted video.
type fileArchiveFileUpdateFileUpdatePayloadFilesVideo struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// A word or phrase to describe the contents or the function of a file.
	Alt *string `json:"alt"`
	// The status of the file.
	FileStatus FileStatus `json:"fileStatus"`
}

// GetTypename returns fileArchiveFileUpdateFileUpdatePayloadFilesVideo.Typename, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesVideo) GetTypename() *string { return v.Typename }

// GetId returns fileArchiveFileUpdateFileUpdatePayloadFilesVideo.Id, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesVideo) GetId() string { return v.Id }

// GetAlt returns fileArchiveFileUpdateFileUpdatePayloadFilesVideo.Alt, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesVideo) GetAlt() *string { return v.Alt }

// GetFileStatus returns fileArchiveFileUpdateFileUpdatePayloadFilesVideo.FileStatus, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadFilesVideo) GetFileStatus() FileStatus {
	return v.FileStatus
}

// fileArchiveFileUpdateFileUpdatePayloadUserErrorsFilesUserError includes the requested fields of the GraphQL type FilesUserError.
// The GraphQL type's documentation follows.
//
// An error that happens during the execution of a Files API query or mutation.
type fileArchiveFileUpdateFileUpdatePayloadUserErrorsFilesUserError struct {
	// The error message.
	Message string `json:"message"`
}

// GetMessage returns fileArchiveFileUpdateFileUpdatePayloadUserErrorsFilesUserError.Message, and is useful for accessing the field via an interface.
func (v *fileArchiveFileUpdateFileUpdatePayloadUserErrorsFilesUserError) GetMessage() string {
	return v.Message
}

// fileArchiveResponse is returned by fileArchive on success.
type fileArchiveResponse struct {
	// Updates properties, content, and metadata associated with an existing file
	// asset that has already been uploaded to Shopify.
	//
	// Use the `fileUpdate` mutation to modify various aspects of files already stored in your store.
	// Files can be updated individually or in batches.
	//
	// The `fileUpdate` mutation supports updating multiple file properties:
	//
	// - **Alt text**: Update accessibility descriptions for images and other media.
	// - **File content**: Replace image or generic file content while maintaining the same URL.
	// - **Filename**: Modify file names (extension must match the original).
	// - **Product references**: Add or remove associations between files and products. Removing file-product associations
	// deletes the file from the product's media gallery and clears the image from any product variants that were using it.
	//
	// The mutation handles different file types with specific capabilities:
	//
	// - **Images**: Update preview images, original source, filename, and alt text.
	// - **Generic files**: Update original source, filename, and alt text.
	// - **Videos and 3D models**: Update alt text and product references.
	//
	// > Note:
	// > Files must be in `ready` state before they can be updated. The mutation includes file locking to prevent
	// > conflicts during updates. You can't simultaneously update both `originalSource` and `previewImageSource`.
	//
	// After updating files, you can use related mutations for additional file management:
	//
	// - [`fileCreate`](https://shopify.dev/docs/api/admin-graphql/latest/mutations/fileCreate):
	// Create new file assets from external URLs or staged uploads.
	// - [`fileDelete`](https://shopify.dev/docs/api/admin-graphql/latest/mutations/fileDelete):
	// Remove files from your store when they are no longer needed.
	//
	// Learn how to manage
	// [product media and file assets](https://shopify.dev/docs/apps/build/online-store/product-media)
	// in your app.
	FileUpdate *fileArchiveFileUpdateFileUpdatePayload `json:"fileUpdate"`
}

// GetFileUpdate returns fileArchiveResponse.FileUpdate, and is useful for accessing the field via an interface.
func (v *fileArchiveResponse) GetFileUpdate() *fileArchiveFileUpdateFileUpdatePayload {
	return v.FileUpdate
}

// fileDeleteFileDeleteFileDeletePayload includes the requested fields of the GraphQL type FileDeletePayload.
// The GraphQL type's documentation follows.
//
// Return type for `fileDelete` mutation.
type fileDeleteFileDeleteFileDeletePayload struct {
	// The IDs of the deleted files.
	DeletedFileIds []string `json:"deletedFileIds"`
}

// GetDeletedFileIds returns fileDeleteFileDeleteFileDeletePayload.DeletedFileIds, and is useful for accessing the field via an interface.
func (v *fileDeleteFileDeleteFileDeletePayload) GetDeletedFileIds() []string { return v.DeletedFileIds }

// fileDeleteResponse is returned by fileDelete on success.
type fileDeleteResponse struct {
	// Deletes file assets that were previously uploaded to your store.
	//
	// Use the `fileDelete` mutation to permanently remove media and file assets from your store when they are no longer needed.
	// This mutation handles the complete removal of files from both your store's file library and any associated references
	// to products or other resources.
	//
	// The `fileDelete` mutation supports removal of multiple file types:
	//
	// - **Images**: Product photos, variant images, and general store imagery
	// - **Videos**: Shopify-hosted videos for product demonstrations and marketing content
	// - **External Videos**: YouTube and Vimeo videos linked to your products
	// - **3D models**: Interactive 3D representations of products
	// - **Generic files**: PDFs, documents, and other file types stored in your
	// [**Files** page](https://shopify.com/admin/settings/files)
	//
	// When you delete files that are referenced by products, the mutation automatically removes those references and
	// reorders any remaining media to maintain proper positioning. Product file references are database relationships
	// managed through a media reference system, not just links in product descriptions. The Shopify admin provides a UI
	// to manage these relationships, and when files are deleted, the system automatically cleans up all references.
	// Files that are currently being processed by other operations are rejected to prevent conflicts.
	//
	// > Caution:
	// > File deletion is permanent and can't be undone. When you delete a file that's being used in your store,
	// > it will immediately stop appearing wherever it was displayed. For example, if you delete a product image,
	// > that product will show a broken image or placeholder on your storefront and in the admin. The same applies
	// > to any other files linked from themes, blog posts, or pages. Before deleting files, you can use the
	// > [`files` query](https://shopify.dev/api/admin-graphql/latest/queries/files) to list and review
	// > your store's file assets.
	//
	// Learn how to manage
	// [product media and file assets](https://shopify.dev/docs/apps/build/online-store/product-media)
	// in your app.
	FileDelete *fileDeleteFileDeleteFileDeletePayload `json:"fileDelete"`
}

// GetFileDelete returns fileDeleteResponse.FileDelete, and is useful for accessing the field via an interface.
func (v *fileDeleteResponse) GetFileDelete() *fileDeleteFileDeleteFileDeletePayload {
	return v.FileDelete
}

// findImageByIdNode includes the requested fields of the GraphQL interface Node.
//
// findImageByIdNode is implemented by the following types:
// findImageByIdNodeAbandonedCheckout
// findImageByIdNodeAbandonedCheckoutLineItem
// findImageByIdNodeAbandonment
// findImageByIdNodeAddAllProductsOperation
// findImageByIdNodeAdditionalFee
// findImageByIdNodeApp
// findImageByIdNodeAppCatalog
// findImageByIdNodeAppCredit
// findImageByIdNodeAppInstallation
// findImageByIdNodeAppPurchaseOneTime
// findImageByIdNodeAppRevenueAttributionRecord
// findImageByIdNodeAppSubscription
// findImageByIdNodeAppUsageRecord
// findImageByIdNodeArticle
// findImageByIdNodeBasicEvent
// findImageByIdNodeBlog
// findImageByIdNodeBulkOperation
// findImageByIdNodeBusinessEntity
// findImageByIdNodeCalculatedOrder
// findImageByIdNodeCartTransform
// findImageByIdNodeCashTrackingAdjustment
// findImageByIdNodeCashTrackingSession
// findImageByIdNodeCatalogCsvOperation
// findImageByIdNodeChannel
// findImageByIdNodeChannelDefinition
// findImageByIdNodeChannelInformation
// findImageByIdNodeCheckoutProfile
// findImageByIdNodeCollection
// findImageByIdNodeComment
// findImageByIdNodeCommentEvent
// findImageByIdNodeCompany
// findImageByIdNodeCompanyAddress
// findImageByIdNodeCompanyContact
// findImageByIdNodeCompanyContactRole
// findImageByIdNodeCompanyContactRoleAssignment
// findImageByIdNodeCompanyLocation
// findImageByIdNodeCompanyLocationCatalog
// findImageByIdNodeCompanyLocationStaffMemberAssignment
// findImageByIdNodeConsentPolicy
// findImageByIdNodeCurrencyExchangeAdjustment
// findImageByIdNodeCustomer
// findImageByIdNodeCustomerAccountAppExtensionPage
// findImageByIdNodeCustomerAccountNativePage
// findImageByIdNodeCustomerPaymentMethod
// findImageByIdNodeCustomerSegmentMembersQuery
// findImageByIdNodeCustomerVisit
// findImageByIdNodeDeliveryCarrierService
// findImageByIdNodeDeliveryCondition
// findImageByIdNodeDeliveryCountry
// findImageByIdNodeDeliveryCustomization
// findImageByIdNodeDeliveryLocationGroup
// findImageByIdNodeDeliveryMethod
// findImageByIdNodeDeliveryMethodDefinition
// findImageByIdNodeDeliveryParticipant
// findImageByIdNodeDeliveryProfile
// findImageByIdNodeDeliveryProfileItem
// findImageByIdNodeDeliveryPromiseParticipant
// findImageByIdNodeDeliveryPromiseProvider
// findImageByIdNodeDeliveryProvince
// findImageByIdNodeDeliveryRateDefinition
// findImageByIdNodeDeliveryZone
// findImageByIdNodeDiscountAutomaticBxgy
// findImageByIdNodeDiscountAutomaticNode
// findImageByIdNodeDiscountCodeNode
// findImageByIdNodeDiscountNode
// findImageByIdNodeDiscountRedeemCodeBulkCreation
// findImageByIdNodeDomain
// findImageByIdNodeDraftOrder
// findImageByIdNodeDraftOrderLineItem
// findImageByIdNodeDraftOrderTag
// findImageByIdNodeDuty
// findImageByIdNodeExchangeLineItem
// findImageByIdNodeExchangeV2
// findImageByIdNodeExternalVideo
// findImageByIdNodeFulfillment
// findImageByIdNodeFulfillmentConstraintRule
// findImageByIdNodeFulfillmentEvent
// findImageByIdNodeFulfillmentHold
// findImageByIdNodeFulfillmentLineItem
// findImageByIdNodeFulfillmentOrder
// findImageByIdNodeFulfillmentOrderDestination
// findImageByIdNodeFulfillmentOrderLineItem
// findImageByIdNodeFulfillmentOrderMerchantRequest
// findImageByIdNodeGenericFile
// findImageByIdNodeGiftCard
// findImageByIdNodeGiftCardCreditTransaction
// findImageByIdNodeGiftCardDebitTransaction
// findImageByIdNodeInventoryAdjustmentGroup
// findImageByIdNodeInventoryItem
// findImageByIdNodeInventoryItemMeasurement
// findImageByIdNodeInventoryLevel
// findImageByIdNodeInventoryQuantity
// findImageByIdNodeInventoryShipment
// findImageByIdNodeInventoryShipmentLineItem
// findImageByIdNodeInventoryTransfer
// findImageByIdNodeInventoryTransferLineItem
// findImageByIdNodeLineItem
// findImageByIdNodeLineItemGroup
// findImageByIdNodeLocation
// findImageByIdNodeMailingAddress
// findImageByIdNodeMarket
// findImageByIdNodeMarketCatalog
// findImageByIdNodeMarketRegionCountry
// findImageByIdNodeMarketWebPresence
// findImageByIdNodeMarketingActivity
// findImageByIdNodeMarketingEvent
// findImageByIdNodeMediaImage
// findImageByIdNodeMenu
// findImageByIdNodeMetafield
// findImageByIdNodeMetafieldDefinition
// findImageByIdNodeMetaobject
// findImageByIdNodeMetaobjectDefinition
// findImageByIdNodeModel3d
// findImageByIdNodeOnlineStoreTheme
// findImageByIdNodeOrder
// findImageByIdNodeOrderAdjustment
// findImageByIdNodeOrderDisputeSummary
// findImageByIdNodeOrderEditSession
// findImageByIdNodeOrderTransaction
// findImageByIdNodePage
// findImageByIdNodePaymentCustomization
// findImageByIdNodePaymentMandate
// findImageByIdNodePaymentSchedule
// findImageByIdNodePaymentTerms
// findImageByIdNodePaymentTermsTemplate
// findImageByIdNodePointOfSaleDevice
// findImageByIdNodePriceList
// findImageByIdNodePriceRule
// findImageByIdNodePriceRuleDiscountCode
// findImageByIdNodeProduct
// findImageByIdNodeProductBundleOperation
// findImageByIdNodeProductDeleteOperation
// findImageByIdNodeProductDuplicateOperation
// findImageByIdNodeProductFeed
// findImageByIdNodeProductOption
// findImageByIdNodeProductOptionValue
// findImageByIdNodeProductSetOperation
// findImageByIdNodeProductTaxonomyNode
// findImageByIdNodeProductVariant
// findImageByIdNodeProductVariantComponent
// findImageByIdNodePublication
// findImageByIdNodePublicationResourceOperation
// findImageByIdNodeQuantityPriceBreak
// findImageByIdNodeRefund
// findImageByIdNodeRefundShippingLine
// findImageByIdNodeReturn
// findImageByIdNodeReturnLineItem
// findImageByIdNodeReturnableFulfillment
// findImageByIdNodeReverseDelivery
// findImageByIdNodeReverseDeliveryLineItem
// findImageByIdNodeReverseFulfillmentOrder
// findImageByIdNodeReverseFulfillmentOrderDisposition
// findImageByIdNodeReverseFulfillmentOrderLineItem
// findImageByIdNodeSaleAdditionalFee
// findImageByIdNodeSavedSearch
// findImageByIdNodeScriptTag
// findImageByIdNodeSegment
// findImageByIdNodeSellingPlan
// findImageByIdNodeSellingPlanGroup
// findImageByIdNodeServerPixel
// findImageByIdNodeShop
// findImageByIdNodeShopAddress
// findImageByIdNodeShopPolicy
// findImageByIdNodeShopifyPaymentsAccount
// findImageByIdNodeShopifyPaymentsBalanceTransaction
// findImageByIdNodeShopifyPaymentsBankAccount
// findImageByIdNodeShopifyPaymentsDispute
// findImageByIdNodeShopifyPaymentsDisputeEvidence
// findImageByIdNodeShopifyPaymentsDisputeFileUpload
// findImageByIdNodeShopifyPaymentsDisputeFulfillment
// findImageByIdNodeShopifyPaymentsPayout
// findImageByIdNodeShopifyPaymentsVerification
// findImageByIdNodeStaffMember
// findImageByIdNodeStandardMetafieldDefinitionTemplate
// findImageByIdNodeStoreCreditAccount
// findImageByIdNodeStoreCreditAccountCreditTransaction
// findImageByIdNodeStoreCreditAccountDebitRevertTransaction
// findImageByIdNodeStoreCreditAccountDebitTransaction
// findImageByIdNodeStorefrontAccessToken
// findImageByIdNodeSubscriptionBillingAttempt
// findImageByIdNodeSubscriptionContract
// findImageByIdNodeSubscriptionDraft
// findImageByIdNodeTaxonomyAttribute
// findImageByIdNodeTaxonomyCategory
// findImageByIdNodeTaxonomyChoiceListAttribute
// findImageByIdNodeTaxonomyMeasurementAttribute
// findImageByIdNodeTaxonomyValue
// findImageByIdNodeTenderTransaction
// findImageByIdNodeTransactionFee
// findImageByIdNodeUnverifiedReturnLineItem
// findImageByIdNodeUrlRedirect
// findImageByIdNodeUrlRedirectImport
// findImageByIdNodeValidation
// findImageByIdNodeVideo
// findImageByIdNodeWebPixel
// findImageByIdNodeWebPresence
// findImageByIdNodeWebhookSubscription
// The GraphQL type's documentation follows.
//
// An object with an ID field to support global identification, in accordance with the
// [Relay specification](https://relay.dev/graphql/objectidentification.htm#sec-Node-Interface).
// This interface is used by the [node](https://shopify.dev/api/admin-graphql/unstable/queries/node)
// and [nodes](https://shopify.dev/api/admin-graphql/unstable/queries/nodes) queries.
type findImageByIdNode interface {
	implementsGraphQLInterfacefindImageByIdNode()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() *string
}

func (v *findImageByIdNodeAbandonedCheckout) implementsGraphQLInterfacefindImageByIdNode()         {}
func (v *findImageByIdNodeAbandonedCheckoutLineItem) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeAbandonment) implementsGraphQLInterfacefindImageByIdNode()               {}
func (v *findImageByIdNodeAddAllProductsOperation) implementsGraphQLInterfacefindImageByIdNode()   {}
func (v *findImageByIdNodeAdditionalFee) implementsGraphQLInterfacefindImageByIdNode()             {}
func (v *findImageByIdNodeApp) implementsGraphQLInterfacefindImageByIdNode()                       {}
func (v *findImageByIdNodeAppCatalog) implementsGraphQLInterfacefindImageByIdNode()                {}
func (v *findImageByIdNodeAppCredit) implementsGraphQLInterfacefindImageByIdNode()                 {}
func (v *findImageByIdNodeAppInstallation) implementsGraphQLInterfacefindImageByIdNode()           {}
func (v *findImageByIdNodeAppPurchaseOneTime) implementsGraphQLInterfacefindImageByIdNode()        {}
func (v *findImageByIdNodeAppRevenueAttributionRecord) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeAppSubscription) implementsGraphQLInterfacefindImageByIdNode()        {}
func (v *findImageByIdNodeAppUsageRecord) implementsGraphQLInterfacefindImageByIdNode()         {}
func (v *findImageByIdNodeArticle) implementsGraphQLInterfacefindImageByIdNode()                {}
func (v *findImageByIdNodeBasicEvent) implementsGraphQLInterfacefindImageByIdNode()             {}
func (v *findImageByIdNodeBlog) implementsGraphQLInterfacefindImageByIdNode()                   {}
func (v *findImageByIdNodeBulkOperation) implementsGraphQLInterfacefindImageByIdNode()          {}
func (v *findImageByIdNodeBusinessEntity) implementsGraphQLInterfacefindImageByIdNode()         {}
func (v *findImageByIdNodeCalculatedOrder) implementsGraphQLInterfacefindImageByIdNode()        {}
func (v *findImageByIdNodeCartTransform) implementsGraphQLInterfacefindImageByIdNode()          {}
func (v *findImageByIdNodeCashTrackingAdjustment) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeCashTrackingSession) implementsGraphQLInterfacefindImageByIdNode()    {}
func (v *findImageByIdNodeCatalogCsvOperation) implementsGraphQLInterfacefindImageByIdNode()    {}
func (v *findImageByIdNodeChannel) implementsGraphQLInterfacefindImageByIdNode()                {}
func (v *findImageByIdNodeChannelDefinition) implementsGraphQLInterfacefindImageByIdNode()      {}
func (v *findImageByIdNodeChannelInformation) implementsGraphQLInterfacefindImageByIdNode()     {}
func (v *findImageByIdNodeCheckoutProfile) implementsGraphQLInterfacefindImageByIdNode()        {}
func (v *findImageByIdNodeCollection) implementsGraphQLInterfacefindImageByIdNode()             {}
func (v *findImageByIdNodeComment) implementsGraphQLInterfacefindImageByIdNode()                {}
func (v *findImageByIdNodeCommentEvent) implementsGraphQLInterfacefindImageByIdNode()           {}
func (v *findImageByIdNodeCompany) implementsGraphQLInterfacefindImageByIdNode()                {}
func (v *findImageByIdNodeCompanyAddress) implementsGraphQLInterfacefindImageByIdNode()         {}
func (v *findImageByIdNodeCompanyContact) implementsGraphQLInterfacefindImageByIdNode()         {}
func (v *findImageByIdNodeCompanyContactRole) implementsGraphQLInterfacefindImageByIdNode()     {}
func (v *findImageByIdNodeCompanyContactRoleAssignment) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeCompanyLocation) implementsGraphQLInterfacefindImageByIdNode()        {}
func (v *findImageByIdNodeCompanyLocationCatalog) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeCompanyLocationStaffMemberAssignment) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeConsentPolicy) implementsGraphQLInterfacefindImageByIdNode()              {}
func (v *findImageByIdNodeCurrencyExchangeAdjustment) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeCustomer) implementsGraphQLInterfacefindImageByIdNode()                   {}
func (v *findImageByIdNodeCustomerAccountAppExtensionPage) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeCustomerAccountNativePage) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeCustomerPaymentMethod) implementsGraphQLInterfacefindImageByIdNode()     {}
func (v *findImageByIdNodeCustomerSegmentMembersQuery) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeCustomerVisit) implementsGraphQLInterfacefindImageByIdNode()              {}
func (v *findImageByIdNodeDeliveryCarrierService) implementsGraphQLInterfacefindImageByIdNode()     {}
func (v *findImageByIdNodeDeliveryCondition) implementsGraphQLInterfacefindImageByIdNode()          {}
func (v *findImageByIdNodeDeliveryCountry) implementsGraphQLInterfacefindImageByIdNode()            {}
func (v *findImageByIdNodeDeliveryCustomization) implementsGraphQLInterfacefindImageByIdNode()      {}
func (v *findImageByIdNodeDeliveryLocationGroup) implementsGraphQLInterfacefindImageByIdNode()      {}
func (v *findImageByIdNodeDeliveryMethod) implementsGraphQLInterfacefindImageByIdNode()             {}
func (v *findImageByIdNodeDeliveryMethodDefinition) implementsGraphQLInterfacefindImageByIdNode()   {}
func (v *findImageByIdNodeDeliveryParticipant) implementsGraphQLInterfacefindImageByIdNode()        {}
func (v *findImageByIdNodeDeliveryProfile) implementsGraphQLInterfacefindImageByIdNode()            {}
func (v *findImageByIdNodeDeliveryProfileItem) implementsGraphQLInterfacefindImageByIdNode()        {}
func (v *findImageByIdNodeDeliveryPromiseParticipant) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeDeliveryPromiseProvider) implementsGraphQLInterfacefindImageByIdNode()    {}
func (v *findImageByIdNodeDeliveryProvince) implementsGraphQLInterfacefindImageByIdNode()           {}
func (v *findImageByIdNodeDeliveryRateDefinition) implementsGraphQLInterfacefindImageByIdNode()     {}
func (v *findImageByIdNodeDeliveryZone) implementsGraphQLInterfacefindImageByIdNode()               {}
func (v *findImageByIdNodeDiscountAutomaticBxgy) implementsGraphQLInterfacefindImageByIdNode()      {}
func (v *findImageByIdNodeDiscountAutomaticNode) implementsGraphQLInterfacefindImageByIdNode()      {}
func (v *findImageByIdNodeDiscountCodeNode) implementsGraphQLInterfacefindImageByIdNode()           {}
func (v *findImageByIdNodeDiscountNode) implementsGraphQLInterfacefindImageByIdNode()               {}
func (v *findImageByIdNodeDiscountRedeemCodeBulkCreation) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeDomain) implementsGraphQLInterfacefindImageByIdNode()                    {}
func (v *findImageByIdNodeDraftOrder) implementsGraphQLInterfacefindImageByIdNode()                {}
func (v *findImageByIdNodeDraftOrderLineItem) implementsGraphQLInterfacefindImageByIdNode()        {}
func (v *findImageByIdNodeDraftOrderTag) implementsGraphQLInterfacefindImageByIdNode()             {}
func (v *findImageByIdNodeDuty) implementsGraphQLInterfacefindImageByIdNode()                      {}
func (v *findImageByIdNodeExchangeLineItem) implementsGraphQLInterfacefindImageByIdNode()          {}
func (v *findImageByIdNodeExchangeV2) implementsGraphQLInterfacefindImageByIdNode()                {}
func (v *findImageByIdNodeExternalVideo) implementsGraphQLInterfacefindImageByIdNode()             {}
func (v *findImageByIdNodeFulfillment) implementsGraphQLInterfacefindImageByIdNode()               {}
func (v *findImageByIdNodeFulfillmentConstraintRule) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeFulfillmentEvent) implementsGraphQLInterfacefindImageByIdNode()          {}
func (v *findImageByIdNodeFulfillmentHold) implementsGraphQLInterfacefindImageByIdNode()           {}
func (v *findImageByIdNodeFulfillmentLineItem) implementsGraphQLInterfacefindImageByIdNode()       {}
func (v *findImageByIdNodeFulfillmentOrder) implementsGraphQLInterfacefindImageByIdNode()          {}
func (v *findImageByIdNodeFulfillmentOrderDestination) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeFulfillmentOrderLineItem) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeFulfillmentOrderMerchantRequest) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeGenericFile) implementsGraphQLInterfacefindImageByIdNode()               {}
func (v *findImageByIdNodeGiftCard) implementsGraphQLInterfacefindImageByIdNode()                  {}
func (v *findImageByIdNodeGiftCardCreditTransaction) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeGiftCardDebitTransaction) implementsGraphQLInterfacefindImageByIdNode()  {}
func (v *findImageByIdNodeInventoryAdjustmentGroup) implementsGraphQLInterfacefindImageByIdNode()  {}
func (v *findImageByIdNodeInventoryItem) implementsGraphQLInterfacefindImageByIdNode()             {}
func (v *findImageByIdNodeInventoryItemMeasurement) implementsGraphQLInterfacefindImageByIdNode()  {}
func (v *findImageByIdNodeInventoryLevel) implementsGraphQLInterfacefindImageByIdNode()            {}
func (v *findImageByIdNodeInventoryQuantity) implementsGraphQLInterfacefindImageByIdNode()         {}
func (v *findImageByIdNodeInventoryShipment) implementsGraphQLInterfacefindImageByIdNode()         {}
func (v *findImageByIdNodeInventoryShipmentLineItem) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeInventoryTransfer) implementsGraphQLInterfacefindImageByIdNode()         {}
func (v *findImageByIdNodeInventoryTransferLineItem) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeLineItem) implementsGraphQLInterfacefindImageByIdNode()                  {}
func (v *findImageByIdNodeLineItemGroup) implementsGraphQLInterfacefindImageByIdNode()             {}
func (v *findImageByIdNodeLocation) implementsGraphQLInterfacefindImageByIdNode()                  {}
func (v *findImageByIdNodeMailingAddress) implementsGraphQLInterfacefindImageByIdNode()            {}
func (v *findImageByIdNodeMarket) implementsGraphQLInterfacefindImageByIdNode()                    {}
func (v *findImageByIdNodeMarketCatalog) implementsGraphQLInterfacefindImageByIdNode()             {}
func (v *findImageByIdNodeMarketRegionCountry) implementsGraphQLInterfacefindImageByIdNode()       {}
func (v *findImageByIdNodeMarketWebPresence) implementsGraphQLInterfacefindImageByIdNode()         {}
func (v *findImageByIdNodeMarketingActivity) implementsGraphQLInterfacefindImageByIdNode()         {}
func (v *findImageByIdNodeMarketingEvent) implementsGraphQLInterfacefindImageByIdNode()            {}
func (v *findImageByIdNodeMediaImage) implementsGraphQLInterfacefindImageByIdNode()                {}
func (v *findImageByIdNodeMenu) implementsGraphQLInterfacefindImageByIdNode()                      {}
func (v *findImageByIdNodeMetafield) implementsGraphQLInterfacefindImageByIdNode()                 {}
func (v *findImageByIdNodeMetafieldDefinition) implementsGraphQLInterfacefindImageByIdNode()       {}
func (v *findImageByIdNodeMetaobject) implementsGraphQLInterfacefindImageByIdNode()                {}
func (v *findImageByIdNodeMetaobjectDefinition) implementsGraphQLInterfacefindImageByIdNode()      {}
func (v *findImageByIdNodeModel3d) implementsGraphQLInterfacefindImageByIdNode()                   {}
func (v *findImageByIdNodeOnlineStoreTheme) implementsGraphQLInterfacefindImageByIdNode()          {}
func (v *findImageByIdNodeOrder) implementsGraphQLInterfacefindImageByIdNode()                     {}
func (v *findImageByIdNodeOrderAdjustment) implementsGraphQLInterfacefindImageByIdNode()           {}
func (v *findImageByIdNodeOrderDisputeSummary) implementsGraphQLInterfacefindImageByIdNode()       {}
func (v *findImageByIdNodeOrderEditSession) implementsGraphQLInterfacefindImageByIdNode()          {}
func (v *findImageByIdNodeOrderTransaction) implementsGraphQLInterfacefindImageByIdNode()          {}
func (v *findImageByIdNodePage) implementsGraphQLInterfacefindImageByIdNode()                      {}
func (v *findImageByIdNodePaymentCustomization) implementsGraphQLInterfacefindImageByIdNode()      {}
func (v *findImageByIdNodePaymentMandate) implementsGraphQLInterfacefindImageByIdNode()            {}
func (v *findImageByIdNodePaymentSchedule) implementsGraphQLInterfacefindImageByIdNode()           {}
func (v *findImageByIdNodePaymentTerms) implementsGraphQLInterfacefindImageByIdNode()              {}
func (v *findImageByIdNodePaymentTermsTemplate) implementsGraphQLInterfacefindImageByIdNode()      {}
func (v *findImageByIdNodePointOfSaleDevice) implementsGraphQLInterfacefindImageByIdNode()         {}
func (v *findImageByIdNodePriceList) implementsGraphQLInterfacefindImageByIdNode()                 {}
func (v *findImageByIdNodePriceRule) implementsGraphQLInterfacefindImageByIdNode()                 {}
func (v *findImageByIdNodePriceRuleDiscountCode) implementsGraphQLInterfacefindImageByIdNode()     {}
func (v *findImageByIdNodeProduct) implementsGraphQLInterfacefindImageByIdNode()                   {}
func (v *findImageByIdNodeProductBundleOperation) implementsGraphQLInterfacefindImageByIdNode()    {}
func (v *findImageByIdNodeProductDeleteOperation) implementsGraphQLInterfacefindImageByIdNode()    {}
func (v *findImageByIdNodeProductDuplicateOperation) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeProductFeed) implementsGraphQLInterfacefindImageByIdNode()               {}
func (v *findImageByIdNodeProductOption) implementsGraphQLInterfacefindImageByIdNode()             {}
func (v *findImageByIdNodeProductOptionValue) implementsGraphQLInterfacefindImageByIdNode()        {}
func (v *findImageByIdNodeProductSetOperation) implementsGraphQLInterfacefindImageByIdNode()       {}
func (v *findImageByIdNodeProductTaxonomyNode) implementsGraphQLInterfacefindImageByIdNode()       {}
func (v *findImageByIdNodeProductVariant) implementsGraphQLInterfacefindImageByIdNode()            {}
func (v *findImageByIdNodeProductVariantComponent) implementsGraphQLInterfacefindImageByIdNode()   {}
func (v *findImageByIdNodePublication) implementsGraphQLInterfacefindImageByIdNode()               {}
func (v *findImageByIdNodePublicationResourceOperation) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeQuantityPriceBreak) implementsGraphQLInterfacefindImageByIdNode()      {}
func (v *findImageByIdNodeRefund) implementsGraphQLInterfacefindImageByIdNode()                  {}
func (v *findImageByIdNodeRefundShippingLine) implementsGraphQLInterfacefindImageByIdNode()      {}
func (v *findImageByIdNodeReturn) implementsGraphQLInterfacefindImageByIdNode()                  {}
func (v *findImageByIdNodeReturnLineItem) implementsGraphQLInterfacefindImageByIdNode()          {}
func (v *findImageByIdNodeReturnableFulfillment) implementsGraphQLInterfacefindImageByIdNode()   {}
func (v *findImageByIdNodeReverseDelivery) implementsGraphQLInterfacefindImageByIdNode()         {}
func (v *findImageByIdNodeReverseDeliveryLineItem) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeReverseFulfillmentOrder) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeReverseFulfillmentOrderDisposition) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeReverseFulfillmentOrderLineItem) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeSaleAdditionalFee) implementsGraphQLInterfacefindImageByIdNode()      {}
func (v *findImageByIdNodeSavedSearch) implementsGraphQLInterfacefindImageByIdNode()            {}
func (v *findImageByIdNodeScriptTag) implementsGraphQLInterfacefindImageByIdNode()              {}
func (v *findImageByIdNodeSegment) implementsGraphQLInterfacefindImageByIdNode()                {}
func (v *findImageByIdNodeSellingPlan) implementsGraphQLInterfacefindImageByIdNode()            {}
func (v *findImageByIdNodeSellingPlanGroup) implementsGraphQLInterfacefindImageByIdNode()       {}
func (v *findImageByIdNodeServerPixel) implementsGraphQLInterfacefindImageByIdNode()            {}
func (v *findImageByIdNodeShop) implementsGraphQLInterfacefindImageByIdNode()                   {}
func (v *findImageByIdNodeShopAddress) implementsGraphQLInterfacefindImageByIdNode()            {}
func (v *findImageByIdNodeShopPolicy) implementsGraphQLInterfacefindImageByIdNode()             {}
func (v *findImageByIdNodeShopifyPaymentsAccount) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeShopifyPaymentsBalanceTransaction) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeShopifyPaymentsBankAccount) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeShopifyPaymentsDispute) implementsGraphQLInterfacefindImageByIdNode()     {}
func (v *findImageByIdNodeShopifyPaymentsDisputeEvidence) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeShopifyPaymentsDisputeFileUpload) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeShopifyPaymentsDisputeFulfillment) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeShopifyPaymentsPayout) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeShopifyPaymentsVerification) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeStaffMember) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeStandardMetafieldDefinitionTemplate) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeStoreCreditAccount) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeStoreCreditAccountCreditTransaction) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeStoreCreditAccountDebitRevertTransaction) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeStoreCreditAccountDebitTransaction) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeStorefrontAccessToken) implementsGraphQLInterfacefindImageByIdNode()      {}
func (v *findImageByIdNodeSubscriptionBillingAttempt) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeSubscriptionContract) implementsGraphQLInterfacefindImageByIdNode()       {}
func (v *findImageByIdNodeSubscriptionDraft) implementsGraphQLInterfacefindImageByIdNode()          {}
func (v *findImageByIdNodeTaxonomyAttribute) implementsGraphQLInterfacefindImageByIdNode()          {}
func (v *findImageByIdNodeTaxonomyCategory) implementsGraphQLInterfacefindImageByIdNode()           {}
func (v *findImageByIdNodeTaxonomyChoiceListAttribute) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeTaxonomyMeasurementAttribute) implementsGraphQLInterfacefindImageByIdNode() {
}
func (v *findImageByIdNodeTaxonomyValue) implementsGraphQLInterfacefindImageByIdNode()            {}
func (v *findImageByIdNodeTenderTransaction) implementsGraphQLInterfacefindImageByIdNode()        {}
func (v *findImageByIdNodeTransactionFee) implementsGraphQLInterfacefindImageByIdNode()           {}
func (v *findImageByIdNodeUnverifiedReturnLineItem) implementsGraphQLInterfacefindImageByIdNode() {}
func (v *findImageByIdNodeUrlRedirect) implementsGraphQLInterfacefindImageByIdNode()              {}
func (v *findImageByIdNodeUrlRedirectImport) implementsGraphQLInterfacefindImageByIdNode()        {}
func (v *findImageByIdNodeValidation) implementsGraphQLInterfacefindImageByIdNode()               {}
func (v *findImageByIdNodeVideo) implementsGraphQLInterfacefindImageByIdNode()                    {}
func (v *findImageByIdNodeWebPixel) implementsGraphQLInterfacefindImageByIdNode()                 {}
func (v *findImageByIdNodeWebPresence) implementsGraphQLInterfacefindImageByIdNode()              {}
func (v *findImageByIdNodeWebhookSubscription) implementsGraphQLInterfacefindImageByIdNode()      {}

func __unmarshalfindImageByIdNode(b []byte, v *findImageByIdNode) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "AbandonedCheckout":
		*v = new(findImageByIdNodeAbandonedCheckout)
		return json.Unmarshal(b, *v)
	case "AbandonedCheckoutLineItem":
		*v = new(findImageByIdNodeAbandonedCheckoutLineItem)
		return json.Unmarshal(b, *v)
	case "Abandonment":
		*v = new(findImageByIdNodeAbandonment)
		return json.Unmarshal(b, *v)
	case "AddAllProductsOperation":
		*v = new(findImageByIdNodeAddAllProductsOperation)
		return json.Unmarshal(b, *v)
	case "AdditionalFee":
		*v = new(findImageByIdNodeAdditionalFee)
		return json.Unmarshal(b, *v)
	case "App":
		*v = new(findImageByIdNodeApp)
		return json.Unmarshal(b, *v)
	case "AppCatalog":
		*v = new(findImageByIdNodeAppCatalog)
		return json.Unmarshal(b, *v)
	case "AppCredit":
		*v = new(findImageByIdNodeAppCredit)
		return json.Unmarshal(b, *v)
	case "AppInstallation":
		*v = new(findImageByIdNodeAppInstallation)
		return json.Unmarshal(b, *v)
	case "AppPurchaseOneTime":
		*v = new(findImageByIdNodeAppPurchaseOneTime)
		return json.Unmarshal(b, *v)
	case "AppRevenueAttributionRecord":
		*v = new(findImageByIdNodeAppRevenueAttributionRecord)
		return json.Unmarshal(b, *v)
	case "AppSubscription":
		*v = new(findImageByIdNodeAppSubscription)
		return json.Unmarshal(b, *v)
	case "AppUsageRecord":
		*v = new(findImageByIdNodeAppUsageRecord)
		return json.Unmarshal(b, *v)
	case "Article":
		*v = new(findImageByIdNodeArticle)
		return json.Unmarshal(b, *v)
	case "BasicEvent":
		*v = new(findImageByIdNodeBasicEvent)
		return json.Unmarshal(b, *v)
	case "Blog":
		*v = new(findImageByIdNodeBlog)
		return json.Unmarshal(b, *v)
	case "BulkOperation":
		*v = new(findImageByIdNodeBulkOperation)
		return json.Unmarshal(b, *v)
	case "BusinessEntity":
		*v = new(findImageByIdNodeBusinessEntity)
		return json.Unmarshal(b, *v)
	case "CalculatedOrder":
		*v = new(findImageByIdNodeCalculatedOrder)
		return json.Unmarshal(b, *v)
	case "CartTransform":
		*v = new(findImageByIdNodeCartTransform)
		return json.Unmarshal(b, *v)
	case "CashTrackingAdjustment":
		*v = new(findImageByIdNodeCashTrackingAdjustment)
		return json.Unmarshal(b, *v)
	case "CashTrackingSession":
		*v = new(findImageByIdNodeCashTrackingSession)
		return json.Unmarshal(b, *v)
	case "CatalogCsvOperation":
		*v = new(findImageByIdNodeCatalogCsvOperation)
		return json.Unmarshal(b, *v)
	case "Channel":
		*v = new(findImageByIdNodeChannel)
		return json.Unmarshal(b, *v)
	case "ChannelDefinition":
		*v = new(findImageByIdNodeChannelDefinition)
		return json.Unmarshal(b, *v)
	case "ChannelInformation":
		*v = new(findImageByIdNodeChannelInformation)
		return json.Unmarshal(b, *v)
	case "CheckoutProfile":
		*v = new(findImageByIdNodeCheckoutProfile)
		return json.Unmarshal(b, *v)
	case "Collection":
		*v = new(findImageByIdNodeCollection)
		return json.Unmarshal(b, *v)
	case "Comment":
		*v = new(findImageByIdNodeComment)
		return json.Unmarshal(b, *v)
	case "CommentEvent":
		*v = new(findImageByIdNodeCommentEvent)
		return json.Unmarshal(b, *v)
	case "Company":
		*v = new(findImageByIdNodeCompany)
		return json.Unmarshal(b, *v)
	case "CompanyAddress":
		*v = new(findImageByIdNodeCompanyAddress)
		return json.Unmarshal(b, *v)
	case "CompanyContact":
		*v = new(findImageByIdNodeCompanyContact)
		return json.Unmarshal(b, *v)
	case "CompanyContactRole":
		*v = new(findImageByIdNodeCompanyContactRole)
		return json.Unmarshal(b, *v)
	case "CompanyContactRoleAssignment":
		*v = new(findImageByIdNodeCompanyContactRoleAssignment)
		return json.Unmarshal(b, *v)
	case "CompanyLocation":
		*v = new(findImageByIdNodeCompanyLocation)
		return json.Unmarshal(b, *v)
	case "CompanyLocationCatalog":
		*v = new(findImageByIdNodeCompanyLocationCatalog)
		return json.Unmarshal(b, *v)
	case "CompanyLocationStaffMemberAssignment":
		*v = new(findImageByIdNodeCompanyLocationStaffMemberAssignment)
		return json.Unmarshal(b, *v)
	case "ConsentPolicy":
		*v = new(findImageByIdNodeConsentPolicy)
		return json.Unmarshal(b, *v)
	case "CurrencyExchangeAdjustment":
		*v = new(findImageByIdNodeCurrencyExchangeAdjustment)
		return json.Unmarshal(b, *v)
	case "Customer":
		*v = new(findImageByIdNodeCustomer)
		return json.Unmarshal(b, *v)
	case "CustomerAccountAppExtensionPage":
		*v = new(findImageByIdNodeCustomerAccountAppExtensionPage)
		return json.Unmarshal(b, *v)
	case "CustomerAccountNativePage":
		*v = new(findImageByIdNodeCustomerAccountNativePage)
		return json.Unmarshal(b, *v)
	case "CustomerPaymentMethod":
		*v = new(findImageByIdNodeCustomerPaymentMethod)
		return json.Unmarshal(b, *v)
	case "CustomerSegmentMembersQuery":
		*v = new(findImageByIdNodeCustomerSegmentMembersQuery)
		return json.Unmarshal(b, *v)
	case "CustomerVisit":
		*v = new(findImageByIdNodeCustomerVisit)
		return json.Unmarshal(b, *v)
	case "DeliveryCarrierService":
		*v = new(findImageByIdNodeDeliveryCarrierService)
		return json.Unmarshal(b, *v)
	case "DeliveryCondition":
		*v = new(findImageByIdNodeDeliveryCondition)
		return json.Unmarshal(b, *v)
	case "DeliveryCountry":
		*v = new(findImageByIdNodeDeliveryCountry)
		return json.Unmarshal(b, *v)
	case "DeliveryCustomization":
		*v = new(findImageByIdNodeDeliveryCustomization)
		return json.Unmarshal(b, *v)
	case "DeliveryLocationGroup":
		*v = new(findImageByIdNodeDeliveryLocationGroup)
		return json.Unmarshal(b, *v)
	case "DeliveryMethod":
		*v = new(findImageByIdNodeDeliveryMethod)
		return json.Unmarshal(b, *v)
	case "DeliveryMethodDefinition":
		*v = new(findImageByIdNodeDeliveryMethodDefinition)
		return json.Unmarshal(b, *v)
	case "DeliveryParticipant":
		*v = new(findImageByIdNodeDeliveryParticipant)
		return json.Unmarshal(b, *v)
	case "DeliveryProfile":
		*v = new(findImageByIdNodeDeliveryProfile)
		return json.Unmarshal(b, *v)
	case "DeliveryProfileItem":
		*v = new(findImageByIdNodeDeliveryProfileItem)
		return json.Unmarshal(b, *v)
	case "DeliveryPromiseParticipant":
		*v = new(findImageByIdNodeDeliveryPromiseParticipant)
		return json.Unmarshal(b, *v)
	case "DeliveryPromiseProvider":
		*v = new(findImageByIdNodeDeliveryPromiseProvider)
		return json.Unmarshal(b, *v)
	case "DeliveryProvince":
		*v = new(findImageByIdNodeDeliveryProvince)
		return json.Unmarshal(b, *v)
	case "DeliveryRateDefinition":
		*v = new(findImageByIdNodeDeliveryRateDefinition)
		return json.Unmarshal(b, *v)
	case "DeliveryZone":
		*v = new(findImageByIdNodeDeliveryZone)
		return json.Unmarshal(b, *v)
	case "DiscountAutomaticBxgy":
		*v = new(findImageByIdNodeDiscountAutomaticBxgy)
		return json.Unmarshal(b, *v)
	case "DiscountAutomaticNode":
		*v = new(findImageByIdNodeDiscountAutomaticNode)
		return json.Unmarshal(b, *v)
	case "DiscountCodeNode":
		*v = new(findImageByIdNodeDiscountCodeNode)
		return json.Unmarshal(b, *v)
	case "DiscountNode":
		*v = new(findImageByIdNodeDiscountNode)
		return json.Unmarshal(b, *v)
	case "DiscountRedeemCodeBulkCreation":
		*v = new(findImageByIdNodeDiscountRedeemCodeBulkCreation)
		return json.Unmarshal(b, *v)
	case "Domain":
		*v = new(findImageByIdNodeDomain)
		return json.Unmarshal(b, *v)
	case "DraftOrder":
		*v = new(findImageByIdNodeDraftOrder)
		return json.Unmarshal(b, *v)
	case "DraftOrderLineItem":
		*v = new(findImageByIdNodeDraftOrderLineItem)
		return json.Unmarshal(b, *v)
	case "DraftOrderTag":
		*v = new(findImageByIdNodeDraftOrderTag)
		return json.Unmarshal(b, *v)
	case "Duty":
		*v = new(findImageByIdNodeDuty)
		return json.Unmarshal(b, *v)
	case "ExchangeLineItem":
		*v = new(findImageByIdNodeExchangeLineItem)
		return json.Unmarshal(b, *v)
	case "ExchangeV2":
		*v = new(findImageByIdNodeExchangeV2)
		return json.Unmarshal(b, *v)
	case "ExternalVideo":
		*v = new(findImageByIdNodeExternalVideo)
		return json.Unmarshal(b, *v)
	case "Fulfillment":
		*v = new(findImageByIdNodeFulfillment)
		return json.Unmarshal(b, *v)
	case "FulfillmentConstraintRule":
		*v = new(findImageByIdNodeFulfillmentConstraintRule)
		return json.Unmarshal(b, *v)
	case "FulfillmentEvent":
		*v = new(findImageByIdNodeFulfillmentEvent)
		return json.Unmarshal(b, *v)
	case "FulfillmentHold":
		*v = new(findImageByIdNodeFulfillmentHold)
		return json.Unmarshal(b, *v)
	case "FulfillmentLineItem":
		*v = new(findImageByIdNodeFulfillmentLineItem)
		return json.Unmarshal(b, *v)
	case "FulfillmentOrder":
		*v = new(findImageByIdNodeFulfillmentOrder)
		return json.Unmarshal(b, *v)
	case "FulfillmentOrderDestination":
		*v = new(findImageByIdNodeFulfillmentOrderDestination)
		return json.Unmarshal(b, *v)
	case "FulfillmentOrderLineItem":
		*v = new(findImageByIdNodeFulfillmentOrderLineItem)
		return json.Unmarshal(b, *v)
	case "FulfillmentOrderMerchantRequest":
		*v = new(findImageByIdNodeFulfillmentOrderMerchantRequest)
		return json.Unmarshal(b, *v)
	case "GenericFile":
		*v = new(findImageByIdNodeGenericFile)
		return json.Unmarshal(b, *v)
	case "GiftCard":
		*v = new(findImageByIdNodeGiftCard)
		return json.Unmarshal(b, *v)
	case "GiftCardCreditTransaction":
		*v = new(findImageByIdNodeGiftCardCreditTransaction)
		return json.Unmarshal(b, *v)
	case "GiftCardDebitTransaction":
		*v = new(findImageByIdNodeGiftCardDebitTransaction)
		return json.Unmarshal(b, *v)
	case "InventoryAdjustmentGroup":
		*v = new(findImageByIdNodeInventoryAdjustmentGroup)
		return json.Unmarshal(b, *v)
	case "InventoryItem":
		*v = new(findImageByIdNodeInventoryItem)
		return json.Unmarshal(b, *v)
	case "InventoryItemMeasurement":
		*v = new(findImageByIdNodeInventoryItemMeasurement)
		return json.Unmarshal(b, *v)
	case "InventoryLevel":
		*v = new(findImageByIdNodeInventoryLevel)
		return json.Unmarshal(b, *v)
	case "InventoryQuantity":
		*v = new(findImageByIdNodeInventoryQuantity)
		return json.Unmarshal(b, *v)
	case "InventoryShipment":
		*v = new(findImageByIdNodeInventoryShipment)
		return json.Unmarshal(b, *v)
	case "InventoryShipmentLineItem":
		*v = new(findImageByIdNodeInventoryShipmentLineItem)
		return json.Unmarshal(b, *v)
	case "InventoryTransfer":
		*v = new(findImageByIdNodeInventoryTransfer)
		return json.Unmarshal(b, *v)
	case "InventoryTransferLineItem":
		*v = new(findImageByIdNodeInventoryTransferLineItem)
		return json.Unmarshal(b, *v)
	case "LineItem":
		*v = new(findImageByIdNodeLineItem)
		return json.Unmarshal(b, *v)
	case "LineItemGroup":
		*v = new(findImageByIdNodeLineItemGroup)
		return json.Unmarshal(b, *v)
	case "Location":
		*v = new(findImageByIdNodeLocation)
		return json.Unmarshal(b, *v)
	case "MailingAddress":
		*v = new(findImageByIdNodeMailingAddress)
		return json.Unmarshal(b, *v)
	case "Market":
		*v = new(findImageByIdNodeMarket)
		return json.Unmarshal(b, *v)
	case "MarketCatalog":
		*v = new(findImageByIdNodeMarketCatalog)
		return json.Unmarshal(b, *v)
	case "MarketRegionCountry":
		*v = new(findImageByIdNodeMarketRegionCountry)
		return json.Unmarshal(b, *v)
	case "MarketWebPresence":
		*v = new(findImageByIdNodeMarketWebPresence)
		return json.Unmarshal(b, *v)
	case "MarketingActivity":
		*v = new(findImageByIdNodeMarketingActivity)
		return json.Unmarshal(b, *v)
	case "MarketingEvent":
		*v = new(findImageByIdNodeMarketingEvent)
		return json.Unmarshal(b, *v)
	case "MediaImage":
		*v = new(findImageByIdNodeMediaImage)
		return json.Unmarshal(b, *v)
	case "Menu":
		*v = new(findImageByIdNodeMenu)
		return json.Unmarshal(b, *v)
	case "Metafield":
		*v = new(findImageByIdNodeMetafield)
		return json.Unmarshal(b, *v)
	case "MetafieldDefinition":
		*v = new(findImageByIdNodeMetafieldDefinition)
		return json.Unmarshal(b, *v)
	case "Metaobject":
		*v = new(findImageByIdNodeMetaobject)
		return json.Unmarshal(b, *v)
	case "MetaobjectDefinition":
		*v = new(findImageByIdNodeMetaobjectDefinition)
		return json.Unmarshal(b, *v)
	case "Model3d":
		*v = new(findImageByIdNodeModel3d)
		return json.Unmarshal(b, *v)
	case "OnlineStoreTheme":
		*v = new(findImageByIdNodeOnlineStoreTheme)
		return json.Unmarshal(b, *v)
	case "Order":
		*v = new(findImageByIdNodeOrder)
		return json.Unmarshal(b, *v)
	case "OrderAdjustment":
		*v = new(findImageByIdNodeOrderAdjustment)
		return json.Unmarshal(b, *v)
	case "OrderDisputeSummary":
		*v = new(findImageByIdNodeOrderDisputeSummary)
		return json.Unmarshal(b, *v)
	case "OrderEditSession":
		*v = new(findImageByIdNodeOrderEditSession)
		return json.Unmarshal(b, *v)
	case "OrderTransaction":
		*v = new(findImageByIdNodeOrderTransaction)
		return json.Unmarshal(b, *v)
	case "Page":
		*v = new(findImageByIdNodePage)
		return json.Unmarshal(b, *v)
	case "PaymentCustomization":
		*v = new(findImageByIdNodePaymentCustomization)
		return json.Unmarshal(b, *v)
	case "PaymentMandate":
		*v = new(findImageByIdNodePaymentMandate)
		return json.Unmarshal(b, *v)
	case "PaymentSchedule":
		*v = new(findImageByIdNodePaymentSchedule)
		return json.Unmarshal(b, *v)
	case "PaymentTerms":
		*v = new(findImageByIdNodePaymentTerms)
		return json.Unmarshal(b, *v)
	case "PaymentTermsTemplate":
		*v = new(findImageByIdNodePaymentTermsTemplate)
		return json.Unmarshal(b, *v)
	case "PointOfSaleDevice":
		*v = new(findImageByIdNodePointOfSaleDevice)
		return json.Unmarshal(b, *v)
	case "PriceList":
		*v = new(findImageByIdNodePriceList)
		return json.Unmarshal(b, *v)
	case "PriceRule":
		*v = new(findImageByIdNodePriceRule)
		return json.Unmarshal(b, *v)
	case "PriceRuleDiscountCode":
		*v = new(findImageByIdNodePriceRuleDiscountCode)
		return json.Unmarshal(b, *v)
	case "Product":
		*v = new(findImageByIdNodeProduct)
		return json.Unmarshal(b, *v)
	case "ProductBundleOperation":
		*v = new(findImageByIdNodeProductBundleOperation)
		return json.Unmarshal(b, *v)
	case "ProductDeleteOperation":
		*v = new(findImageByIdNodeProductDeleteOperation)
		return json.Unmarshal(b, *v)
	case "ProductDuplicateOperation":
		*v = new(findImageByIdNodeProductDuplicateOperation)
		return json.Unmarshal(b, *v)
	case "ProductFeed":
		*v = new(findImageByIdNodeProductFeed)
		return json.Unmarshal(b, *v)
	case "ProductOption":
		*v = new(findImageByIdNodeProductOption)
		return json.Unmarshal(b, *v)
	case "ProductOptionValue":
		*v = new(findImageByIdNodeProductOptionValue)
		return json.Unmarshal(b, *v)
	case "ProductSetOperation":
		*v = new(findImageByIdNodeProductSetOperation)
		return json.Unmarshal(b, *v)
	case "ProductTaxonomyNode":
		*v = new(findImageByIdNodeProductTaxonomyNode)
		return json.Unmarshal(b, *v)
	case "ProductVariant":
		*v = new(findImageByIdNodeProductVariant)
		return json.Unmarshal(b, *v)
	case "ProductVariantComponent":
		*v = new(findImageByIdNodeProductVariantComponent)
		return json.Unmarshal(b, *v)
	case "Publication":
		*v = new(findImageByIdNodePublication)
		return json.Unmarshal(b, *v)
	case "PublicationResourceOperation":
		*v = new(findImageByIdNodePublicationResourceOperation)
		return json.Unmarshal(b, *v)
	case "QuantityPriceBreak":
		*v = new(findImageByIdNodeQuantityPriceBreak)
		return json.Unmarshal(b, *v)
	case "Refund":
		*v = new(findImageByIdNodeRefund)
		return json.Unmarshal(b, *v)
	case "RefundShippingLine":
		*v = new(findImageByIdNodeRefundShippingLine)
		return json.Unmarshal(b, *v)
	case "Return":
		*v = new(findImageByIdNodeReturn)
		return json.Unmarshal(b, *v)
	case "ReturnLineItem":
		*v = new(findImageByIdNodeReturnLineItem)
		return json.Unmarshal(b, *v)
	case "ReturnableFulfillment":
		*v = new(findImageByIdNodeReturnableFulfillment)
		return json.Unmarshal(b, *v)
	case "ReverseDelivery":
		*v = new(findImageByIdNodeReverseDelivery)
		return json.Unmarshal(b, *v)
	case "ReverseDeliveryLineItem":
		*v = new(findImageByIdNodeReverseDeliveryLineItem)
		return json.Unmarshal(b, *v)
	case "ReverseFulfillmentOrder":
		*v = new(findImageByIdNodeReverseFulfillmentOrder)
		return json.Unmarshal(b, *v)
	case "ReverseFulfillmentOrderDisposition":
		*v = new(findImageByIdNodeReverseFulfillmentOrderDisposition)
		return json.Unmarshal(b, *v)
	case "ReverseFulfillmentOrderLineItem":
		*v = new(findImageByIdNodeReverseFulfillmentOrderLineItem)
		return json.Unmarshal(b, *v)
	case "SaleAdditionalFee":
		*v = new(findImageByIdNodeSaleAdditionalFee)
		return json.Unmarshal(b, *v)
	case "SavedSearch":
		*v = new(findImageByIdNodeSavedSearch)
		return json.Unmarshal(b, *v)
	case "ScriptTag":
		*v = new(findImageByIdNodeScriptTag)
		return json.Unmarshal(b, *v)
	case "Segment":
		*v = new(findImageByIdNodeSegment)
		return json.Unmarshal(b, *v)
	case "SellingPlan":
		*v = new(findImageByIdNodeSellingPlan)
		return json.Unmarshal(b, *v)
	case "SellingPlanGroup":
		*v = new(findImageByIdNodeSellingPlanGroup)
		return json.Unmarshal(b, *v)
	case "ServerPixel":
		*v = new(findImageByIdNodeServerPixel)
		return json.Unmarshal(b, *v)
	case "Shop":
		*v = new(findImageByIdNodeShop)
		return json.Unmarshal(b, *v)
	case "ShopAddress":
		*v = new(findImageByIdNodeShopAddress)
		return json.Unmarshal(b, *v)
	case "ShopPolicy":
		*v = new(findImageByIdNodeShopPolicy)
		return json.Unmarshal(b, *v)
	case "ShopifyPaymentsAccount":
		*v = new(findImageByIdNodeShopifyPaymentsAccount)
		return json.Unmarshal(b, *v)
	case "ShopifyPaymentsBalanceTransaction":
		*v = new(findImageByIdNodeShopifyPaymentsBalanceTransaction)
		return json.Unmarshal(b, *v)
	case "ShopifyPaymentsBankAccount":
		*v = new(findImageByIdNodeShopifyPaymentsBankAccount)
		return json.Unmarshal(b, *v)
	case "ShopifyPaymentsDispute":
		*v = new(findImageByIdNodeShopifyPaymentsDispute)
		return json.Unmarshal(b, *v)
	case "ShopifyPaymentsDisputeEvidence":
		*v = new(findImageByIdNodeShopifyPaymentsDisputeEvidence)
		return json.Unmarshal(b, *v)
	case "ShopifyPaymentsDisputeFileUpload":
		*v = new(findImageByIdNodeShopifyPaymentsDisputeFileUpload)
		return json.Unmarshal(b, *v)
	case "ShopifyPaymentsDisputeFulfillment":
		*v = new(findImageByIdNodeShopifyPaymentsDisputeFulfillment)
		return json.Unmarshal(b, *v)
	case "ShopifyPaymentsPayout":
		*v = new(findImageByIdNodeShopifyPaymentsPayout)
		return json.Unmarshal(b, *v)
	case "ShopifyPaymentsVerification":
		*v = new(findImageByIdNodeShopifyPaymentsVerification)
		return json.Unmarshal(b, *v)
	case "StaffMember":
		*v = new(findImageByIdNodeStaffMember)
		return json.Unmarshal(b, *v)
	case "StandardMetafieldDefinitionTemplate":
		*v = new(findImageByIdNodeStandardMetafieldDefinitionTemplate)
		return json.Unmarshal(b, *v)
	case "StoreCreditAccount":
		*v = new(findImageByIdNodeStoreCreditAccount)
		return json.Unmarshal(b, *v)
	case "StoreCreditAccountCreditTransaction":
		*v = new(findImageByIdNodeStoreCreditAccountCreditTransaction)
		return json.Unmarshal(b, *v)
	case "StoreCreditAccountDebitRevertTransaction":
		*v = new(findImageByIdNodeStoreCreditAccountDebitRevertTransaction)
		return json.Unmarshal(b, *v)
	case "StoreCreditAccountDebitTransaction":
		*v = new(findImageByIdNodeStoreCreditAccountDebitTransaction)
		return json.Unmarshal(b, *v)
	case "StorefrontAccessToken":
		*v = new(findImageByIdNodeStorefrontAccessToken)
		return json.Unmarshal(b, *v)
	case "SubscriptionBillingAttempt":
		*v = new(findImageByIdNodeSubscriptionBillingAttempt)
		return json.Unmarshal(b, *v)
	case "SubscriptionContract":
		*v = new(findImageByIdNodeSubscriptionContract)
		return json.Unmarshal(b, *v)
	case "SubscriptionDraft":
		*v = new(findImageByIdNodeSubscriptionDraft)
		return json.Unmarshal(b, *v)
	case "TaxonomyAttribute":
		*v = new(findImageByIdNodeTaxonomyAttribute)
		return json.Unmarshal(b, *v)
	case "TaxonomyCategory":
		*v = new(findImageByIdNodeTaxonomyCategory)
		return json.Unmarshal(b, *v)
	case "TaxonomyChoiceListAttribute":
		*v = new(findImageByIdNodeTaxonomyChoiceListAttribute)
		return json.Unmarshal(b, *v)
	case "TaxonomyMeasurementAttribute":
		*v = new(findImageByIdNodeTaxonomyMeasurementAttribute)
		return json.Unmarshal(b, *v)
	case "TaxonomyValue":
		*v = new(findImageByIdNodeTaxonomyValue)
		return json.Unmarshal(b, *v)
	case "TenderTransaction":
		*v = new(findImageByIdNodeTenderTransaction)
		return json.Unmarshal(b, *v)
	case "TransactionFee":
		*v = new(findImageByIdNodeTransactionFee)
		return json.Unmarshal(b, *v)
	case "UnverifiedReturnLineItem":
		*v = new(findImageByIdNodeUnverifiedReturnLineItem)
		return json.Unmarshal(b, *v)
	case "UrlRedirect":
		*v = new(findImageByIdNodeUrlRedirect)
		return json.Unmarshal(b, *v)
	case "UrlRedirectImport":
		*v = new(findImageByIdNodeUrlRedirectImport)
		return json.Unmarshal(b, *v)
	case "Validation":
		*v = new(findImageByIdNodeValidation)
		return json.Unmarshal(b, *v)
	case "Video":
		*v = new(findImageByIdNodeVideo)
		return json.Unmarshal(b, *v)
	case "WebPixel":
		*v = new(findImageByIdNodeWebPixel)
		return json.Unmarshal(b, *v)
	case "WebPresence":
		*v = new(findImageByIdNodeWebPresence)
		return json.Unmarshal(b, *v)
	case "WebhookSubscription":
		*v = new(findImageByIdNodeWebhookSubscription)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing Node.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for findImageByIdNode: "%v"`, tn.TypeName)
	}
}

func __marshalfindImageByIdNode(v *findImageByIdNode) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *findImageByIdNodeAbandonedCheckout:
		typename = "AbandonedCheckout"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeAbandonedCheckout
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeAbandonedCheckoutLineItem:
		typename = "AbandonedCheckoutLineItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeAbandonedCheckoutLineItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeAbandonment:
		typename = "Abandonment"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeAbandonment
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeAddAllProductsOperation:
		typename = "AddAllProductsOperation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeAddAllProductsOperation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeAdditionalFee:
		typename = "AdditionalFee"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeAdditionalFee
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeApp:
		typename = "App"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeApp
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeAppCatalog:
		typename = "AppCatalog"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeAppCatalog
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeAppCredit:
		typename = "AppCredit"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeAppCredit
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeAppInstallation:
		typename = "AppInstallation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeAppInstallation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeAppPurchaseOneTime:
		typename = "AppPurchaseOneTime"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeAppPurchaseOneTime
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeAppRevenueAttributionRecord:
		typename = "AppRevenueAttributionRecord"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeAppRevenueAttributionRecord
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeAppSubscription:
		typename = "AppSubscription"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeAppSubscription
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeAppUsageRecord:
		typename = "AppUsageRecord"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeAppUsageRecord
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeArticle:
		typename = "Article"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeArticle
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeBasicEvent:
		typename = "BasicEvent"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeBasicEvent
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeBlog:
		typename = "Blog"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeBlog
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeBulkOperation:
		typename = "BulkOperation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeBulkOperation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeBusinessEntity:
		typename = "BusinessEntity"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeBusinessEntity
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCalculatedOrder:
		typename = "CalculatedOrder"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCalculatedOrder
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCartTransform:
		typename = "CartTransform"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCartTransform
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCashTrackingAdjustment:
		typename = "CashTrackingAdjustment"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCashTrackingAdjustment
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCashTrackingSession:
		typename = "CashTrackingSession"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCashTrackingSession
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCatalogCsvOperation:
		typename = "CatalogCsvOperation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCatalogCsvOperation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeChannel:
		typename = "Channel"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeChannel
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeChannelDefinition:
		typename = "ChannelDefinition"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeChannelDefinition
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeChannelInformation:
		typename = "ChannelInformation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeChannelInformation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCheckoutProfile:
		typename = "CheckoutProfile"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCheckoutProfile
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCollection:
		typename = "Collection"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCollection
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeComment:
		typename = "Comment"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeComment
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCommentEvent:
		typename = "CommentEvent"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCommentEvent
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCompany:
		typename = "Company"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCompany
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCompanyAddress:
		typename = "CompanyAddress"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCompanyAddress
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCompanyContact:
		typename = "CompanyContact"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCompanyContact
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCompanyContactRole:
		typename = "CompanyContactRole"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCompanyContactRole
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCompanyContactRoleAssignment:
		typename = "CompanyContactRoleAssignment"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCompanyContactRoleAssignment
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCompanyLocation:
		typename = "CompanyLocation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCompanyLocation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCompanyLocationCatalog:
		typename = "CompanyLocationCatalog"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCompanyLocationCatalog
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCompanyLocationStaffMemberAssignment:
		typename = "CompanyLocationStaffMemberAssignment"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCompanyLocationStaffMemberAssignment
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeConsentPolicy:
		typename = "ConsentPolicy"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeConsentPolicy
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCurrencyExchangeAdjustment:
		typename = "CurrencyExchangeAdjustment"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCurrencyExchangeAdjustment
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCustomer:
		typename = "Customer"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCustomer
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCustomerAccountAppExtensionPage:
		typename = "CustomerAccountAppExtensionPage"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCustomerAccountAppExtensionPage
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCustomerAccountNativePage:
		typename = "CustomerAccountNativePage"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCustomerAccountNativePage
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCustomerPaymentMethod:
		typename = "CustomerPaymentMethod"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCustomerPaymentMethod
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCustomerSegmentMembersQuery:
		typename = "CustomerSegmentMembersQuery"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCustomerSegmentMembersQuery
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeCustomerVisit:
		typename = "CustomerVisit"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeCustomerVisit
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryCarrierService:
		typename = "DeliveryCarrierService"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryCarrierService
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryCondition:
		typename = "DeliveryCondition"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryCondition
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryCountry:
		typename = "DeliveryCountry"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryCountry
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryCustomization:
		typename = "DeliveryCustomization"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryCustomization
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryLocationGroup:
		typename = "DeliveryLocationGroup"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryLocationGroup
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryMethod:
		typename = "DeliveryMethod"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryMethod
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryMethodDefinition:
		typename = "DeliveryMethodDefinition"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryMethodDefinition
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryParticipant:
		typename = "DeliveryParticipant"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryParticipant
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryProfile:
		typename = "DeliveryProfile"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryProfile
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryProfileItem:
		typename = "DeliveryProfileItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryProfileItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryPromiseParticipant:
		typename = "DeliveryPromiseParticipant"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryPromiseParticipant
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryPromiseProvider:
		typename = "DeliveryPromiseProvider"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryPromiseProvider
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryProvince:
		typename = "DeliveryProvince"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryProvince
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryRateDefinition:
		typename = "DeliveryRateDefinition"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryRateDefinition
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDeliveryZone:
		typename = "DeliveryZone"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDeliveryZone
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDiscountAutomaticBxgy:
		typename = "DiscountAutomaticBxgy"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDiscountAutomaticBxgy
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDiscountAutomaticNode:
		typename = "DiscountAutomaticNode"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDiscountAutomaticNode
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDiscountCodeNode:
		typename = "DiscountCodeNode"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDiscountCodeNode
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDiscountNode:
		typename = "DiscountNode"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDiscountNode
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDiscountRedeemCodeBulkCreation:
		typename = "DiscountRedeemCodeBulkCreation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDiscountRedeemCodeBulkCreation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDomain:
		typename = "Domain"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDomain
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDraftOrder:
		typename = "DraftOrder"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDraftOrder
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDraftOrderLineItem:
		typename = "DraftOrderLineItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDraftOrderLineItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDraftOrderTag:
		typename = "DraftOrderTag"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDraftOrderTag
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeDuty:
		typename = "Duty"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeDuty
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeExchangeLineItem:
		typename = "ExchangeLineItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeExchangeLineItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeExchangeV2:
		typename = "ExchangeV2"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeExchangeV2
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeExternalVideo:
		typename = "ExternalVideo"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeExternalVideo
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeFulfillment:
		typename = "Fulfillment"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeFulfillment
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeFulfillmentConstraintRule:
		typename = "FulfillmentConstraintRule"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeFulfillmentConstraintRule
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeFulfillmentEvent:
		typename = "FulfillmentEvent"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeFulfillmentEvent
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeFulfillmentHold:
		typename = "FulfillmentHold"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeFulfillmentHold
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeFulfillmentLineItem:
		typename = "FulfillmentLineItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeFulfillmentLineItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeFulfillmentOrder:
		typename = "FulfillmentOrder"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeFulfillmentOrder
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeFulfillmentOrderDestination:
		typename = "FulfillmentOrderDestination"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeFulfillmentOrderDestination
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeFulfillmentOrderLineItem:
		typename = "FulfillmentOrderLineItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeFulfillmentOrderLineItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeFulfillmentOrderMerchantRequest:
		typename = "FulfillmentOrderMerchantRequest"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeFulfillmentOrderMerchantRequest
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeGenericFile:
		typename = "GenericFile"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeGenericFile
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeGiftCard:
		typename = "GiftCard"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeGiftCard
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeGiftCardCreditTransaction:
		typename = "GiftCardCreditTransaction"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeGiftCardCreditTransaction
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeGiftCardDebitTransaction:
		typename = "GiftCardDebitTransaction"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeGiftCardDebitTransaction
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeInventoryAdjustmentGroup:
		typename = "InventoryAdjustmentGroup"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeInventoryAdjustmentGroup
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeInventoryItem:
		typename = "InventoryItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeInventoryItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeInventoryItemMeasurement:
		typename = "InventoryItemMeasurement"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeInventoryItemMeasurement
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeInventoryLevel:
		typename = "InventoryLevel"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeInventoryLevel
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeInventoryQuantity:
		typename = "InventoryQuantity"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeInventoryQuantity
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeInventoryShipment:
		typename = "InventoryShipment"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeInventoryShipment
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeInventoryShipmentLineItem:
		typename = "InventoryShipmentLineItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeInventoryShipmentLineItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeInventoryTransfer:
		typename = "InventoryTransfer"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeInventoryTransfer
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeInventoryTransferLineItem:
		typename = "InventoryTransferLineItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeInventoryTransferLineItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeLineItem:
		typename = "LineItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeLineItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeLineItemGroup:
		typename = "LineItemGroup"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeLineItemGroup
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeLocation:
		typename = "Location"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeLocation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMailingAddress:
		typename = "MailingAddress"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMailingAddress
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMarket:
		typename = "Market"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMarket
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMarketCatalog:
		typename = "MarketCatalog"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMarketCatalog
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMarketRegionCountry:
		typename = "MarketRegionCountry"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMarketRegionCountry
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMarketWebPresence:
		typename = "MarketWebPresence"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMarketWebPresence
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMarketingActivity:
		typename = "MarketingActivity"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMarketingActivity
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMarketingEvent:
		typename = "MarketingEvent"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMarketingEvent
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMediaImage:
		typename = "MediaImage"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMediaImage
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMenu:
		typename = "Menu"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMenu
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMetafield:
		typename = "Metafield"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMetafield
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMetafieldDefinition:
		typename = "MetafieldDefinition"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMetafieldDefinition
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMetaobject:
		typename = "Metaobject"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMetaobject
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeMetaobjectDefinition:
		typename = "MetaobjectDefinition"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeMetaobjectDefinition
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeModel3d:
		typename = "Model3d"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeModel3d
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeOnlineStoreTheme:
		typename = "OnlineStoreTheme"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeOnlineStoreTheme
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeOrder:
		typename = "Order"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeOrder
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeOrderAdjustment:
		typename = "OrderAdjustment"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeOrderAdjustment
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeOrderDisputeSummary:
		typename = "OrderDisputeSummary"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeOrderDisputeSummary
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeOrderEditSession:
		typename = "OrderEditSession"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeOrderEditSession
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeOrderTransaction:
		typename = "OrderTransaction"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeOrderTransaction
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodePage:
		typename = "Page"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodePage
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodePaymentCustomization:
		typename = "PaymentCustomization"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodePaymentCustomization
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodePaymentMandate:
		typename = "PaymentMandate"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodePaymentMandate
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodePaymentSchedule:
		typename = "PaymentSchedule"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodePaymentSchedule
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodePaymentTerms:
		typename = "PaymentTerms"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodePaymentTerms
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodePaymentTermsTemplate:
		typename = "PaymentTermsTemplate"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodePaymentTermsTemplate
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodePointOfSaleDevice:
		typename = "PointOfSaleDevice"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodePointOfSaleDevice
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodePriceList:
		typename = "PriceList"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodePriceList
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodePriceRule:
		typename = "PriceRule"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodePriceRule
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodePriceRuleDiscountCode:
		typename = "PriceRuleDiscountCode"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodePriceRuleDiscountCode
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeProduct:
		typename = "Product"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeProduct
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeProductBundleOperation:
		typename = "ProductBundleOperation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeProductBundleOperation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeProductDeleteOperation:
		typename = "ProductDeleteOperation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeProductDeleteOperation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeProductDuplicateOperation:
		typename = "ProductDuplicateOperation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeProductDuplicateOperation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeProductFeed:
		typename = "ProductFeed"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeProductFeed
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeProductOption:
		typename = "ProductOption"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeProductOption
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeProductOptionValue:
		typename = "ProductOptionValue"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeProductOptionValue
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeProductSetOperation:
		typename = "ProductSetOperation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeProductSetOperation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeProductTaxonomyNode:
		typename = "ProductTaxonomyNode"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeProductTaxonomyNode
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeProductVariant:
		typename = "ProductVariant"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeProductVariant
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeProductVariantComponent:
		typename = "ProductVariantComponent"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeProductVariantComponent
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodePublication:
		typename = "Publication"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodePublication
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodePublicationResourceOperation:
		typename = "PublicationResourceOperation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodePublicationResourceOperation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeQuantityPriceBreak:
		typename = "QuantityPriceBreak"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeQuantityPriceBreak
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeRefund:
		typename = "Refund"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeRefund
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeRefundShippingLine:
		typename = "RefundShippingLine"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeRefundShippingLine
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeReturn:
		typename = "Return"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeReturn
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeReturnLineItem:
		typename = "ReturnLineItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeReturnLineItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeReturnableFulfillment:
		typename = "ReturnableFulfillment"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeReturnableFulfillment
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeReverseDelivery:
		typename = "ReverseDelivery"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeReverseDelivery
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeReverseDeliveryLineItem:
		typename = "ReverseDeliveryLineItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeReverseDeliveryLineItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeReverseFulfillmentOrder:
		typename = "ReverseFulfillmentOrder"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeReverseFulfillmentOrder
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeReverseFulfillmentOrderDisposition:
		typename = "ReverseFulfillmentOrderDisposition"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeReverseFulfillmentOrderDisposition
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeReverseFulfillmentOrderLineItem:
		typename = "ReverseFulfillmentOrderLineItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeReverseFulfillmentOrderLineItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeSaleAdditionalFee:
		typename = "SaleAdditionalFee"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeSaleAdditionalFee
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeSavedSearch:
		typename = "SavedSearch"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeSavedSearch
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeScriptTag:
		typename = "ScriptTag"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeScriptTag
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeSegment:
		typename = "Segment"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeSegment
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeSellingPlan:
		typename = "SellingPlan"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeSellingPlan
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeSellingPlanGroup:
		typename = "SellingPlanGroup"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeSellingPlanGroup
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeServerPixel:
		typename = "ServerPixel"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeServerPixel
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeShop:
		typename = "Shop"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeShop
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeShopAddress:
		typename = "ShopAddress"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeShopAddress
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeShopPolicy:
		typename = "ShopPolicy"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeShopPolicy
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeShopifyPaymentsAccount:
		typename = "ShopifyPaymentsAccount"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeShopifyPaymentsAccount
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeShopifyPaymentsBalanceTransaction:
		typename = "ShopifyPaymentsBalanceTransaction"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeShopifyPaymentsBalanceTransaction
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeShopifyPaymentsBankAccount:
		typename = "ShopifyPaymentsBankAccount"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeShopifyPaymentsBankAccount
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeShopifyPaymentsDispute:
		typename = "ShopifyPaymentsDispute"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeShopifyPaymentsDispute
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeShopifyPaymentsDisputeEvidence:
		typename = "ShopifyPaymentsDisputeEvidence"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeShopifyPaymentsDisputeEvidence
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeShopifyPaymentsDisputeFileUpload:
		typename = "ShopifyPaymentsDisputeFileUpload"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeShopifyPaymentsDisputeFileUpload
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeShopifyPaymentsDisputeFulfillment:
		typename = "ShopifyPaymentsDisputeFulfillment"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeShopifyPaymentsDisputeFulfillment
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeShopifyPaymentsPayout:
		typename = "ShopifyPaymentsPayout"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeShopifyPaymentsPayout
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeShopifyPaymentsVerification:
		typename = "ShopifyPaymentsVerification"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeShopifyPaymentsVerification
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeStaffMember:
		typename = "StaffMember"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeStaffMember
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeStandardMetafieldDefinitionTemplate:
		typename = "StandardMetafieldDefinitionTemplate"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeStandardMetafieldDefinitionTemplate
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeStoreCreditAccount:
		typename = "StoreCreditAccount"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeStoreCreditAccount
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeStoreCreditAccountCreditTransaction:
		typename = "StoreCreditAccountCreditTransaction"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeStoreCreditAccountCreditTransaction
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeStoreCreditAccountDebitRevertTransaction:
		typename = "StoreCreditAccountDebitRevertTransaction"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeStoreCreditAccountDebitRevertTransaction
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeStoreCreditAccountDebitTransaction:
		typename = "StoreCreditAccountDebitTransaction"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeStoreCreditAccountDebitTransaction
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeStorefrontAccessToken:
		typename = "StorefrontAccessToken"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeStorefrontAccessToken
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeSubscriptionBillingAttempt:
		typename = "SubscriptionBillingAttempt"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeSubscriptionBillingAttempt
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeSubscriptionContract:
		typename = "SubscriptionContract"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeSubscriptionContract
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeSubscriptionDraft:
		typename = "SubscriptionDraft"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeSubscriptionDraft
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeTaxonomyAttribute:
		typename = "TaxonomyAttribute"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeTaxonomyAttribute
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeTaxonomyCategory:
		typename = "TaxonomyCategory"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeTaxonomyCategory
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeTaxonomyChoiceListAttribute:
		typename = "TaxonomyChoiceListAttribute"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeTaxonomyChoiceListAttribute
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeTaxonomyMeasurementAttribute:
		typename = "TaxonomyMeasurementAttribute"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeTaxonomyMeasurementAttribute
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeTaxonomyValue:
		typename = "TaxonomyValue"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeTaxonomyValue
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeTenderTransaction:
		typename = "TenderTransaction"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeTenderTransaction
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeTransactionFee:
		typename = "TransactionFee"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeTransactionFee
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeUnverifiedReturnLineItem:
		typename = "UnverifiedReturnLineItem"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeUnverifiedReturnLineItem
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeUrlRedirect:
		typename = "UrlRedirect"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeUrlRedirect
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeUrlRedirectImport:
		typename = "UrlRedirectImport"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeUrlRedirectImport
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeValidation:
		typename = "Validation"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeValidation
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeVideo:
		typename = "Video"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeVideo
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeWebPixel:
		typename = "WebPixel"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeWebPixel
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeWebPresence:
		typename = "WebPresence"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeWebPresence
		}{typename, v}
		return json.Marshal(result)
	case *findImageByIdNodeWebhookSubscription:
		typename = "WebhookSubscription"

		result := struct {
			TypeName string `json:"__typename"`
			*findImageByIdNodeWebhookSubscription
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for findImageByIdNode: "%T"`, v)
	}
}

// findImageByIdNodeAbandonedCheckout includes the requested fields of the GraphQL type AbandonedCheckout.
// The GraphQL type's documentation follows.
//
// A checkout that was abandoned by the customer.
type findImageByIdNodeAbandonedCheckout struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeAbandonedCheckout.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeAbandonedCheckout) GetTypename() *string { return v.Typename }

// findImageByIdNodeAbandonedCheckoutLineItem includes the requested fields of the GraphQL type AbandonedCheckoutLineItem.
// The GraphQL type's documentation follows.
//
// A single line item in an abandoned checkout.
type findImageByIdNodeAbandonedCheckoutLineItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeAbandonedCheckoutLineItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeAbandonedCheckoutLineItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeAbandonment includes the requested fields of the GraphQL type Abandonment.
// The GraphQL type's documentation follows.
//
// A browse, cart, or checkout that was abandoned by a customer.
type findImageByIdNodeAbandonment struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeAbandonment.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeAbandonment) GetTypename() *string { return v.Typename }

// findImageByIdNodeAddAllProductsOperation includes the requested fields of the GraphQL type AddAllProductsOperation.
// The GraphQL type's documentation follows.
//
// Represents an operation publishing all products to a publication.
type findImageByIdNodeAddAllProductsOperation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeAddAllProductsOperation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeAddAllProductsOperation) GetTypename() *string { return v.Typename }

// findImageByIdNodeAdditionalFee includes the requested fields of the GraphQL type AdditionalFee.
// The GraphQL type's documentation follows.
//
// The additional fees that have been applied to the order.
type findImageByIdNodeAdditionalFee struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeAdditionalFee.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeAdditionalFee) GetTypename() *string { return v.Typename }

// findImageByIdNodeApp includes the requested fields of the GraphQL type App.
// The GraphQL type's documentation follows.
//
// A Shopify application.
type findImageByIdNodeApp struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeApp.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeApp) GetTypename() *string { return v.Typename }

// findImageByIdNodeAppCatalog includes the requested fields of the GraphQL type AppCatalog.
// The GraphQL type's documentation follows.
//
// A catalog that defines the publication associated with an app.
type findImageByIdNodeAppCatalog struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeAppCatalog.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeAppCatalog) GetTypename() *string { return v.Typename }

// findImageByIdNodeAppCredit includes the requested fields of the GraphQL type AppCredit.
// The GraphQL type's documentation follows.
//
// App credits can be applied by the merchant towards future app purchases, subscriptions, or usage records in Shopify.
type findImageByIdNodeAppCredit struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeAppCredit.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeAppCredit) GetTypename() *string { return v.Typename }

// findImageByIdNodeAppInstallation includes the requested fields of the GraphQL type AppInstallation.
// The GraphQL type's documentation follows.
//
// Represents an installed application on a shop.
type findImageByIdNodeAppInstallation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeAppInstallation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeAppInstallation) GetTypename() *string { return v.Typename }

// findImageByIdNodeAppPurchaseOneTime includes the requested fields of the GraphQL type AppPurchaseOneTime.
// The GraphQL type's documentation follows.
//
// Services and features purchased once by a store.
type findImageByIdNodeAppPurchaseOneTime struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeAppPurchaseOneTime.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeAppPurchaseOneTime) GetTypename() *string { return v.Typename }

// findImageByIdNodeAppRevenueAttributionRecord includes the requested fields of the GraphQL type AppRevenueAttributionRecord.
// The GraphQL type's documentation follows.
//
// Represents app revenue that was captured externally by the partner.
type findImageByIdNodeAppRevenueAttributionRecord struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeAppRevenueAttributionRecord.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeAppRevenueAttributionRecord) GetTypename() *string { return v.Typename }

// findImageByIdNodeAppSubscription includes the requested fields of the GraphQL type AppSubscription.
// The GraphQL type's documentation follows.
//
// Provides users access to services and/or features for a duration of time.
type findImageByIdNodeAppSubscription struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeAppSubscription.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeAppSubscription) GetTypename() *string { return v.Typename }

// findImageByIdNodeAppUsageRecord includes the requested fields of the GraphQL type AppUsageRecord.
// The GraphQL type's documentation follows.
//
// Store usage for app subscriptions with usage pricing.
type findImageByIdNodeAppUsageRecord struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeAppUsageRecord.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeAppUsageRecord) GetTypename() *string { return v.Typename }

// findImageByIdNodeArticle includes the requested fields of the GraphQL type Article.
// The GraphQL type's documentation follows.
//
// An article in the blogging system.
type findImageByIdNodeArticle struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeArticle.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeArticle) GetTypename() *string { return v.Typename }

// findImageByIdNodeBasicEvent includes the requested fields of the GraphQL type BasicEvent.
// The GraphQL type's documentation follows.
//
// Basic events chronicle resource activities such as the creation of an article, the fulfillment of an order, or
// the addition of a product.
//
// ### General events
//
// | Action | Description  |
// |---|---|
// | `create` | The item was created. |
// | `destroy` | The item was destroyed. |
// | `published` | The item was published. |
// | `unpublished` | The item was unpublished. |
// | `update` | The item was updated.  |
//
// ### Order events
//
// Order events can be divided into the following categories:
//
// - *Authorization*: Includes whether the authorization succeeded, failed, or is pending.
// - *Capture*: Includes whether the capture succeeded, failed, or is pending.
// - *Email*: Includes confirmation or cancellation of the order, as well as shipping.
// - *Fulfillment*: Includes whether the fulfillment succeeded, failed, or is
// pending. Also includes cancellation, restocking, and fulfillment updates.
// - *Order*: Includess the placement, confirmation, closing, re-opening, and cancellation of the order.
// - *Refund*: Includes whether the refund succeeded, failed, or is pending.
// - *Sale*: Includes whether the sale succeeded, failed, or is pending.
// - *Void*: Includes whether the void succeeded, failed, or is pending.
//
// | Action  | Message  | Description  |
// |---|---|---|
// | `authorization_failure` | The customer, unsuccessfully, tried to authorize:
// `{money_amount}`. | Authorization failed. The funds cannot be captured. |
// | `authorization_pending` | Authorization for `{money_amount}` is pending. | Authorization pending. |
// | `authorization_success` | The customer successfully authorized us to capture:
// `{money_amount}`. | Authorization was successful and the funds are available for capture. |
// | `cancelled` | Order was cancelled by `{shop_staff_name}`. | The order was cancelled. |
// | `capture_failure` | We failed to capture: `{money_amount}`. | The capture
// failed. The funds cannot be transferred to the shop. |
// | `capture_pending` | Capture for `{money_amount}` is pending. | The capture is
// in process. The funds are not yet available to the shop. |
// | `capture_success` | We successfully captured: `{money_amount}` | The capture
// was successful and the funds are now available to the shop. |
// | `closed` | Order was closed. | The order was closed. |
// | `confirmed` | Received a new order: `{order_number}` by `{customer_name}`. | The order was confirmed. |
// | `fulfillment_cancelled` | We cancelled `{number_of_line_items}` from being
// fulfilled by the third party fulfillment service. | Fulfillment for one or more
// of the line_items failed. |
// | `fulfillment_pending` | We submitted `{number_of_line_items}` to the third
// party service. | One or more of the line_items has been assigned to a third
// party service for fulfillment. |
// | `fulfillment_success` | We successfully fulfilled line_items. | Fulfillment was successful for one or more line_items. |
// | `mail_sent` | `{message_type}` email was sent to the customer. | An email was sent to the customer. |
// | `placed` | Order was placed. | An order was placed by the customer. |
// | `re_opened` | Order was re-opened. | An order was re-opened. |
// | `refund_failure` | We failed to refund `{money_amount}`. | The refund failed. The funds are still with the shop. |
// | `refund_pending` | Refund of `{money_amount}` is still pending. | The refund
// is in process. The funds are still with shop. |
// | `refund_success` | We successfully refunded `{money_amount}`. | The refund was
// successful. The funds have been transferred to the customer. |
// | `restock_line_items` | We restocked `{number_of_line_items}`. |	One or more of
// the order's line items have been restocked. |
// | `sale_failure` | The customer failed to pay `{money_amount}`. | The sale
// failed. The funds are not available to the shop. |
// | `sale_pending` | The `{money_amount}` is pending. | The sale is in process. The funds are not yet available to the shop. |
// | `sale_success` | We successfully captured `{money_amount}`. | The sale was successful. The funds are now with the shop. |
// | `update` | `{order_number}` was updated. | The order was updated. |
// | `void_failure` | We failed to void the authorization. | Voiding the
// authorization failed. The authorization is still valid. |
// | `void_pending` | Authorization void is pending. | Voiding the authorization is
// in process. The authorization is still valid. |
// | `void_success` | We successfully voided the authorization. | Voiding the
// authorization was successful. The authorization is no longer valid. |
type findImageByIdNodeBasicEvent struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeBasicEvent.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeBasicEvent) GetTypename() *string { return v.Typename }

// findImageByIdNodeBlog includes the requested fields of the GraphQL type Blog.
// The GraphQL type's documentation follows.
//
// Shopify stores come with a built-in blogging engine, allowing a shop to have one or more blogs.  Blogs are meant
// to be used as a type of magazine or newsletter for the shop, with content that changes over time.
type findImageByIdNodeBlog struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeBlog.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeBlog) GetTypename() *string { return v.Typename }

// findImageByIdNodeBulkOperation includes the requested fields of the GraphQL type BulkOperation.
// The GraphQL type's documentation follows.
//
// An asynchronous long-running operation to fetch data in bulk or to bulk import data.
//
// Bulk operations are created using the `bulkOperationRunQuery` or `bulkOperationRunMutation` mutation. After
// they are created, clients should poll the `status` field for updates. When `COMPLETED`, the `url` field contains
// a link to the data in [JSONL](http://jsonlines.org/) format.
//
// Refer to the [bulk operations guide](https://shopify.dev/api/usage/bulk-operations/imports) for more details.
type findImageByIdNodeBulkOperation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeBulkOperation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeBulkOperation) GetTypename() *string { return v.Typename }

// findImageByIdNodeBusinessEntity includes the requested fields of the GraphQL type BusinessEntity.
// The GraphQL type's documentation follows.
//
// Represents a merchant's Business Entity.
type findImageByIdNodeBusinessEntity struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeBusinessEntity.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeBusinessEntity) GetTypename() *string { return v.Typename }

// findImageByIdNodeCalculatedOrder includes the requested fields of the GraphQL type CalculatedOrder.
// The GraphQL type's documentation follows.
//
// An order with edits applied but not saved.
type findImageByIdNodeCalculatedOrder struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCalculatedOrder.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCalculatedOrder) GetTypename() *string { return v.Typename }

// findImageByIdNodeCartTransform includes the requested fields of the GraphQL type CartTransform.
// The GraphQL type's documentation follows.
//
// A Cart Transform Function to create [Customized Bundles.](https://shopify.dev/docs/apps/selling-strategies/bundles/add-a-customized-bundle).
type findImageByIdNodeCartTransform struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCartTransform.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCartTransform) GetTypename() *string { return v.Typename }

// findImageByIdNodeCashTrackingAdjustment includes the requested fields of the GraphQL type CashTrackingAdjustment.
// The GraphQL type's documentation follows.
//
// Tracks an adjustment to the cash in a cash tracking session for a point of sale device over the course of a shift.
type findImageByIdNodeCashTrackingAdjustment struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCashTrackingAdjustment.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCashTrackingAdjustment) GetTypename() *string { return v.Typename }

// findImageByIdNodeCashTrackingSession includes the requested fields of the GraphQL type CashTrackingSession.
// The GraphQL type's documentation follows.
//
// Tracks the balance in a cash drawer for a point of sale device over the course of a shift.
type findImageByIdNodeCashTrackingSession struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCashTrackingSession.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCashTrackingSession) GetTypename() *string { return v.Typename }

// findImageByIdNodeCatalogCsvOperation includes the requested fields of the GraphQL type CatalogCsvOperation.
// The GraphQL type's documentation follows.
//
// A catalog csv operation represents a CSV file import.
type findImageByIdNodeCatalogCsvOperation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCatalogCsvOperation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCatalogCsvOperation) GetTypename() *string { return v.Typename }

// findImageByIdNodeChannel includes the requested fields of the GraphQL type Channel.
// The GraphQL type's documentation follows.
//
// A channel represents an app where you sell a group of products and collections.
// A channel can be a platform or marketplace such as Facebook or Pinterest, an online store, or POS.
type findImageByIdNodeChannel struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeChannel.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeChannel) GetTypename() *string { return v.Typename }

// findImageByIdNodeChannelDefinition includes the requested fields of the GraphQL type ChannelDefinition.
// The GraphQL type's documentation follows.
//
// A channel definition represents channels surfaces on the platform.
// A channel definition can be a platform or a subsegment of it such as Facebook
// Home, Instagram Live, Instagram Shops, or WhatsApp chat.
type findImageByIdNodeChannelDefinition struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeChannelDefinition.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeChannelDefinition) GetTypename() *string { return v.Typename }

// findImageByIdNodeChannelInformation includes the requested fields of the GraphQL type ChannelInformation.
// The GraphQL type's documentation follows.
//
// Contains the information for a given sales channel.
type findImageByIdNodeChannelInformation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeChannelInformation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeChannelInformation) GetTypename() *string { return v.Typename }

// findImageByIdNodeCheckoutProfile includes the requested fields of the GraphQL type CheckoutProfile.
// The GraphQL type's documentation follows.
//
// A checkout profile defines the branding settings and the UI extensions for a
// store's checkout. A checkout profile could be published or draft. A store might
// have at most one published checkout profile, which is used to render their live
// checkout. The store could also have multiple draft profiles that were created,
// previewed, and published using the admin checkout editor.
type findImageByIdNodeCheckoutProfile struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCheckoutProfile.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCheckoutProfile) GetTypename() *string { return v.Typename }

// findImageByIdNodeCollection includes the requested fields of the GraphQL type Collection.
// The GraphQL type's documentation follows.
//
// The `Collection` object represents a group of [products](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product)
// that merchants can organize to make their stores easier to browse and help customers find related products.
// Collections serve as the primary way to categorize and display products across
// [online stores](https://shopify.dev/docs/apps/build/online-store),
// [sales channels](https://shopify.dev/docs/apps/build/sales-channels), and marketing campaigns.
//
// There are two types of collections:
//
// - **[Custom (manual) collections](https://help.shopify.com/manual/products/collections/manual-shopify-collection)**:
// You specify the products to include in a collection.
// - **[Smart (automated) collections](https://help.shopify.com/manual/products/collections/automated-collections)**:
// You define rules, and products matching those rules are automatically included
// in the collection.
//
// The `Collection` object provides information to:
//
// - Organize products by category, season, or promotion.
// - Automate product grouping using rules (for example, by tag, type, or price).
// - Configure product sorting and display order (for example, alphabetical, best-selling, price, or manual).
// - Manage collection visibility and publication across sales channels.
// - Add rich descriptions, images, and metadata to enhance discovery.
//
// > Note:
// > Collections are unpublished by default. To make them available to customers,
// use the [`publishablePublish`](https://shopify.dev/docs/api/admin-graphql/latest/mutations/publishablePublish)
// mutation after creation.
//
// Collections can be displayed in a store with Shopify's theme system through [Liquid templates](https://shopify.dev/docs/storefronts/themes/architecture/templates/collection)
// and can be customized with [template suffixes](https://shopify.dev/docs/storefronts/themes/architecture/templates/alternate-templates)
// for unique layouts. They also support advanced features like translated content, resource feedback,
// and contextual publication for location-based catalogs.
//
// Learn about [using metafields with smart collections](https://shopify.dev/docs/apps/build/custom-data/metafields/use-metafield-capabilities).
type findImageByIdNodeCollection struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCollection.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCollection) GetTypename() *string { return v.Typename }

// findImageByIdNodeComment includes the requested fields of the GraphQL type Comment.
// The GraphQL type's documentation follows.
//
// A comment on an article.
type findImageByIdNodeComment struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeComment.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeComment) GetTypename() *string { return v.Typename }

// findImageByIdNodeCommentEvent includes the requested fields of the GraphQL type CommentEvent.
// The GraphQL type's documentation follows.
//
// Comment events are generated by staff members of a shop.
// They are created when a staff member adds a comment to the timeline of an order, draft order, customer, or transfer.
type findImageByIdNodeCommentEvent struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCommentEvent.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCommentEvent) GetTypename() *string { return v.Typename }

// findImageByIdNodeCompany includes the requested fields of the GraphQL type Company.
// The GraphQL type's documentation follows.
//
// Represents information about a company which is also a customer of the shop.
type findImageByIdNodeCompany struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCompany.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCompany) GetTypename() *string { return v.Typename }

// findImageByIdNodeCompanyAddress includes the requested fields of the GraphQL type CompanyAddress.
// The GraphQL type's documentation follows.
//
// Represents a billing or shipping address for a company location.
type findImageByIdNodeCompanyAddress struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCompanyAddress.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCompanyAddress) GetTypename() *string { return v.Typename }

// findImageByIdNodeCompanyContact includes the requested fields of the GraphQL type CompanyContact.
// The GraphQL type's documentation follows.
//
// A person that acts on behalf of company associated to [a
// customer](https://shopify.dev/api/admin-graphql/latest/objects/customer).
type findImageByIdNodeCompanyContact struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCompanyContact.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCompanyContact) GetTypename() *string { return v.Typename }

// findImageByIdNodeCompanyContactRole includes the requested fields of the GraphQL type CompanyContactRole.
// The GraphQL type's documentation follows.
//
// The role for a [company contact](https://shopify.dev/api/admin-graphql/latest/objects/companycontact).
type findImageByIdNodeCompanyContactRole struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCompanyContactRole.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCompanyContactRole) GetTypename() *string { return v.Typename }

// findImageByIdNodeCompanyContactRoleAssignment includes the requested fields of the GraphQL type CompanyContactRoleAssignment.
// The GraphQL type's documentation follows.
//
// The CompanyContactRoleAssignment describes the company and location associated to a company contact's role.
type findImageByIdNodeCompanyContactRoleAssignment struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCompanyContactRoleAssignment.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCompanyContactRoleAssignment) GetTypename() *string { return v.Typename }

// findImageByIdNodeCompanyLocation includes the requested fields of the GraphQL type CompanyLocation.
// The GraphQL type's documentation follows.
//
// A location or branch of a [company that's a
// customer](https://shopify.dev/api/admin-graphql/latest/objects/company) of the
// shop. Configuration of B2B relationship, for example prices lists and checkout
// settings, may be done for a location.
type findImageByIdNodeCompanyLocation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCompanyLocation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCompanyLocation) GetTypename() *string { return v.Typename }

// findImageByIdNodeCompanyLocationCatalog includes the requested fields of the GraphQL type CompanyLocationCatalog.
// The GraphQL type's documentation follows.
//
// A list of products with publishing and pricing information associated with company locations.
type findImageByIdNodeCompanyLocationCatalog struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCompanyLocationCatalog.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCompanyLocationCatalog) GetTypename() *string { return v.Typename }

// findImageByIdNodeCompanyLocationStaffMemberAssignment includes the requested fields of the GraphQL type CompanyLocationStaffMemberAssignment.
// The GraphQL type's documentation follows.
//
// A representation of store's staff member who is assigned to a [company
// location](https://shopify.dev/api/admin-graphql/latest/objects/CompanyLocation)
// of the shop. The staff member's actions will be limited to objects associated
// with the assigned company location.
type findImageByIdNodeCompanyLocationStaffMemberAssignment struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCompanyLocationStaffMemberAssignment.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCompanyLocationStaffMemberAssignment) GetTypename() *string {
	return v.Typename
}

// findImageByIdNodeConsentPolicy includes the requested fields of the GraphQL type ConsentPolicy.
// The GraphQL type's documentation follows.
//
// A consent policy describes the level of consent that the merchant requires from the user before actually
// collecting and processing the data.
type findImageByIdNodeConsentPolicy struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeConsentPolicy.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeConsentPolicy) GetTypename() *string { return v.Typename }

// findImageByIdNodeCurrencyExchangeAdjustment includes the requested fields of the GraphQL type CurrencyExchangeAdjustment.
// The GraphQL type's documentation follows.
//
// Represents a currency exchange adjustment applied to an order transaction.
type findImageByIdNodeCurrencyExchangeAdjustment struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCurrencyExchangeAdjustment.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCurrencyExchangeAdjustment) GetTypename() *string { return v.Typename }

// findImageByIdNodeCustomer includes the requested fields of the GraphQL type Customer.
// The GraphQL type's documentation follows.
//
// Represents information about a customer of the shop, such as the customer's contact details, their order
// history, and whether they've agreed to receive marketing material by email.
//
// **Caution:** Only use this data if it's required for your app's functionality.
// Shopify will restrict [access to
// scopes](https://shopify.dev/api/usage/access-scopes) for apps that don't have a
// legitimate use for the associated data.
type findImageByIdNodeCustomer struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCustomer.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCustomer) GetTypename() *string { return v.Typename }

// findImageByIdNodeCustomerAccountAppExtensionPage includes the requested fields of the GraphQL type CustomerAccountAppExtensionPage.
// The GraphQL type's documentation follows.
//
// An app extension page for the customer account navigation menu.
type findImageByIdNodeCustomerAccountAppExtensionPage struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCustomerAccountAppExtensionPage.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCustomerAccountAppExtensionPage) GetTypename() *string { return v.Typename }

// findImageByIdNodeCustomerAccountNativePage includes the requested fields of the GraphQL type CustomerAccountNativePage.
// The GraphQL type's documentation follows.
//
// A native page for the customer account navigation menu.
type findImageByIdNodeCustomerAccountNativePage struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCustomerAccountNativePage.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCustomerAccountNativePage) GetTypename() *string { return v.Typename }

// findImageByIdNodeCustomerPaymentMethod includes the requested fields of the GraphQL type CustomerPaymentMethod.
// The GraphQL type's documentation follows.
//
// A customer's payment method.
type findImageByIdNodeCustomerPaymentMethod struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCustomerPaymentMethod.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCustomerPaymentMethod) GetTypename() *string { return v.Typename }

// findImageByIdNodeCustomerSegmentMembersQuery includes the requested fields of the GraphQL type CustomerSegmentMembersQuery.
// The GraphQL type's documentation follows.
//
// A job to determine a list of members, such as customers, that are associated with an individual segment.
type findImageByIdNodeCustomerSegmentMembersQuery struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCustomerSegmentMembersQuery.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCustomerSegmentMembersQuery) GetTypename() *string { return v.Typename }

// findImageByIdNodeCustomerVisit includes the requested fields of the GraphQL type CustomerVisit.
// The GraphQL type's documentation follows.
//
// Represents a customer's session visiting a shop's online store, including
// information about the marketing activity attributed to starting the session.
type findImageByIdNodeCustomerVisit struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeCustomerVisit.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeCustomerVisit) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryCarrierService includes the requested fields of the GraphQL type DeliveryCarrierService.
// The GraphQL type's documentation follows.
//
// A carrier service (also known as a carrier calculated service or shipping
// service) provides real-time shipping rates to Shopify. Some common carrier
// services include Canada Post, FedEx, UPS, and USPS. The term **carrier** is
// often used interchangeably with the terms **shipping company** and **rate provider**.
//
// Using the CarrierService resource, you can add a carrier service to a shop and
// then provide a list of applicable shipping rates at checkout. You can even use
// the cart data to adjust shipping rates and offer shipping discounts based on
// what is in the customer's cart.
//
// ## Requirements for accessing the CarrierService resource
// To access the CarrierService resource, add the `write_shipping` permission to
// your app's requested scopes. For more information, see [API access
// scopes](https://shopify.dev/docs/admin-api/access-scopes).
//
// Your app's request to create a carrier service will fail unless the store
// installing your carrier service meets one of the following requirements:
// * It's on the Advanced Shopify plan or higher.
// * It's on the Shopify plan with yearly billing, or the carrier service feature
// has been added to the store for a monthly fee. For more information, contact
// [Shopify Support](https://help.shopify.com/questions).
// * It's a development store.
//
// > Note:
// > If a store changes its Shopify plan, then the store's association with a
// carrier service is deactivated if the store no long meets one of the
// requirements above.
//
// ## Providing shipping rates to Shopify
// When adding a carrier service to a store, you need to provide a POST endpoint
// rooted in the `callbackUrl` property where Shopify can retrieve applicable
// shipping rates. The callback URL should be a public endpoint that expects these
// requests from Shopify.
//
// ### Example shipping rate request sent to a carrier service
//
// ```json
// {
// "rate": {
// "origin": {
// "country": "CA",
// "postal_code": "K2P1L4",
// "province": "ON",
// "city": "Ottawa",
// "name": null,
// "address1": "150 Elgin St.",
// "address2": "",
// "address3": null,
// "phone": null,
// "fax": null,
// "email": null,
// "address_type": null,
// "company_name": "Jamie D's Emporium"
// },
// "destination": {
// "country": "CA",
// "postal_code": "K1M1M4",
// "province": "ON",
// "city": "Ottawa",
// "name": "Bob Norman",
// "address1": "24 Sussex Dr.",
// "address2": "",
// "address3": null,
// "phone": null,
// "fax": null,
// "email": null,
// "address_type": null,
// "company_name": null
// },
// "items": [{
// "name": "Short Sleeve T-Shirt",
// "sku": "",
// "quantity": 1,
// "grams": 1000,
// "price": 1999,
// "vendor": "Jamie D's Emporium",
// "requires_shipping": true,
// "taxable": true,
// "fulfillment_service": "manual",
// "properties": null,
// "product_id": 48447225880,
// "variant_id": 258644705304
// }],
// "currency": "USD",
// "locale": "en"
// }
// }
// ```
//
// ### Example response
// ```json
// {
// "rates": [
// {
// "service_name": "canadapost-overnight",
// "service_code": "ON",
// "total_price": "1295",
// "description": "This is the fastest option by far",
// "currency": "CAD",
// "min_delivery_date": "2013-04-12 14:48:45 -0400",
// "max_delivery_date": "2013-04-12 14:48:45 -0400"
// },
// {
// "service_name": "fedex-2dayground",
// "service_code": "2D",
// "total_price": "2934",
// "currency": "USD",
// "min_delivery_date": "2013-04-12 14:48:45 -0400",
// "max_delivery_date": "2013-04-12 14:48:45 -0400"
// },
// {
// "service_name": "fedex-priorityovernight",
// "service_code": "1D",
// "total_price": "3587",
// "currency": "USD",
// "min_delivery_date": "2013-04-12 14:48:45 -0400",
// "max_delivery_date": "2013-04-12 14:48:45 -0400"
// }
// ]
// }
// ```
//
// The `address3`, `fax`, `address_type`, and `company_name` fields are returned by
// specific [ActiveShipping](https://github.com/Shopify/active_shipping) providers.
// For API-created carrier services, you should use only the following shipping
// address fields:
// * `address1`
// * `address2`
// * `city`
// * `zip`
// * `province`
// * `country`
//
// Other values remain as `null` and are not sent to the callback URL.
//
// ### Response fields
//
// When Shopify requests shipping rates using your callback URL, the response
// object `rates` must be a JSON array of objects with the following fields.
// Required fields must be included in the response for the carrier service
// integration to work properly.
//
// | Field                   | Required | Description
//
// |
// | ----------------------- | -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
// | `service_name`          | Yes      | The name of the rate, which customers see
// at checkout. For example: `Expedited Mail`.
// |
// | `description`           | Yes      | A description of the rate, which
// customers see at checkout. For example: `Includes tracking and insurance`.
// |
// | `service_code`          | Yes      | A unique code associated with the rate.
// For example: `expedited_mail`.
// |
// | `currency`              | Yes      | The currency of the shipping rate.
//
// |
// | `total_price`           | Yes      | The total price expressed in subunits. If
// the currency doesn't use subunits, then the value must be multiplied by 100. For
// example: `"total_price": 500` for 5.00 CAD, `"total_price": 100000` for 1000 JPY. |
// | `phone_required`        | No       | Whether the customer must provide a phone
// number at checkout.
// |
// | `min_delivery_date`     | No       | The earliest delivery date for the
// displayed rate.
// |
// | `max_delivery_date`     | No       | The latest delivery date for the
// displayed rate to still be valid.
// |
//
// ### Special conditions
//
// * To indicate that this carrier service cannot handle this shipping request,
// return an empty array and any successful (20x) HTTP code.
// * To force backup rates instead, return a 40x or 50x HTTP code with any content.
// A good choice is the regular 404 Not Found code.
// * Redirects (30x codes) will only be followed for the same domain as the
// original callback URL. Attempting to redirect to a different domain will trigger backup rates.
// * There is no retry mechanism. The response must be successful on the first try,
// within the time budget listed below. Timeouts or errors will trigger backup rates.
//
// ## Response Timeouts
//
// The read timeout for rate requests are dynamic, based on the number of requests
// per minute (RPM). These limits are applied to each shop-app pair. The timeout
// values are as follows.
//
// | RPM Range     | Timeout    |
// | ------------- | ---------- |
// | Under 1500    | 10s        |
// | 1500 to 3000  | 5s         |
// | Over 3000     | 3s         |
//
// > Note:
// > These values are upper limits and should not be interpretted as a goal to
// develop towards. Shopify is constantly evaluating the performance of the
// platform and working towards improving resilience as well as app capabilities.
// As such, these numbers may be adjusted outside of our normal versioning timelines.
//
// ## Server-side caching of requests
// Shopify provides server-side caching to reduce the number of requests it makes.
// Any shipping rate request that identically matches the following fields will be
// retrieved from Shopify's cache of the initial response:
// * variant IDs
// * default shipping box weight and dimensions
// * variant quantities
// * carrier service ID
// * origin address
// * destination address
// * item weights and signatures
//
// If any of these fields differ, or if the cache has expired since the original
// request, then new shipping rates are requested. The cache expires 15 minutes
// after rates are successfully returned. If an error occurs, then the cache
// expires after 30 seconds.
type findImageByIdNodeDeliveryCarrierService struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryCarrierService.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryCarrierService) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryCondition includes the requested fields of the GraphQL type DeliveryCondition.
// The GraphQL type's documentation follows.
//
// A condition that must pass for a delivery method definition to be applied to an order.
type findImageByIdNodeDeliveryCondition struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryCondition.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryCondition) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryCountry includes the requested fields of the GraphQL type DeliveryCountry.
// The GraphQL type's documentation follows.
//
// A country that is used to define a shipping zone.
type findImageByIdNodeDeliveryCountry struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryCountry.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryCountry) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryCustomization includes the requested fields of the GraphQL type DeliveryCustomization.
// The GraphQL type's documentation follows.
//
// A delivery customization.
type findImageByIdNodeDeliveryCustomization struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryCustomization.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryCustomization) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryLocationGroup includes the requested fields of the GraphQL type DeliveryLocationGroup.
// The GraphQL type's documentation follows.
//
// A location group is a collection of locations. They share zones and delivery methods across delivery
// profiles.
type findImageByIdNodeDeliveryLocationGroup struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryLocationGroup.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryLocationGroup) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryMethod includes the requested fields of the GraphQL type DeliveryMethod.
// The GraphQL type's documentation follows.
//
// The delivery method used by a fulfillment order.
type findImageByIdNodeDeliveryMethod struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryMethod.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryMethod) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryMethodDefinition includes the requested fields of the GraphQL type DeliveryMethodDefinition.
// The GraphQL type's documentation follows.
//
// A method definition contains the delivery rate and the conditions that must be met for the method to be
// applied.
type findImageByIdNodeDeliveryMethodDefinition struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryMethodDefinition.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryMethodDefinition) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryParticipant includes the requested fields of the GraphQL type DeliveryParticipant.
// The GraphQL type's documentation follows.
//
// A participant defines carrier-calculated rates for shipping services
// with a possible merchant-defined fixed fee or a percentage-of-rate fee.
type findImageByIdNodeDeliveryParticipant struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryParticipant.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryParticipant) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryProfile includes the requested fields of the GraphQL type DeliveryProfile.
// The GraphQL type's documentation follows.
//
// A shipping profile. In Shopify, a shipping profile is a set of shipping rates
// scoped to a set of products or variants that can be shipped from selected
// locations to zones. Learn more about [building with delivery profiles](https://shopify.dev/apps/build/purchase-options/deferred/delivery-and-deferment/build-delivery-profiles).
type findImageByIdNodeDeliveryProfile struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryProfile.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryProfile) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryProfileItem includes the requested fields of the GraphQL type DeliveryProfileItem.
// The GraphQL type's documentation follows.
//
// A product and the subset of associated variants that are part of this delivery profile.
type findImageByIdNodeDeliveryProfileItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryProfileItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryProfileItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryPromiseParticipant includes the requested fields of the GraphQL type DeliveryPromiseParticipant.
// The GraphQL type's documentation follows.
//
// Returns enabled delivery promise participants.
type findImageByIdNodeDeliveryPromiseParticipant struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryPromiseParticipant.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryPromiseParticipant) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryPromiseProvider includes the requested fields of the GraphQL type DeliveryPromiseProvider.
// The GraphQL type's documentation follows.
//
// A delivery promise provider. Currently restricted to select approved delivery promise partners.
type findImageByIdNodeDeliveryPromiseProvider struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryPromiseProvider.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryPromiseProvider) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryProvince includes the requested fields of the GraphQL type DeliveryProvince.
// The GraphQL type's documentation follows.
//
// A region that is used to define a shipping zone.
type findImageByIdNodeDeliveryProvince struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryProvince.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryProvince) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryRateDefinition includes the requested fields of the GraphQL type DeliveryRateDefinition.
// The GraphQL type's documentation follows.
//
// The merchant-defined rate of the [DeliveryMethodDefinition](https://shopify.dev/api/admin-graphql/latest/objects/DeliveryMethodDefinition).
type findImageByIdNodeDeliveryRateDefinition struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryRateDefinition.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryRateDefinition) GetTypename() *string { return v.Typename }

// findImageByIdNodeDeliveryZone includes the requested fields of the GraphQL type DeliveryZone.
// The GraphQL type's documentation follows.
//
// A zone is a group of countries that have the same shipping rates. Customers can
// order products from a store only if they choose a shipping destination that's
// included in one of the store's zones.
type findImageByIdNodeDeliveryZone struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDeliveryZone.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDeliveryZone) GetTypename() *string { return v.Typename }

// findImageByIdNodeDiscountAutomaticBxgy includes the requested fields of the GraphQL type DiscountAutomaticBxgy.
// The GraphQL type's documentation follows.
//
// The `DiscountAutomaticBxgy` object lets you manage
// [buy X get Y discounts (BXGY)](https://help.shopify.com/manual/discounts/discount-types/buy-x-get-y)
// that are automatically applied on a cart and at checkout. BXGY discounts incentivize customers by offering
// them additional items at a discounted price or for free when they purchase a specified quantity of items.
//
// The `DiscountAutomaticBxgy` object stores information about automatic BXGY discounts that apply to
// specific [products and variants](https://shopify.dev/docs/api/admin-graphql/latest/objects/DiscountProducts),
// [collections](https://shopify.dev/docs/api/admin-graphql/latest/objects/DiscountCollections),
// or [all items in a cart](https://shopify.dev/docs/api/admin-graphql/latest/objects/AllDiscountItems).
//
// Learn more about working with [Shopify's discount model](https://shopify.dev/docs/apps/build/discounts),
// including limitations and considerations.
//
// > Note:
// > The [`DiscountCodeBxgy`](https://shopify.dev/docs/api/admin-graphql/latest/objects/DiscountCodeBxgy)
// object has similar functionality to the `DiscountAutomaticBxgy` object, but customers need to enter a code to
// receive a discount.
type findImageByIdNodeDiscountAutomaticBxgy struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDiscountAutomaticBxgy.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDiscountAutomaticBxgy) GetTypename() *string { return v.Typename }

// findImageByIdNodeDiscountAutomaticNode includes the requested fields of the GraphQL type DiscountAutomaticNode.
// The GraphQL type's documentation follows.
//
// The `DiscountAutomaticNode` object enables you to manage [automatic discounts](https://help.shopify.com/manual/discounts/discount-types#automatic-discounts)
// that are applied when an order meets specific criteria. You can create amount
// off, free shipping, or buy X get Y automatic discounts. For example, you can
// offer customers a free shipping discount that applies when conditions are met.
// Or you can offer customers a buy X get Y discount that's automatically applied
// when customers spend a specified amount of money, or a specified quantity of products.
//
// Learn more about working with [Shopify's discount model](https://shopify.dev/docs/apps/build/discounts),
// including related queries, mutations, limitations, and considerations.
type findImageByIdNodeDiscountAutomaticNode struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDiscountAutomaticNode.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDiscountAutomaticNode) GetTypename() *string { return v.Typename }

// findImageByIdNodeDiscountCodeNode includes the requested fields of the GraphQL type DiscountCodeNode.
// The GraphQL type's documentation follows.
//
// The `DiscountCodeNode` object enables you to manage [code discounts](https://help.shopify.com/manual/discounts/discount-types#discount-codes)
// that are applied when customers enter a code at checkout. For example, you can
// offer discounts where customers have to enter a code to redeem an amount off
// discount on products, variants, or collections in a store. Or, you can offer
// discounts where customers have to enter a code to get free shipping. Merchants
// can create and share discount codes individually with customers.
//
// Learn more about working with [Shopify's discount model](https://shopify.dev/docs/apps/build/discounts),
// including related queries, mutations, limitations, and considerations.
type findImageByIdNodeDiscountCodeNode struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDiscountCodeNode.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDiscountCodeNode) GetTypename() *string { return v.Typename }

// findImageByIdNodeDiscountNode includes the requested fields of the GraphQL type DiscountNode.
// The GraphQL type's documentation follows.
//
// The `DiscountNode` object enables you to manage
// [discounts](https://help.shopify.com/manual/discounts), which are applied at
// checkout or on a cart.
//
// Discounts are a way for merchants to promote sales and special offers, or as
// customer loyalty rewards. Discounts can apply to [orders, products, or
// shipping](https://shopify.dev/docs/apps/build/discounts#discount-classes), and
// can be either automatic or code-based. For example, you can offer customers a
// buy X get Y discount that's automatically applied when purchases meet specific
// criteria. Or, you can offer discounts where customers have to enter a code to
// redeem an amount off discount on products, variants, or collections in a store.
//
// Learn more about working with [Shopify's discount model](https://shopify.dev/docs/apps/build/discounts),
// including related mutations, limitations, and considerations.
type findImageByIdNodeDiscountNode struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDiscountNode.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDiscountNode) GetTypename() *string { return v.Typename }

// findImageByIdNodeDiscountRedeemCodeBulkCreation includes the requested fields of the GraphQL type DiscountRedeemCodeBulkCreation.
// The GraphQL type's documentation follows.
//
// The properties and status of a bulk discount redeem code creation operation.
type findImageByIdNodeDiscountRedeemCodeBulkCreation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDiscountRedeemCodeBulkCreation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDiscountRedeemCodeBulkCreation) GetTypename() *string { return v.Typename }

// findImageByIdNodeDomain includes the requested fields of the GraphQL type Domain.
// The GraphQL type's documentation follows.
//
// A unique string that represents the address of a Shopify store on the Internet.
type findImageByIdNodeDomain struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDomain.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDomain) GetTypename() *string { return v.Typename }

// findImageByIdNodeDraftOrder includes the requested fields of the GraphQL type DraftOrder.
// The GraphQL type's documentation follows.
//
// An order that a merchant creates on behalf of a customer. Draft orders are
// useful for merchants that need to do the following tasks:
//
// - Create new orders for sales made by phone, in person, by chat, or elsewhere.
// When a merchant accepts payment for a draft order, an order is created.
// - Send invoices to customers to pay with a secure checkout link.
// - Use custom items to represent additional costs or products that aren't displayed in a shop's inventory.
// - Re-create orders manually from active sales channels.
// - Sell products at discount or wholesale rates.
// - Take pre-orders.
//
// For draft orders in multiple currencies `presentment_money` is the source of
// truth for what a customer is going to be charged and `shop_money` is an estimate
// of what the merchant might receive in their shop currency.
//
// **Caution:** Only use this data if it's required for your app's functionality.
// Shopify will restrict [access to
// scopes](https://shopify.dev/api/usage/access-scopes) for apps that don't have a
// legitimate use for the associated data.
//
// Draft orders created on or after April 1, 2025 will be automatically purged after one year of inactivity.
type findImageByIdNodeDraftOrder struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDraftOrder.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDraftOrder) GetTypename() *string { return v.Typename }

// findImageByIdNodeDraftOrderLineItem includes the requested fields of the GraphQL type DraftOrderLineItem.
// The GraphQL type's documentation follows.
//
// The line item for a draft order.
type findImageByIdNodeDraftOrderLineItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDraftOrderLineItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDraftOrderLineItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeDraftOrderTag includes the requested fields of the GraphQL type DraftOrderTag.
// The GraphQL type's documentation follows.
//
// Represents a draft order tag.
type findImageByIdNodeDraftOrderTag struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDraftOrderTag.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDraftOrderTag) GetTypename() *string { return v.Typename }

// findImageByIdNodeDuty includes the requested fields of the GraphQL type Duty.
// The GraphQL type's documentation follows.
//
// The duty details for a line item.
type findImageByIdNodeDuty struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeDuty.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeDuty) GetTypename() *string { return v.Typename }

// findImageByIdNodeExchangeLineItem includes the requested fields of the GraphQL type ExchangeLineItem.
// The GraphQL type's documentation follows.
//
// An item for exchange.
type findImageByIdNodeExchangeLineItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeExchangeLineItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeExchangeLineItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeExchangeV2 includes the requested fields of the GraphQL type ExchangeV2.
// The GraphQL type's documentation follows.
//
// An exchange where existing items on an order are returned and new items are added to the order.
type findImageByIdNodeExchangeV2 struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeExchangeV2.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeExchangeV2) GetTypename() *string { return v.Typename }

// findImageByIdNodeExternalVideo includes the requested fields of the GraphQL type ExternalVideo.
// The GraphQL type's documentation follows.
//
// Represents a video hosted outside of Shopify.
type findImageByIdNodeExternalVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeExternalVideo.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeExternalVideo) GetTypename() *string { return v.Typename }

// findImageByIdNodeFulfillment includes the requested fields of the GraphQL type Fulfillment.
// The GraphQL type's documentation follows.
//
// Represents a fulfillment.
// In Shopify, a fulfillment represents a shipment of one or more items in an order.
// When an order has been completely fulfilled, it means that all the items that are included
// in the order have been sent to the customer.
// There can be more than one fulfillment for an order.
type findImageByIdNodeFulfillment struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeFulfillment.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeFulfillment) GetTypename() *string { return v.Typename }

// findImageByIdNodeFulfillmentConstraintRule includes the requested fields of the GraphQL type FulfillmentConstraintRule.
// The GraphQL type's documentation follows.
//
// A fulfillment constraint rule.
type findImageByIdNodeFulfillmentConstraintRule struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeFulfillmentConstraintRule.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeFulfillmentConstraintRule) GetTypename() *string { return v.Typename }

// findImageByIdNodeFulfillmentEvent includes the requested fields of the GraphQL type FulfillmentEvent.
// The GraphQL type's documentation follows.
//
// The fulfillment event that describes the fulfilllment status at a particular time.
type findImageByIdNodeFulfillmentEvent struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeFulfillmentEvent.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeFulfillmentEvent) GetTypename() *string { return v.Typename }

// findImageByIdNodeFulfillmentHold includes the requested fields of the GraphQL type FulfillmentHold.
// The GraphQL type's documentation follows.
//
// A fulfillment hold currently applied on a fulfillment order.
type findImageByIdNodeFulfillmentHold struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeFulfillmentHold.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeFulfillmentHold) GetTypename() *string { return v.Typename }

// findImageByIdNodeFulfillmentLineItem includes the requested fields of the GraphQL type FulfillmentLineItem.
// The GraphQL type's documentation follows.
//
// Represents a line item from an order that's included in a fulfillment.
type findImageByIdNodeFulfillmentLineItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeFulfillmentLineItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeFulfillmentLineItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeFulfillmentOrder includes the requested fields of the GraphQL type FulfillmentOrder.
// The GraphQL type's documentation follows.
//
// The FulfillmentOrder object represents either an item or a group of items in an
// [Order](https://shopify.dev/api/admin-graphql/latest/objects/Order)
// that are expected to be fulfilled from the same location.
// There can be more than one fulfillment order for an
// [order](https://shopify.dev/api/admin-graphql/latest/objects/Order)
// at a given location.
//
// {{ '/api/reference/fulfillment_order_relationships.png' | image }}
//
// Fulfillment orders represent the work which is intended to be done in relation to an order.
// When fulfillment has started for one or more line items, a
// [Fulfillment](https://shopify.dev/api/admin-graphql/latest/objects/Fulfillment)
// is created by a merchant or third party to represent the ongoing or completed work of fulfillment.
//
// [See below for more details on creating fulfillments](#the-lifecycle-of-a-fulfillment-order-at-a-location-which-is-managed-by-a-fulfillment-service).
//
// > Note:
// > Shopify creates fulfillment orders automatically when an order is created.
// > It is not possible to manually create fulfillment orders.
// >
// > [See below for more details on the lifecycle of a fulfillment order](#the-lifecycle-of-a-fulfillment-order).
//
// ## Retrieving fulfillment orders
//
// ### Fulfillment orders from an order
//
// All fulfillment orders related to a given order can be retrieved with the
// [Order.fulfillmentOrders](https://shopify.dev/api/admin-graphql/latest/objects/Order#connection-order-fulfillmentorders)
// connection.
//
// [API access scopes](#api-access-scopes)
// govern which fulfillments orders are returned to clients.
// An API client will only receive a subset of the fulfillment orders which belong to an order
// if they don't have the necessary access scopes to view all of the fulfillment orders.
//
// ### Fulfillment orders assigned to the app for fulfillment
//
// Fulfillment service apps can retrieve the fulfillment orders which have been assigned to their locations with the
// [assignedFulfillmentOrders](https://shopify.dev/api/admin-graphql/2024-07/objects/queryroot#connection-assignedfulfillmentorders)
// connection.
// Use the `assignmentStatus` argument to control whether all assigned fulfillment orders
// should be returned or only those where a merchant has sent a
// [fulfillment request](https://shopify.dev/api/admin-graphql/latest/objects/FulfillmentOrderMerchantRequest)
// and it has yet to be responded to.
//
// The API client must be granted the `read_assigned_fulfillment_orders` access scope to access
// the assigned fulfillment orders.
//
// ### All fulfillment orders
//
// Apps can retrieve all fulfillment orders with the
// [fulfillmentOrders](https://shopify.dev/api/admin-graphql/latest/queries/fulfillmentOrders)
// query. This query returns all assigned, merchant-managed, and third-party fulfillment orders on the shop,
// which are accessible to the app according to the
// [fulfillment order access scopes](#api-access-scopes) it was granted with.
//
// ## The lifecycle of a fulfillment order
//
// ### Fulfillment Order Creation
//
// After an order is created, a background worker performs the order routing process which determines
// which locations will be responsible for fulfilling the purchased items.
// Once the order routing process is complete, one or more fulfillment orders will be created
// and assigned to these locations. It is not possible to manually create fulfillment orders.
//
// Once a fulfillment order has been created, it will have one of two different lifecycles depending on
// the type of location which the fulfillment order is assigned to.
//
// ### The lifecycle of a fulfillment order at a merchant managed location
//
// Fulfillment orders are completed by creating
// [fulfillments](https://shopify.dev/api/admin-graphql/latest/objects/Fulfillment).
// Fulfillments represents the work done.
//
// For digital products a merchant or an order management app would create a fulfilment once the digital asset
// has been provisioned.
// For example, in the case of a digital gift card, a merchant would to do this once
// the gift card has been activated - before the email has been shipped.
//
// On the other hand, for a traditional shipped order,
// a merchant or an order management app would create a fulfillment after picking and packing the items relating
// to a fulfillment order, but before the courier has collected the goods.
//
// [Learn about managing fulfillment orders as an order management app](https://shopify.dev/apps/fulfillment/order-management-apps/manage-fulfillments).
//
// ### The lifecycle of a fulfillment order at a location which is managed by a fulfillment service
//
// For fulfillment orders which are assigned to a location that is managed by a fulfillment service,
// a merchant or an Order Management App can
// [send a fulfillment request](https://shopify.dev/api/admin-graphql/latest/mutations/fulfillmentOrderSubmitFulfillmentRequest)
// to the fulfillment service which operates the location to request that they fulfill the associated items.
// A fulfillment service has the option to
// [accept](https://shopify.dev/api/admin-graphql/latest/mutations/fulfillmentOrderAcceptFulfillmentRequest)
// or [reject](https://shopify.dev/api/admin-graphql/latest/mutations/fulfillmentOrderRejectFulfillmentRequest)
// this fulfillment request.
//
// Once the fulfillment service has accepted the request, the request can no longer be cancelled by the merchant
// or order management app and instead a
// [cancellation request must be submitted](https://shopify.dev/api/admin-graphql/latest/mutations/fulfillmentOrderSubmitCancellationRequest)
// to the fulfillment service.
//
// Once a fulfillment service accepts a fulfillment request,
// then after they are ready to pack items and send them for delivery, they create fulfillments with the
// [fulfillmentCreate](https://shopify.dev/api/admin-graphql/unstable/mutations/fulfillmentCreate)
// mutation.
// They can provide tracking information right away or create fulfillments without it and then
// update the tracking information for fulfillments with the
// [fulfillmentTrackingInfoUpdate](https://shopify.dev/api/admin-graphql/unstable/mutations/fulfillmentTrackingInfoUpdate)
// mutation.
//
// [Learn about managing fulfillment orders as a fulfillment service](https://shopify.dev/apps/fulfillment/fulfillment-service-apps/manage-fulfillments).
//
// ## API access scopes
//
// Fulfillment orders are governed by the following API access scopes:
//
// * The `read_merchant_managed_fulfillment_orders` and
// `write_merchant_managed_fulfillment_orders` access scopes
// grant access to fulfillment orders assigned to merchant-managed locations.
// * The `read_assigned_fulfillment_orders` and `write_assigned_fulfillment_orders`
// access scopes are intended for fulfillment services.
// These scopes grant access to fulfillment orders assigned to locations that are being managed
// by fulfillment services.
// * The `read_third_party_fulfillment_orders` and `write_third_party_fulfillment_orders`
// access scopes grant access to fulfillment orders
// assigned to locations managed by other fulfillment services.
//
// ### Fulfillment service app access scopes
//
// Usually, **fulfillment services** have the `write_assigned_fulfillment_orders` access scope
// and don't have the `*_third_party_fulfillment_orders`
// or `*_merchant_managed_fulfillment_orders` access scopes.
// The app will only have access to the fulfillment orders assigned to their location
// (or multiple locations if the app registers multiple fulfillment services on the shop).
// The app will not have access to fulfillment orders assigned to merchant-managed locations
// or locations owned by other fulfillment service apps.
//
// ### Order management app access scopes
//
// **Order management apps** will usually request `write_merchant_managed_fulfillment_orders` and
// `write_third_party_fulfillment_orders` access scopes. This will allow them to manage all fulfillment orders
// on behalf of a merchant.
//
// If an app combines the functions of an order management app and a fulfillment service,
// then the app should request all
// access scopes to manage all assigned and all unassigned fulfillment orders.
//
// ## Notifications about fulfillment orders
//
// Fulfillment services are required to
// [register](https://shopify.dev/api/admin-graphql/latest/objects/FulfillmentService)
// a self-hosted callback URL which has a number of uses. One of these uses is that this callback URL will be notified
// whenever a merchant submits a fulfillment or cancellation request.
//
// Both merchants and apps can
// [subscribe](https://shopify.dev/apps/fulfillment/fulfillment-service-apps/manage-fulfillments#webhooks)
// to the
// [fulfillment order webhooks](https://shopify.dev/api/admin-graphql/latest/enums/WebhookSubscriptionTopic#value-fulfillmentorderscancellationrequestaccepted)
// to be notified whenever fulfillment order related domain events occur.
//
// [Learn about fulfillment workflows](https://shopify.dev/apps/fulfillment).
type findImageByIdNodeFulfillmentOrder struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeFulfillmentOrder.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeFulfillmentOrder) GetTypename() *string { return v.Typename }

// findImageByIdNodeFulfillmentOrderDestination includes the requested fields of the GraphQL type FulfillmentOrderDestination.
// The GraphQL type's documentation follows.
//
// Represents the destination where the items should be sent upon fulfillment.
type findImageByIdNodeFulfillmentOrderDestination struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeFulfillmentOrderDestination.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeFulfillmentOrderDestination) GetTypename() *string { return v.Typename }

// findImageByIdNodeFulfillmentOrderLineItem includes the requested fields of the GraphQL type FulfillmentOrderLineItem.
// The GraphQL type's documentation follows.
//
// Associates an order line item with quantities requiring fulfillment from the respective fulfillment order.
type findImageByIdNodeFulfillmentOrderLineItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeFulfillmentOrderLineItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeFulfillmentOrderLineItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeFulfillmentOrderMerchantRequest includes the requested fields of the GraphQL type FulfillmentOrderMerchantRequest.
// The GraphQL type's documentation follows.
//
// A request made by the merchant or an order management app to a fulfillment service
// for a fulfillment order.
type findImageByIdNodeFulfillmentOrderMerchantRequest struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeFulfillmentOrderMerchantRequest.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeFulfillmentOrderMerchantRequest) GetTypename() *string { return v.Typename }

// findImageByIdNodeGenericFile includes the requested fields of the GraphQL type GenericFile.
// The GraphQL type's documentation follows.
//
// Represents any file other than HTML.
type findImageByIdNodeGenericFile struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeGenericFile.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeGenericFile) GetTypename() *string { return v.Typename }

// findImageByIdNodeGiftCard includes the requested fields of the GraphQL type GiftCard.
// The GraphQL type's documentation follows.
//
// Represents an issued gift card.
type findImageByIdNodeGiftCard struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeGiftCard.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeGiftCard) GetTypename() *string { return v.Typename }

// findImageByIdNodeGiftCardCreditTransaction includes the requested fields of the GraphQL type GiftCardCreditTransaction.
// The GraphQL type's documentation follows.
//
// A credit transaction which increases the gift card balance.
type findImageByIdNodeGiftCardCreditTransaction struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeGiftCardCreditTransaction.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeGiftCardCreditTransaction) GetTypename() *string { return v.Typename }

// findImageByIdNodeGiftCardDebitTransaction includes the requested fields of the GraphQL type GiftCardDebitTransaction.
// The GraphQL type's documentation follows.
//
// A debit transaction which decreases the gift card balance.
type findImageByIdNodeGiftCardDebitTransaction struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeGiftCardDebitTransaction.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeGiftCardDebitTransaction) GetTypename() *string { return v.Typename }

// findImageByIdNodeInventoryAdjustmentGroup includes the requested fields of the GraphQL type InventoryAdjustmentGroup.
// The GraphQL type's documentation follows.
//
// Represents a group of adjustments made as part of the same operation.
type findImageByIdNodeInventoryAdjustmentGroup struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeInventoryAdjustmentGroup.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeInventoryAdjustmentGroup) GetTypename() *string { return v.Typename }

// findImageByIdNodeInventoryItem includes the requested fields of the GraphQL type InventoryItem.
// The GraphQL type's documentation follows.
//
// Represents the goods available to be shipped to a customer.
// It holds essential information about the goods, including SKU and whether it is tracked.
// Learn [more about the relationships between inventory objects](https://shopify.dev/docs/apps/build/orders-fulfillment/inventory-management-apps/manage-quantities-states#inventory-object-relationships).
type findImageByIdNodeInventoryItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeInventoryItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeInventoryItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeInventoryItemMeasurement includes the requested fields of the GraphQL type InventoryItemMeasurement.
// The GraphQL type's documentation follows.
//
// Represents the packaged dimension for an inventory item.
type findImageByIdNodeInventoryItemMeasurement struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeInventoryItemMeasurement.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeInventoryItemMeasurement) GetTypename() *string { return v.Typename }

// findImageByIdNodeInventoryLevel includes the requested fields of the GraphQL type InventoryLevel.
// The GraphQL type's documentation follows.
//
// The quantities of an inventory item that are related to a specific location.
// Learn [more about the relationships between inventory objects](https://shopify.dev/docs/apps/build/orders-fulfillment/inventory-management-apps/manage-quantities-states#inventory-object-relationships).
type findImageByIdNodeInventoryLevel struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeInventoryLevel.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeInventoryLevel) GetTypename() *string { return v.Typename }

// findImageByIdNodeInventoryQuantity includes the requested fields of the GraphQL type InventoryQuantity.
// The GraphQL type's documentation follows.
//
// Represents a quantity of an inventory item at a specific location, for a specific name.
type findImageByIdNodeInventoryQuantity struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeInventoryQuantity.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeInventoryQuantity) GetTypename() *string { return v.Typename }

// findImageByIdNodeInventoryShipment includes the requested fields of the GraphQL type InventoryShipment.
// The GraphQL type's documentation follows.
//
// Represents an inventory shipment.
type findImageByIdNodeInventoryShipment struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeInventoryShipment.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeInventoryShipment) GetTypename() *string { return v.Typename }

// findImageByIdNodeInventoryShipmentLineItem includes the requested fields of the GraphQL type InventoryShipmentLineItem.
// The GraphQL type's documentation follows.
//
// Represents a single line item within an inventory shipment.
type findImageByIdNodeInventoryShipmentLineItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeInventoryShipmentLineItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeInventoryShipmentLineItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeInventoryTransfer includes the requested fields of the GraphQL type InventoryTransfer.
// The GraphQL type's documentation follows.
//
// Represents the intention to move inventory between locations.
type findImageByIdNodeInventoryTransfer struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeInventoryTransfer.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeInventoryTransfer) GetTypename() *string { return v.Typename }

// findImageByIdNodeInventoryTransferLineItem includes the requested fields of the GraphQL type InventoryTransferLineItem.
// The GraphQL type's documentation follows.
//
// Represents a line item belonging to an inventory transfer.
type findImageByIdNodeInventoryTransferLineItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeInventoryTransferLineItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeInventoryTransferLineItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeLineItem includes the requested fields of the GraphQL type LineItem.
// The GraphQL type's documentation follows.
//
// The `LineItem` object represents a single product or service that a customer purchased in an
// [order](https://shopify.dev/docs/api/admin-graphql/latest/objects/Order).
// Each line item is associated with a
// [product variant](https://shopify.dev/docs/api/admin-graphql/latest/objects/ProductVariant)
// and can have multiple [discount allocations](https://shopify.dev/docs/api/admin-graphql/latest/objects/DiscountAllocation).
// Line items contain details about what was purchased, including the product variant, quantity, pricing,
// and fulfillment status.
//
// Use the `LineItem` object to manage the following processes:
//
// - [Track the quantity of items](https://shopify.dev/docs/apps/build/orders-fulfillment/order-management-apps/build-fulfillment-solutions)
// ordered, fulfilled, and unfulfilled.
// - [Calculate prices](https://shopify.dev/docs/apps/build/orders-fulfillment/order-management-apps/edit-orders), including discounts and taxes.
// - Manage fulfillment through [fulfillment services](https://shopify.dev/docs/apps/build/orders-fulfillment/fulfillment-service-apps).
// - Manage [returns](https://shopify.dev/docs/apps/build/orders-fulfillment/returns-apps/build-return-management) and [exchanges](https://shopify.dev/docs/apps/build/orders-fulfillment/returns-apps/manage-exchanges).
// - Handle [subscriptions](https://shopify.dev/docs/apps/build/purchase-options/subscriptions) and recurring orders.
//
// Line items can also include custom attributes and properties, allowing merchants to add specific details
// about each item in an order. Learn more about
// [managing orders and fulfillment](https://shopify.dev/docs/apps/build/orders-fulfillment).
type findImageByIdNodeLineItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeLineItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeLineItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeLineItemGroup includes the requested fields of the GraphQL type LineItemGroup.
// The GraphQL type's documentation follows.
//
// A line item group (bundle) to which a line item belongs to.
type findImageByIdNodeLineItemGroup struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeLineItemGroup.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeLineItemGroup) GetTypename() *string { return v.Typename }

// findImageByIdNodeLocation includes the requested fields of the GraphQL type Location.
// The GraphQL type's documentation follows.
//
// Represents the location where the physical good resides. You can stock inventory at active locations. Active
// locations that have `fulfills_online_orders: true` and are configured with a shipping rate, pickup enabled or
// local delivery will be able to sell from their storefront.
type findImageByIdNodeLocation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeLocation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeLocation) GetTypename() *string { return v.Typename }

// findImageByIdNodeMailingAddress includes the requested fields of the GraphQL type MailingAddress.
// The GraphQL type's documentation follows.
//
// Represents a customer mailing address.
//
// For example, a customer's default address and an order's billing address are both mailling addresses.
type findImageByIdNodeMailingAddress struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeMailingAddress.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMailingAddress) GetTypename() *string { return v.Typename }

// findImageByIdNodeMarket includes the requested fields of the GraphQL type Market.
// The GraphQL type's documentation follows.
//
// A market is a group of one or more regions that you want to target for international sales.
// By creating a market, you can configure a distinct, localized shopping experience for
// customers from a specific area of the world. For example, you can
// [change currency](https://shopify.dev/api/admin-graphql/current/mutations/marketCurrencySettingsUpdate),
// [configure international pricing](https://shopify.dev/apps/internationalization/product-price-lists),
// or [add market-specific domains or subfolders](https://shopify.dev/api/admin-graphql/current/objects/MarketWebPresence).
type findImageByIdNodeMarket struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeMarket.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMarket) GetTypename() *string { return v.Typename }

// findImageByIdNodeMarketCatalog includes the requested fields of the GraphQL type MarketCatalog.
// The GraphQL type's documentation follows.
//
// A list of products with publishing and pricing information associated with markets.
type findImageByIdNodeMarketCatalog struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeMarketCatalog.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMarketCatalog) GetTypename() *string { return v.Typename }

// findImageByIdNodeMarketRegionCountry includes the requested fields of the GraphQL type MarketRegionCountry.
// The GraphQL type's documentation follows.
//
// A country which comprises a market.
type findImageByIdNodeMarketRegionCountry struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeMarketRegionCountry.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMarketRegionCountry) GetTypename() *string { return v.Typename }

// findImageByIdNodeMarketWebPresence includes the requested fields of the GraphQL type MarketWebPresence.
// The GraphQL type's documentation follows.
//
// The market’s web presence, which defines its SEO strategy. This can be a different domain
// (e.g. `example.ca`), subdomain (e.g. `ca.example.com`), or subfolders of the primary
// domain (e.g. `example.com/en-ca`). Each web presence comprises one or more language
// variants. If a market does not have its own web presence, it is accessible on the shop’s
// primary domain via [country
// selectors](https://shopify.dev/themes/internationalization/multiple-currencies-languages#the-country-selector).
//
// Note: while the domain/subfolders defined by a market’s web presence are not applicable to
// custom storefronts, which must manage their own domains and routing, the languages chosen
// here do govern [the languages available on the Storefront
// API](https://shopify.dev/custom-storefronts/internationalization/multiple-languages) for the countries in
// this market.
type findImageByIdNodeMarketWebPresence struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeMarketWebPresence.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMarketWebPresence) GetTypename() *string { return v.Typename }

// findImageByIdNodeMarketingActivity includes the requested fields of the GraphQL type MarketingActivity.
// The GraphQL type's documentation follows.
//
// The marketing activity resource represents marketing that a
// merchant created through an app.
type findImageByIdNodeMarketingActivity struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeMarketingActivity.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMarketingActivity) GetTypename() *string { return v.Typename }

// findImageByIdNodeMarketingEvent includes the requested fields of the GraphQL type MarketingEvent.
// The GraphQL type's documentation follows.
//
// Represents actions that market a merchant's store or products.
type findImageByIdNodeMarketingEvent struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeMarketingEvent.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMarketingEvent) GetTypename() *string { return v.Typename }

// findImageByIdNodeMediaImage includes the requested fields of the GraphQL type MediaImage.
// The GraphQL type's documentation follows.
//
// The `MediaImage` object represents an image hosted on Shopify's
// [content delivery network (CDN)](https://shopify.dev/docs/storefronts/themes/best-practices/performance/platform#shopify-cdn).
// Shopify CDN is a content system that serves as the primary way to store,
// manage, and deliver visual content for products, variants, and other resources across the Shopify platform.
//
// The `MediaImage` object provides information to:
//
// - Store and display product and variant images across online stores, admin interfaces, and mobile apps.
// - Retrieve visual branding elements, including logos, banners, favicons, and background images in checkout flows.
// - Retrieve signed URLs for secure, time-limited access to original image files.
//
// Each `MediaImage` object provides both the processed image data (with automatic optimization and CDN delivery)
// and access to the original source file. The image processing is handled asynchronously, so images
// might not be immediately available after upload. The
// [`status`](https://shopify.dev/docs/api/admin-graphql/latest/objects/mediaimage#field-MediaImage.fields.status)
// field indicates when processing is complete and the image is ready for use.
//
// The `MediaImage` object implements the [`Media`](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/Media)
// interface alongside other media types, like videos and 3D models.
//
// Learn about
// managing media for [products](https://shopify.dev/docs/apps/build/online-store/product-media),
// [product variants](https://shopify.dev/docs/apps/build/online-store/product-variant-media), and
// [asynchronous media management](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components#asynchronous-media-management).
type findImageByIdNodeMediaImage struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// Current status of the media.
	Status MediaStatus `json:"status"`
	// The MIME type of the image.
	MimeType *string `json:"mimeType"`
	// The image for the media. Returns `null` until `status` is `READY`.
	Image *findImageByIdNodeMediaImageImage `json:"image"`
	// The original source of the image.
	OriginalSource *findImageByIdNodeMediaImageOriginalSource `json:"originalSource"`
}

// GetTypename returns findImageByIdNodeMediaImage.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMediaImage) GetTypename() *string { return v.Typename }

// GetId returns findImageByIdNodeMediaImage.Id, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMediaImage) GetId() string { return v.Id }

// GetStatus returns findImageByIdNodeMediaImage.Status, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMediaImage) GetStatus() MediaStatus { return v.Status }

// GetMimeType returns findImageByIdNodeMediaImage.MimeType, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMediaImage) GetMimeType() *string { return v.MimeType }

// GetImage returns findImageByIdNodeMediaImage.Image, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMediaImage) GetImage() *findImageByIdNodeMediaImageImage { return v.Image }

// GetOriginalSource returns findImageByIdNodeMediaImage.OriginalSource, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMediaImage) GetOriginalSource() *findImageByIdNodeMediaImageOriginalSource {
	return v.OriginalSource
}

// findImageByIdNodeMediaImageImage includes the requested fields of the GraphQL type Image.
// The GraphQL type's documentation follows.
//
// Represents an image resource.
type findImageByIdNodeMediaImageImage struct {
	// The original width of the image in pixels. Returns `null` if the image isn't hosted by Shopify.
	Width *int `json:"width"`
	// The original height of the image in pixels. Returns `null` if the image isn't hosted by Shopify.
	Height *int `json:"height"`
	// The location of the image as a URL.
	//
	// If no transform options are specified, then the original image will be preserved including any pre-applied transforms.
	//
	// All transformation options are considered "best-effort". Any transformation
	// that the original image type doesn't support will be ignored.
	//
	// If you need multiple variations of the same image, then you can use [GraphQL
	// aliases](https://graphql.org/learn/queries/#aliases).
	Url string `json:"url"`
	// The ThumbHash of the image.
	//
	// Useful to display placeholder images while the original image is loading.
	Thumbhash *string `json:"thumbhash"`
}

// GetWidth returns findImageByIdNodeMediaImageImage.Width, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMediaImageImage) GetWidth() *int { return v.Width }

// GetHeight returns findImageByIdNodeMediaImageImage.Height, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMediaImageImage) GetHeight() *int { return v.Height }

// GetUrl returns findImageByIdNodeMediaImageImage.Url, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMediaImageImage) GetUrl() string { return v.Url }

// GetThumbhash returns findImageByIdNodeMediaImageImage.Thumbhash, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMediaImageImage) GetThumbhash() *string { return v.Thumbhash }

// findImageByIdNodeMediaImageOriginalSource includes the requested fields of the GraphQL type MediaImageOriginalSource.
// The GraphQL type's documentation follows.
//
// The original source for an image.
type findImageByIdNodeMediaImageOriginalSource struct {
	// The URL of the original image, valid only for a short period.
	Url *string `json:"url"`
	// The size of the original file in bytes.
	FileSize *int `json:"fileSize"`
}

// GetUrl returns findImageByIdNodeMediaImageOriginalSource.Url, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMediaImageOriginalSource) GetUrl() *string { return v.Url }

// GetFileSize returns findImageByIdNodeMediaImageOriginalSource.FileSize, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMediaImageOriginalSource) GetFileSize() *int { return v.FileSize }

// findImageByIdNodeMenu includes the requested fields of the GraphQL type Menu.
// The GraphQL type's documentation follows.
//
// A menu for display on the storefront.
type findImageByIdNodeMenu struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeMenu.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMenu) GetTypename() *string { return v.Typename }

// findImageByIdNodeMetafield includes the requested fields of the GraphQL type Metafield.
// The GraphQL type's documentation follows.
//
// Metafields enable you to attach additional information to a Shopify resource, such
// as a [Product](https://shopify.dev/api/admin-graphql/latest/objects/product) or
// a [Collection](https://shopify.dev/api/admin-graphql/latest/objects/collection).
// For more information about where you can attach metafields refer to [HasMetafields](https://shopify.dev/api/admin/graphql/reference/common-objects/HasMetafields).
// Some examples of the data that metafields enable you to store are
// specifications, size charts, downloadable documents, release dates, images, or part numbers.
// Metafields are identified by an owner resource, namespace, and key. and store a
// value along with type information for that value.
type findImageByIdNodeMetafield struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeMetafield.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMetafield) GetTypename() *string { return v.Typename }

// findImageByIdNodeMetafieldDefinition includes the requested fields of the GraphQL type MetafieldDefinition.
// The GraphQL type's documentation follows.
//
// Metafield definitions enable you to define additional validation constraints for metafields, and enable the
// merchant to edit metafield values in context.
type findImageByIdNodeMetafieldDefinition struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeMetafieldDefinition.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMetafieldDefinition) GetTypename() *string { return v.Typename }

// findImageByIdNodeMetaobject includes the requested fields of the GraphQL type Metaobject.
// The GraphQL type's documentation follows.
//
// Provides an object instance represented by a MetaobjectDefinition.
type findImageByIdNodeMetaobject struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeMetaobject.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMetaobject) GetTypename() *string { return v.Typename }

// findImageByIdNodeMetaobjectDefinition includes the requested fields of the GraphQL type MetaobjectDefinition.
// The GraphQL type's documentation follows.
//
// Provides the definition of a generic object structure composed of metafields.
type findImageByIdNodeMetaobjectDefinition struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeMetaobjectDefinition.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeMetaobjectDefinition) GetTypename() *string { return v.Typename }

// findImageByIdNodeModel3d includes the requested fields of the GraphQL type Model3d.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted 3D model.
type findImageByIdNodeModel3d struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeModel3d.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeModel3d) GetTypename() *string { return v.Typename }

// findImageByIdNodeOnlineStoreTheme includes the requested fields of the GraphQL type OnlineStoreTheme.
// The GraphQL type's documentation follows.
//
// A theme for display on the storefront.
type findImageByIdNodeOnlineStoreTheme struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeOnlineStoreTheme.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeOnlineStoreTheme) GetTypename() *string { return v.Typename }

// findImageByIdNodeOrder includes the requested fields of the GraphQL type Order.
// The GraphQL type's documentation follows.
//
// The `Order` object represents a customer's request to purchase one or more
// products from a store. Use the `Order` object to handle the complete purchase
// lifecycle from checkout to fulfillment.
//
// Use the `Order` object when you need to:
//
// - Display order details on customer account pages or admin dashboards.
// - Create orders for phone sales, wholesale customers, or subscription services.
// - Update order information like shipping addresses, notes, or fulfillment status.
// - Process returns, exchanges, and partial refunds.
// - Generate invoices, receipts, and shipping labels.
//
// The `Order` object serves as the central hub connecting customer information,
// product details, payment processing, and fulfillment data within the GraphQL
// Admin API schema.
//
// > Note:
// > Only the last 60 days' worth of orders from a store are accessible from the
// `Order` object by default. If you want to access older records,
// > then you need to [request access to all
// orders](https://shopify.dev/docs/api/usage/access-scopes#orders-permissions). If
// your app is granted
// > access, then you can add the `read_all_orders`, `read_orders`, and `write_orders` scopes.
//
// > Caution:
// > Only use orders data if it's required for your app's functionality. Shopify
// will restrict [access to scopes](https://shopify.dev/docs/api/usage/access-scopes#requesting-specific-permissions)
// for apps that don't have a legitimate use for the associated data.
//
// Learn more about [building apps for orders and fulfillment](https://shopify.dev/docs/apps/build/orders-fulfillment).
type findImageByIdNodeOrder struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeOrder.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeOrder) GetTypename() *string { return v.Typename }

// findImageByIdNodeOrderAdjustment includes the requested fields of the GraphQL type OrderAdjustment.
// The GraphQL type's documentation follows.
//
// An order adjustment accounts for the difference between a calculated and actual refund amount.
type findImageByIdNodeOrderAdjustment struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeOrderAdjustment.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeOrderAdjustment) GetTypename() *string { return v.Typename }

// findImageByIdNodeOrderDisputeSummary includes the requested fields of the GraphQL type OrderDisputeSummary.
// The GraphQL type's documentation follows.
//
// A summary of the important details for a dispute on an order.
type findImageByIdNodeOrderDisputeSummary struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeOrderDisputeSummary.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeOrderDisputeSummary) GetTypename() *string { return v.Typename }

// findImageByIdNodeOrderEditSession includes the requested fields of the GraphQL type OrderEditSession.
// The GraphQL type's documentation follows.
//
// An edit session for an order.
type findImageByIdNodeOrderEditSession struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeOrderEditSession.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeOrderEditSession) GetTypename() *string { return v.Typename }

// findImageByIdNodeOrderTransaction includes the requested fields of the GraphQL type OrderTransaction.
// The GraphQL type's documentation follows.
//
// The `OrderTransaction` object represents a payment transaction that's associated with an order. An order
// transaction is a specific action or event that happens within the context of an order, such as a customer paying
// for a purchase or receiving a refund, or other payment-related activity.
//
// Use the `OrderTransaction` object to capture the complete lifecycle of a payment, from initial
// authorization to final settlement, including refunds and currency exchanges. Common use cases for using the
// `OrderTransaction` object include:
//
// - Processing new payments for orders
// - Managing payment authorizations and captures
// - Processing refunds for returned items
// - Tracking payment status and errors
// - Managing multi-currency transactions
// - Handling payment gateway integrations
//
// Each `OrderTransaction` object has a [`kind`](https://shopify.dev/docs/api/admin-graphql/latest/enums/OrderTransactionKind)
// that defines the type of transaction and a [`status`](https://shopify.dev/docs/api/admin-graphql/latest/enums/OrderTransactionStatus)
// that indicates the current state of the transaction. The object stores detailed information about payment
// methods, gateway processing, and settlement details.
//
// Learn more about [payment processing](https://help.shopify.com/manual/payments)
// and [payment gateway integrations](https://www.shopify.com/ca/payment-gateways).
type findImageByIdNodeOrderTransaction struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeOrderTransaction.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeOrderTransaction) GetTypename() *string { return v.Typename }

// findImageByIdNodePage includes the requested fields of the GraphQL type Page.
// The GraphQL type's documentation follows.
//
// A page on the Online Store.
type findImageByIdNodePage struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodePage.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodePage) GetTypename() *string { return v.Typename }

// findImageByIdNodePaymentCustomization includes the requested fields of the GraphQL type PaymentCustomization.
// The GraphQL type's documentation follows.
//
// A payment customization.
type findImageByIdNodePaymentCustomization struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodePaymentCustomization.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodePaymentCustomization) GetTypename() *string { return v.Typename }

// findImageByIdNodePaymentMandate includes the requested fields of the GraphQL type PaymentMandate.
// The GraphQL type's documentation follows.
//
// A payment instrument and the permission
// the owner of the instrument gives to the merchant to debit it.
type findImageByIdNodePaymentMandate struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodePaymentMandate.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodePaymentMandate) GetTypename() *string { return v.Typename }

// findImageByIdNodePaymentSchedule includes the requested fields of the GraphQL type PaymentSchedule.
// The GraphQL type's documentation follows.
//
// Represents the payment schedule for a single payment defined in the payment terms.
type findImageByIdNodePaymentSchedule struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodePaymentSchedule.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodePaymentSchedule) GetTypename() *string { return v.Typename }

// findImageByIdNodePaymentTerms includes the requested fields of the GraphQL type PaymentTerms.
// The GraphQL type's documentation follows.
//
// Represents the payment terms for an order or draft order.
type findImageByIdNodePaymentTerms struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodePaymentTerms.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodePaymentTerms) GetTypename() *string { return v.Typename }

// findImageByIdNodePaymentTermsTemplate includes the requested fields of the GraphQL type PaymentTermsTemplate.
// The GraphQL type's documentation follows.
//
// Represents the payment terms template object.
type findImageByIdNodePaymentTermsTemplate struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodePaymentTermsTemplate.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodePaymentTermsTemplate) GetTypename() *string { return v.Typename }

// findImageByIdNodePointOfSaleDevice includes the requested fields of the GraphQL type PointOfSaleDevice.
// The GraphQL type's documentation follows.
//
// Represents a mobile device that Shopify Point of Sale has been installed on.
type findImageByIdNodePointOfSaleDevice struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodePointOfSaleDevice.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodePointOfSaleDevice) GetTypename() *string { return v.Typename }

// findImageByIdNodePriceList includes the requested fields of the GraphQL type PriceList.
// The GraphQL type's documentation follows.
//
// Represents a price list, including information about related prices and eligibility rules.
// You can use price lists to specify either fixed prices or adjusted relative prices that
// override initial product variant prices. Price lists are applied to customers
// using context rules, which determine price list eligibility.
//
// For more information on price lists, refer to
// [Support different pricing models](https://shopify.dev/apps/internationalization/product-price-lists).
type findImageByIdNodePriceList struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodePriceList.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodePriceList) GetTypename() *string { return v.Typename }

// findImageByIdNodePriceRule includes the requested fields of the GraphQL type PriceRule.
// The GraphQL type's documentation follows.
//
// Price rules are a set of conditions, including entitlements and prerequisites,
// that must be met in order for a discount code to apply.
//
// We recommend using the types and queries detailed at [Getting started with discounts](https://shopify.dev/docs/apps/selling-strategies/discounts/getting-started)
// instead. These will replace the GraphQL `PriceRule` object and REST Admin
// `PriceRule` and `DiscountCode` resources.
type findImageByIdNodePriceRule struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodePriceRule.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodePriceRule) GetTypename() *string { return v.Typename }

// findImageByIdNodePriceRuleDiscountCode includes the requested fields of the GraphQL type PriceRuleDiscountCode.
// The GraphQL type's documentation follows.
//
// A discount code of a price rule.
type findImageByIdNodePriceRuleDiscountCode struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodePriceRuleDiscountCode.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodePriceRuleDiscountCode) GetTypename() *string { return v.Typename }

// findImageByIdNodeProduct includes the requested fields of the GraphQL type Product.
// The GraphQL type's documentation follows.
//
// The `Product` object lets you manage products in a merchant’s store.
//
// Products are the goods and services that merchants offer to customers. They can
// include various details such as title, description, price, images, and options
// such as size or color.
// You can use [product variants](https://shopify.dev/docs/api/admin-graphql/latest/objects/productvariant)
// to create or update different versions of the same product.
// You can also add or update product [media](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/media).
// Products can be organized by grouping them into a [collection](https://shopify.dev/docs/api/admin-graphql/latest/objects/collection).
//
// Learn more about working with [Shopify's product model](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components),
// including limitations and considerations.
type findImageByIdNodeProduct struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeProduct.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeProduct) GetTypename() *string { return v.Typename }

// findImageByIdNodeProductBundleOperation includes the requested fields of the GraphQL type ProductBundleOperation.
// The GraphQL type's documentation follows.
//
// An entity that represents details of an asynchronous
// [ProductBundleCreate](https://shopify.dev/api/admin-graphql/current/mutations/productBundleCreate) or
// [ProductBundleUpdate](https://shopify.dev/api/admin-graphql/current/mutations/productBundleUpdate) mutation.
//
// By querying this entity with the
// [productOperation](https://shopify.dev/api/admin-graphql/current/queries/productOperation) query
// using the ID that was returned when the bundle was created or updated, this can be used to check the status of an operation.
//
// The `status` field indicates whether the operation is `CREATED`, `ACTIVE`, or `COMPLETE`.
//
// The `product` field provides the details of the created or updated product.
//
// The `userErrors` field provides mutation errors that occurred during the operation.
type findImageByIdNodeProductBundleOperation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeProductBundleOperation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeProductBundleOperation) GetTypename() *string { return v.Typename }

// findImageByIdNodeProductDeleteOperation includes the requested fields of the GraphQL type ProductDeleteOperation.
// The GraphQL type's documentation follows.
//
// An entity that represents details of an asynchronous
// [ProductDelete](https://shopify.dev/api/admin-graphql/current/mutations/productDelete) mutation.
//
// By querying this entity with the
// [productOperation](https://shopify.dev/api/admin-graphql/current/queries/productOperation) query
// using the ID that was returned when the product was deleted, this can be used to check the status of an operation.
//
// The `status` field indicates whether the operation is `CREATED`, `ACTIVE`, or `COMPLETE`.
//
// The `deletedProductId` field provides the ID of the deleted product.
//
// The `userErrors` field provides mutation errors that occurred during the operation.
type findImageByIdNodeProductDeleteOperation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeProductDeleteOperation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeProductDeleteOperation) GetTypename() *string { return v.Typename }

// findImageByIdNodeProductDuplicateOperation includes the requested fields of the GraphQL type ProductDuplicateOperation.
// The GraphQL type's documentation follows.
//
// An entity that represents details of an asynchronous
// [ProductDuplicate](https://shopify.dev/api/admin-graphql/current/mutations/productDuplicate) mutation.
//
// By querying this entity with the
// [productOperation](https://shopify.dev/api/admin-graphql/current/queries/productOperation) query
// using the ID that was returned
// [when the product was duplicated](https://shopify.dev/api/admin/migrate/new-product-model/sync-data#create-a-product-with-variants-and-options-asynchronously),
// this can be used to check the status of an operation.
//
// The `status` field indicates whether the operation is `CREATED`, `ACTIVE`, or `COMPLETE`.
//
// The `product` field provides the details of the original product.
//
// The `newProduct` field provides the details of the new duplicate of the product.
//
// The `userErrors` field provides mutation errors that occurred during the operation.
type findImageByIdNodeProductDuplicateOperation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeProductDuplicateOperation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeProductDuplicateOperation) GetTypename() *string { return v.Typename }

// findImageByIdNodeProductFeed includes the requested fields of the GraphQL type ProductFeed.
// The GraphQL type's documentation follows.
//
// A product feed.
type findImageByIdNodeProductFeed struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeProductFeed.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeProductFeed) GetTypename() *string { return v.Typename }

// findImageByIdNodeProductOption includes the requested fields of the GraphQL type ProductOption.
// The GraphQL type's documentation follows.
//
// The product property names. For example, "Size", "Color", and "Material".
// Variants are selected based on permutations of these options.
// The limit for each product property name is 255 characters.
type findImageByIdNodeProductOption struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeProductOption.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeProductOption) GetTypename() *string { return v.Typename }

// findImageByIdNodeProductOptionValue includes the requested fields of the GraphQL type ProductOptionValue.
// The GraphQL type's documentation follows.
//
// The product option value names. For example, "Red", "Blue", and "Green" for a "Color" option.
type findImageByIdNodeProductOptionValue struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeProductOptionValue.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeProductOptionValue) GetTypename() *string { return v.Typename }

// findImageByIdNodeProductSetOperation includes the requested fields of the GraphQL type ProductSetOperation.
// The GraphQL type's documentation follows.
//
// An entity that represents details of an asynchronous
// [ProductSet](https://shopify.dev/api/admin-graphql/current/mutations/productSet) mutation.
//
// By querying this entity with the
// [productOperation](https://shopify.dev/api/admin-graphql/current/queries/productOperation) query
// using the ID that was returned
// [when the product was created or updated](https://shopify.dev/api/admin/migrate/new-product-model/sync-data#create-a-product-with-variants-and-options-asynchronously),
// this can be used to check the status of an operation.
//
// The `status` field indicates whether the operation is `CREATED`, `ACTIVE`, or `COMPLETE`.
//
// The `product` field provides the details of the created or updated product.
//
// The `userErrors` field provides mutation errors that occurred during the operation.
type findImageByIdNodeProductSetOperation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeProductSetOperation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeProductSetOperation) GetTypename() *string { return v.Typename }

// findImageByIdNodeProductTaxonomyNode includes the requested fields of the GraphQL type ProductTaxonomyNode.
// The GraphQL type's documentation follows.
//
// Represents a [Shopify product taxonomy](https://shopify.github.io/product-taxonomy/releases/unstable/?categoryId=sg-4-17-2-17) node.
type findImageByIdNodeProductTaxonomyNode struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeProductTaxonomyNode.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeProductTaxonomyNode) GetTypename() *string { return v.Typename }

// findImageByIdNodeProductVariant includes the requested fields of the GraphQL type ProductVariant.
// The GraphQL type's documentation follows.
//
// The `ProductVariant` object represents a version of a
// [product](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product)
// that comes in more than one [option](https://shopify.dev/docs/api/admin-graphql/latest/objects/ProductOption),
// such as size or color. For example, if a merchant sells t-shirts with options for size and color, then a small,
// blue t-shirt would be one product variant and a large, blue t-shirt would be another.
//
// Use the `ProductVariant` object to manage the full lifecycle and configuration of a product's variants. Common
// use cases for using the `ProductVariant` object include:
//
// - Tracking inventory for each variant
// - Setting unique prices for each variant
// - Assigning barcodes and SKUs to connect variants to fulfillment services
// - Attaching variant-specific images and media
// - Setting delivery and tax requirements
// - Supporting product bundles, subscriptions, and selling plans
//
// A `ProductVariant` is associated with a parent
// [`Product`](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product) object.
// `ProductVariant` serves as the central link between a product's merchandising configuration, inventory,
// pricing, fulfillment, and sales channels within the GraphQL Admin API schema. Each variant
// can reference other GraphQL types such as:
//
// - [`InventoryItem`](https://shopify.dev/docs/api/admin-graphql/latest/objects/InventoryItem): Used for inventory tracking
// - [`Image`](https://shopify.dev/docs/api/admin-graphql/latest/objects/Image): Used for variant-specific images
// - [`SellingPlanGroup`](https://shopify.dev/docs/api/admin-graphql/latest/objects/SellingPlanGroup): Used for subscriptions and selling plans
//
// Learn more about [Shopify's product model](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components).
type findImageByIdNodeProductVariant struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeProductVariant.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeProductVariant) GetTypename() *string { return v.Typename }

// findImageByIdNodeProductVariantComponent includes the requested fields of the GraphQL type ProductVariantComponent.
// The GraphQL type's documentation follows.
//
// A product variant component that is included within a bundle.
//
// These are the individual product variants that make up a bundle product,
// where each component has a specific required quantity.
type findImageByIdNodeProductVariantComponent struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeProductVariantComponent.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeProductVariantComponent) GetTypename() *string { return v.Typename }

// findImageByIdNodePublication includes the requested fields of the GraphQL type Publication.
// The GraphQL type's documentation follows.
//
// A publication is a group of products and collections that is published to an app.
type findImageByIdNodePublication struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodePublication.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodePublication) GetTypename() *string { return v.Typename }

// findImageByIdNodePublicationResourceOperation includes the requested fields of the GraphQL type PublicationResourceOperation.
// The GraphQL type's documentation follows.
//
// A bulk update operation on a publication.
type findImageByIdNodePublicationResourceOperation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodePublicationResourceOperation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodePublicationResourceOperation) GetTypename() *string { return v.Typename }

// findImageByIdNodeQuantityPriceBreak includes the requested fields of the GraphQL type QuantityPriceBreak.
// The GraphQL type's documentation follows.
//
// Quantity price breaks lets you offer different rates that are based on the
// amount of a specific variant being ordered.
type findImageByIdNodeQuantityPriceBreak struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeQuantityPriceBreak.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeQuantityPriceBreak) GetTypename() *string { return v.Typename }

// findImageByIdNodeRefund includes the requested fields of the GraphQL type Refund.
// The GraphQL type's documentation follows.
//
// The `Refund` object represents a financial record of money returned to a customer from an order.
// It provides a comprehensive view of all refunded amounts, transactions, and restocking instructions
// associated with returning products or correcting order issues.
//
// The `Refund` object provides information to:
//
// - Process customer returns and issue payments back to customers
// - Handle partial or full refunds for line items with optional inventory restocking
// - Refund shipping costs, duties, and additional fees
// - Issue store credit refunds as an alternative to original payment method returns
// - Track and reconcile all financial transactions related to refunds
//
// Each `Refund` object maintains detailed records of what was refunded, how much was refunded,
// which payment transactions were involved, and any inventory restocking that occurred. The refund
// can include multiple components such as product line items, shipping charges, taxes, duties, and
// additional fees, all calculated with proper currency handling for international orders.
//
// Refunds are always associated with an [order](https://shopify.dev/docs/api/admin-graphql/latest/objects/Order)
// and can optionally be linked to a [return](https://shopify.dev/docs/api/admin-graphql/latest/objects/Return)
// if the refund was initiated through the returns process. The refund tracks both the presentment currency
// (what the customer sees) and the shop currency for accurate financial reporting.
//
// > Note:
// > The existence of a `Refund` object doesn't guarantee that the money has been returned to the customer.
// > The actual financial processing happens through associated
// > [`OrderTransaction`](https://shopify.dev/docs/api/admin-graphql/latest/objects/OrderTransaction)
// > objects, which can be in various states, such as pending, processing, success, or failure.
// > To determine if money has actually been refunded, check the
// > [status](https://shopify.dev/docs/api/admin-graphql/latest/objects/OrderTransaction#field-OrderTransaction.fields.status)
// > of the associated transactions.
//
// Learn more about
// [managing returns](https://shopify.dev/docs/apps/build/orders-fulfillment/returns-apps/build-return-management),
// [refunding duties](https://shopify.dev/docs/apps/build/orders-fulfillment/returns-apps/view-and-refund-duties), and
// [processing refunds](https://shopify.dev/docs/api/admin-graphql/latest/mutations/refundCreate).
type findImageByIdNodeRefund struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeRefund.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeRefund) GetTypename() *string { return v.Typename }

// findImageByIdNodeRefundShippingLine includes the requested fields of the GraphQL type RefundShippingLine.
// The GraphQL type's documentation follows.
//
// A shipping line item that's included in a refund.
type findImageByIdNodeRefundShippingLine struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeRefundShippingLine.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeRefundShippingLine) GetTypename() *string { return v.Typename }

// findImageByIdNodeReturn includes the requested fields of the GraphQL type Return.
// The GraphQL type's documentation follows.
//
// The `Return` object represents the intent of a buyer to ship one or more items from an order back to a merchant
// or a third-party fulfillment location. A return is associated with an
// [order](https://shopify.dev/docs/api/admin-graphql/latest/objects/Order)
// and can include multiple return [line items](https://shopify.dev/docs/api/admin-graphql/latest/objects/LineItem).
// Each return has a [status](https://shopify.dev/docs/apps/build/orders-fulfillment/returns-apps#return-statuses),
// which indicates the state of the return.
//
// Use the `Return` object to capture the financial, logistical,
// and business intent of a return. For example, you can identify eligible items for a return and issue customers
// a refund for returned items on behalf of the merchant.
//
// Learn more about providing a
// [return management workflow](https://shopify.dev/docs/apps/build/orders-fulfillment/returns-apps/build-return-management)
// for merchants. You can also manage [exchanges](https://shopify.dev/docs/apps/build/orders-fulfillment/returns-apps/manage-exchanges),
// [reverse fulfillment orders](https://shopify.dev/docs/apps/build/orders-fulfillment/returns-apps/manage-reverse-fulfillment-orders),
// and [reverse deliveries](https://shopify.dev/docs/apps/build/orders-fulfillment/returns-apps/manage-reverse-deliveries)
// on behalf of merchants.
type findImageByIdNodeReturn struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeReturn.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeReturn) GetTypename() *string { return v.Typename }

// findImageByIdNodeReturnLineItem includes the requested fields of the GraphQL type ReturnLineItem.
// The GraphQL type's documentation follows.
//
// A return line item.
type findImageByIdNodeReturnLineItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeReturnLineItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeReturnLineItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeReturnableFulfillment includes the requested fields of the GraphQL type ReturnableFulfillment.
// The GraphQL type's documentation follows.
//
// A returnable fulfillment, which is an order that has been delivered
// and is eligible to be returned to the merchant.
type findImageByIdNodeReturnableFulfillment struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeReturnableFulfillment.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeReturnableFulfillment) GetTypename() *string { return v.Typename }

// findImageByIdNodeReverseDelivery includes the requested fields of the GraphQL type ReverseDelivery.
// The GraphQL type's documentation follows.
//
// A reverse delivery is a post-fulfillment object that represents a buyer sending a package to a merchant.
// For example, a buyer requests a return, and a merchant sends the buyer a shipping label.
// The reverse delivery contains the context of the items sent back, how they're being sent back
// (for example, a shipping label), and the current state of the delivery (tracking information).
type findImageByIdNodeReverseDelivery struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeReverseDelivery.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeReverseDelivery) GetTypename() *string { return v.Typename }

// findImageByIdNodeReverseDeliveryLineItem includes the requested fields of the GraphQL type ReverseDeliveryLineItem.
// The GraphQL type's documentation follows.
//
// The details about a reverse delivery line item.
type findImageByIdNodeReverseDeliveryLineItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeReverseDeliveryLineItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeReverseDeliveryLineItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeReverseFulfillmentOrder includes the requested fields of the GraphQL type ReverseFulfillmentOrder.
// The GraphQL type's documentation follows.
//
// A group of one or more items in a return that will be processed at a fulfillment service.
// There can be more than one reverse fulfillment order for a return at a given location.
type findImageByIdNodeReverseFulfillmentOrder struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeReverseFulfillmentOrder.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeReverseFulfillmentOrder) GetTypename() *string { return v.Typename }

// findImageByIdNodeReverseFulfillmentOrderDisposition includes the requested fields of the GraphQL type ReverseFulfillmentOrderDisposition.
// The GraphQL type's documentation follows.
//
// The details of the arrangement of an item.
type findImageByIdNodeReverseFulfillmentOrderDisposition struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeReverseFulfillmentOrderDisposition.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeReverseFulfillmentOrderDisposition) GetTypename() *string {
	return v.Typename
}

// findImageByIdNodeReverseFulfillmentOrderLineItem includes the requested fields of the GraphQL type ReverseFulfillmentOrderLineItem.
// The GraphQL type's documentation follows.
//
// The details about a reverse fulfillment order line item.
type findImageByIdNodeReverseFulfillmentOrderLineItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeReverseFulfillmentOrderLineItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeReverseFulfillmentOrderLineItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeSaleAdditionalFee includes the requested fields of the GraphQL type SaleAdditionalFee.
// The GraphQL type's documentation follows.
//
// The additional fee details for a line item.
type findImageByIdNodeSaleAdditionalFee struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeSaleAdditionalFee.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeSaleAdditionalFee) GetTypename() *string { return v.Typename }

// findImageByIdNodeSavedSearch includes the requested fields of the GraphQL type SavedSearch.
// The GraphQL type's documentation follows.
//
// A saved search is a representation of a search query saved in the admin.
type findImageByIdNodeSavedSearch struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeSavedSearch.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeSavedSearch) GetTypename() *string { return v.Typename }

// findImageByIdNodeScriptTag includes the requested fields of the GraphQL type ScriptTag.
// The GraphQL type's documentation follows.
//
// <div class="note"><h4>Theme app extensions</h4>
// <p>If your app integrates with a Shopify theme and you plan to submit it to the
// Shopify App Store, you must use theme app extensions instead of Script tags.
// Script tags can only be used with vintage themes. <a
// href="/apps/online-store#what-integration-method-should-i-use"
// target="_blank">Learn more</a>.</p></div>
//
// <div class="note"><h4>Script tag deprecation</h4>
// <p>Script tags will be sunset for the <b>Order status</b> page on August 28, 2025. <a
// href="https://www.shopify.com/plus/upgrading-to-checkout-extensibility">Upgrade
// to Checkout Extensibility</a> before this date. <a
// href="/docs/api/liquid/objects#script">Shopify Scripts</a> will continue to work
// alongside Checkout Extensibility until August 28, 2025.</p></div>
//
// A script tag represents remote JavaScript code that is loaded into the pages of
// a shop's storefront or the **Order status** page of checkout.
type findImageByIdNodeScriptTag struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeScriptTag.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeScriptTag) GetTypename() *string { return v.Typename }

// findImageByIdNodeSegment includes the requested fields of the GraphQL type Segment.
// The GraphQL type's documentation follows.
//
// A dynamic collection of customers based on specific criteria.
type findImageByIdNodeSegment struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeSegment.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeSegment) GetTypename() *string { return v.Typename }

// findImageByIdNodeSellingPlan includes the requested fields of the GraphQL type SellingPlan.
// The GraphQL type's documentation follows.
//
// Represents how a product can be sold and purchased. Selling plans and associated records (selling plan groups
// and policies) are deleted 48 hours after a merchant uninstalls their subscriptions app. We recommend backing
// up these records if you need to restore them later.
//
// For more information on selling plans, refer to
// [*Creating and managing selling plans*](https://shopify.dev/docs/apps/selling-strategies/subscriptions/selling-plans).
type findImageByIdNodeSellingPlan struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeSellingPlan.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeSellingPlan) GetTypename() *string { return v.Typename }

// findImageByIdNodeSellingPlanGroup includes the requested fields of the GraphQL type SellingPlanGroup.
// The GraphQL type's documentation follows.
//
// Represents a selling method (for example, "Subscribe and save" or "Pre-paid"). Selling plan groups
// and associated records (selling plans and policies) are deleted 48 hours after a merchant
// uninstalls their subscriptions app. We recommend backing up these records if you need to restore them later.
type findImageByIdNodeSellingPlanGroup struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeSellingPlanGroup.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeSellingPlanGroup) GetTypename() *string { return v.Typename }

// findImageByIdNodeServerPixel includes the requested fields of the GraphQL type ServerPixel.
// The GraphQL type's documentation follows.
//
// A server pixel stores configuration for streaming customer interactions to an EventBridge or PubSub endpoint.
type findImageByIdNodeServerPixel struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeServerPixel.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeServerPixel) GetTypename() *string { return v.Typename }

// findImageByIdNodeShop includes the requested fields of the GraphQL type Shop.
// The GraphQL type's documentation follows.
//
// Represents a collection of general settings and information about the shop.
type findImageByIdNodeShop struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeShop.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeShop) GetTypename() *string { return v.Typename }

// findImageByIdNodeShopAddress includes the requested fields of the GraphQL type ShopAddress.
// The GraphQL type's documentation follows.
//
// An address for a shop.
type findImageByIdNodeShopAddress struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeShopAddress.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeShopAddress) GetTypename() *string { return v.Typename }

// findImageByIdNodeShopPolicy includes the requested fields of the GraphQL type ShopPolicy.
// The GraphQL type's documentation follows.
//
// Policy that a merchant has configured for their store, such as their refund or privacy policy.
type findImageByIdNodeShopPolicy struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeShopPolicy.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeShopPolicy) GetTypename() *string { return v.Typename }

// findImageByIdNodeShopifyPaymentsAccount includes the requested fields of the GraphQL type ShopifyPaymentsAccount.
// The GraphQL type's documentation follows.
//
// Balance and payout information for a
// [Shopify Payments](https://help.shopify.com/manual/payments/shopify-payments/getting-paid-with-shopify-payments)
// account. Balance includes all balances for the currencies supported by the shop.
// You can also query for a list of payouts, where each payout includes the corresponding currencyCode field.
type findImageByIdNodeShopifyPaymentsAccount struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeShopifyPaymentsAccount.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeShopifyPaymentsAccount) GetTypename() *string { return v.Typename }

// findImageByIdNodeShopifyPaymentsBalanceTransaction includes the requested fields of the GraphQL type ShopifyPaymentsBalanceTransaction.
// The GraphQL type's documentation follows.
//
// A transaction that contributes to a Shopify Payments account balance.
type findImageByIdNodeShopifyPaymentsBalanceTransaction struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeShopifyPaymentsBalanceTransaction.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeShopifyPaymentsBalanceTransaction) GetTypename() *string { return v.Typename }

// findImageByIdNodeShopifyPaymentsBankAccount includes the requested fields of the GraphQL type ShopifyPaymentsBankAccount.
// The GraphQL type's documentation follows.
//
// A bank account that can receive payouts.
type findImageByIdNodeShopifyPaymentsBankAccount struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeShopifyPaymentsBankAccount.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeShopifyPaymentsBankAccount) GetTypename() *string { return v.Typename }

// findImageByIdNodeShopifyPaymentsDispute includes the requested fields of the GraphQL type ShopifyPaymentsDispute.
// The GraphQL type's documentation follows.
//
// A dispute occurs when a buyer questions the legitimacy of a charge with their financial institution.
type findImageByIdNodeShopifyPaymentsDispute struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeShopifyPaymentsDispute.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeShopifyPaymentsDispute) GetTypename() *string { return v.Typename }

// findImageByIdNodeShopifyPaymentsDisputeEvidence includes the requested fields of the GraphQL type ShopifyPaymentsDisputeEvidence.
// The GraphQL type's documentation follows.
//
// The evidence associated with the dispute.
type findImageByIdNodeShopifyPaymentsDisputeEvidence struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeShopifyPaymentsDisputeEvidence.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeShopifyPaymentsDisputeEvidence) GetTypename() *string { return v.Typename }

// findImageByIdNodeShopifyPaymentsDisputeFileUpload includes the requested fields of the GraphQL type ShopifyPaymentsDisputeFileUpload.
// The GraphQL type's documentation follows.
//
// The file upload associated with the dispute evidence.
type findImageByIdNodeShopifyPaymentsDisputeFileUpload struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeShopifyPaymentsDisputeFileUpload.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeShopifyPaymentsDisputeFileUpload) GetTypename() *string { return v.Typename }

// findImageByIdNodeShopifyPaymentsDisputeFulfillment includes the requested fields of the GraphQL type ShopifyPaymentsDisputeFulfillment.
// The GraphQL type's documentation follows.
//
// The fulfillment associated with dispute evidence.
type findImageByIdNodeShopifyPaymentsDisputeFulfillment struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeShopifyPaymentsDisputeFulfillment.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeShopifyPaymentsDisputeFulfillment) GetTypename() *string { return v.Typename }

// findImageByIdNodeShopifyPaymentsPayout includes the requested fields of the GraphQL type ShopifyPaymentsPayout.
// The GraphQL type's documentation follows.
//
// Payouts represent the movement of money between a merchant's Shopify
// Payments balance and their bank account.
type findImageByIdNodeShopifyPaymentsPayout struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeShopifyPaymentsPayout.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeShopifyPaymentsPayout) GetTypename() *string { return v.Typename }

// findImageByIdNodeShopifyPaymentsVerification includes the requested fields of the GraphQL type ShopifyPaymentsVerification.
// The GraphQL type's documentation follows.
//
// Each subject (individual) of an account has a verification object giving
// information about the verification state.
type findImageByIdNodeShopifyPaymentsVerification struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeShopifyPaymentsVerification.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeShopifyPaymentsVerification) GetTypename() *string { return v.Typename }

// findImageByIdNodeStaffMember includes the requested fields of the GraphQL type StaffMember.
// The GraphQL type's documentation follows.
//
// Represents the data about a staff member's Shopify account. Merchants can use
// staff member data to get more information about the staff members in their store.
type findImageByIdNodeStaffMember struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeStaffMember.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeStaffMember) GetTypename() *string { return v.Typename }

// findImageByIdNodeStandardMetafieldDefinitionTemplate includes the requested fields of the GraphQL type StandardMetafieldDefinitionTemplate.
// The GraphQL type's documentation follows.
//
// Standard metafield definition templates provide preset configurations to create metafield definitions.
// Each template has a specific namespace and key that we've reserved to have specific meanings for common use cases.
//
// Refer to the [list of standard metafield definitions](https://shopify.dev/apps/metafields/definitions/standard-definitions).
type findImageByIdNodeStandardMetafieldDefinitionTemplate struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeStandardMetafieldDefinitionTemplate.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeStandardMetafieldDefinitionTemplate) GetTypename() *string {
	return v.Typename
}

// findImageByIdNodeStoreCreditAccount includes the requested fields of the GraphQL type StoreCreditAccount.
// The GraphQL type's documentation follows.
//
// A store credit account contains a monetary balance that can be redeemed at checkout for purchases in the shop.
// The account is held in the specified currency and has an owner that cannot be transferred.
//
// The account balance is redeemable at checkout only when the owner is
// authenticated via [new customer accounts
// authentication](https://shopify.dev/docs/api/customer).
type findImageByIdNodeStoreCreditAccount struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeStoreCreditAccount.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeStoreCreditAccount) GetTypename() *string { return v.Typename }

// findImageByIdNodeStoreCreditAccountCreditTransaction includes the requested fields of the GraphQL type StoreCreditAccountCreditTransaction.
// The GraphQL type's documentation follows.
//
// A credit transaction which increases the store credit account balance.
type findImageByIdNodeStoreCreditAccountCreditTransaction struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeStoreCreditAccountCreditTransaction.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeStoreCreditAccountCreditTransaction) GetTypename() *string {
	return v.Typename
}

// findImageByIdNodeStoreCreditAccountDebitRevertTransaction includes the requested fields of the GraphQL type StoreCreditAccountDebitRevertTransaction.
// The GraphQL type's documentation follows.
//
// A debit revert transaction which increases the store credit account balance.
// Debit revert transactions are created automatically when a [store credit account debit transaction](https://shopify.dev/api/admin-graphql/latest/objects/StoreCreditAccountDebitTransaction) is reverted.
//
// Store credit account debit transactions are reverted when an order is cancelled,
// refunded or in the event of a payment failure at checkout.
// The amount added to the balance is equal to the amount reverted on the original credit.
type findImageByIdNodeStoreCreditAccountDebitRevertTransaction struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeStoreCreditAccountDebitRevertTransaction.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeStoreCreditAccountDebitRevertTransaction) GetTypename() *string {
	return v.Typename
}

// findImageByIdNodeStoreCreditAccountDebitTransaction includes the requested fields of the GraphQL type StoreCreditAccountDebitTransaction.
// The GraphQL type's documentation follows.
//
// A debit transaction which decreases the store credit account balance.
type findImageByIdNodeStoreCreditAccountDebitTransaction struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeStoreCreditAccountDebitTransaction.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeStoreCreditAccountDebitTransaction) GetTypename() *string {
	return v.Typename
}

// findImageByIdNodeStorefrontAccessToken includes the requested fields of the GraphQL type StorefrontAccessToken.
// The GraphQL type's documentation follows.
//
// A token that's used to delegate unauthenticated access scopes to clients that need to access
// the unauthenticated [Storefront API](https://shopify.dev/docs/api/storefront).
//
// An app can have a maximum of 100 active storefront access
// tokens for each shop.
//
// [Get started with the Storefront API](https://shopify.dev/docs/storefronts/headless/building-with-the-storefront-api/getting-started).
type findImageByIdNodeStorefrontAccessToken struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeStorefrontAccessToken.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeStorefrontAccessToken) GetTypename() *string { return v.Typename }

// findImageByIdNodeSubscriptionBillingAttempt includes the requested fields of the GraphQL type SubscriptionBillingAttempt.
// The GraphQL type's documentation follows.
//
// A record of an execution of the subscription billing process. Billing attempts use
// idempotency keys to avoid duplicate order creation. A successful billing attempt
// will create an order.
type findImageByIdNodeSubscriptionBillingAttempt struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeSubscriptionBillingAttempt.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeSubscriptionBillingAttempt) GetTypename() *string { return v.Typename }

// findImageByIdNodeSubscriptionContract includes the requested fields of the GraphQL type SubscriptionContract.
// The GraphQL type's documentation follows.
//
// Represents a Subscription Contract.
type findImageByIdNodeSubscriptionContract struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeSubscriptionContract.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeSubscriptionContract) GetTypename() *string { return v.Typename }

// findImageByIdNodeSubscriptionDraft includes the requested fields of the GraphQL type SubscriptionDraft.
// The GraphQL type's documentation follows.
//
// The `SubscriptionDraft` object represents a draft version of a
// [subscription contract](https://shopify.dev/docs/api/admin-graphql/latest/objects/SubscriptionContract)
// before it's committed. It serves as a staging area for making changes to an existing subscription or creating
// a new one. The draft allows you to preview and modify various aspects of a subscription before applying the changes.
//
// Use the `SubscriptionDraft` object to:
//
// - Add, remove, or modify subscription lines and their quantities
// - Manage discounts (add, remove, or update manual and code-based discounts)
// - Configure delivery options and shipping methods
// - Set up billing and delivery policies
// - Manage customer payment methods
// - Add custom attributes and notes to generated orders
// - Configure billing cycles and next billing dates
// - Preview the projected state of the subscription
//
// Each `SubscriptionDraft` object maintains a projected state that shows how the subscription will look after the changes
// are committed. This allows you to preview the impact of your modifications before applying them. The draft can be
// associated with an existing subscription contract (for modifications) or used to create a new subscription.
//
// The draft remains in a draft state until it's committed, at which point the changes are applied to the subscription
// contract and the draft is no longer accessible.
//
// Learn more about
// [how subscription contracts work](https://shopify.dev/docs/apps/build/purchase-options/subscriptions/contracts)
// and how to [build](https://shopify.dev/docs/apps/build/purchase-options/subscriptions/contracts/build-a-subscription-contract),
// [update](https://shopify.dev/docs/apps/build/purchase-options/subscriptions/contracts/update-a-subscription-contract), and
// [combine](https://shopify.dev/docs/apps/build/purchase-options/subscriptions/contracts/combine-subscription-contracts) subscription contracts.
type findImageByIdNodeSubscriptionDraft struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeSubscriptionDraft.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeSubscriptionDraft) GetTypename() *string { return v.Typename }

// findImageByIdNodeTaxonomyAttribute includes the requested fields of the GraphQL type TaxonomyAttribute.
// The GraphQL type's documentation follows.
//
// A Shopify product taxonomy attribute.
type findImageByIdNodeTaxonomyAttribute struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeTaxonomyAttribute.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeTaxonomyAttribute) GetTypename() *string { return v.Typename }

// findImageByIdNodeTaxonomyCategory includes the requested fields of the GraphQL type TaxonomyCategory.
// The GraphQL type's documentation follows.
//
// The details of a specific product category within the [Shopify product taxonomy](https://shopify.github.io/product-taxonomy/releases/unstable/?categoryId=sg-4-17-2-17).
type findImageByIdNodeTaxonomyCategory struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeTaxonomyCategory.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeTaxonomyCategory) GetTypename() *string { return v.Typename }

// findImageByIdNodeTaxonomyChoiceListAttribute includes the requested fields of the GraphQL type TaxonomyChoiceListAttribute.
// The GraphQL type's documentation follows.
//
// A Shopify product taxonomy choice list attribute.
type findImageByIdNodeTaxonomyChoiceListAttribute struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeTaxonomyChoiceListAttribute.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeTaxonomyChoiceListAttribute) GetTypename() *string { return v.Typename }

// findImageByIdNodeTaxonomyMeasurementAttribute includes the requested fields of the GraphQL type TaxonomyMeasurementAttribute.
// The GraphQL type's documentation follows.
//
// A Shopify product taxonomy measurement attribute.
type findImageByIdNodeTaxonomyMeasurementAttribute struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeTaxonomyMeasurementAttribute.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeTaxonomyMeasurementAttribute) GetTypename() *string { return v.Typename }

// findImageByIdNodeTaxonomyValue includes the requested fields of the GraphQL type TaxonomyValue.
// The GraphQL type's documentation follows.
//
// Represents a Shopify product taxonomy value.
type findImageByIdNodeTaxonomyValue struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeTaxonomyValue.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeTaxonomyValue) GetTypename() *string { return v.Typename }

// findImageByIdNodeTenderTransaction includes the requested fields of the GraphQL type TenderTransaction.
// The GraphQL type's documentation follows.
//
// A TenderTransaction represents a transaction with financial impact on a shop's balance sheet. A tender transaction always
// represents actual money movement between a buyer and a shop. TenderTransactions can be used instead of OrderTransactions
// for reconciling a shop's cash flow. A TenderTransaction is immutable once created.
type findImageByIdNodeTenderTransaction struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeTenderTransaction.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeTenderTransaction) GetTypename() *string { return v.Typename }

// findImageByIdNodeTransactionFee includes the requested fields of the GraphQL type TransactionFee.
// The GraphQL type's documentation follows.
//
// Transaction fee related to an order transaction.
type findImageByIdNodeTransactionFee struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeTransactionFee.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeTransactionFee) GetTypename() *string { return v.Typename }

// findImageByIdNodeUnverifiedReturnLineItem includes the requested fields of the GraphQL type UnverifiedReturnLineItem.
// The GraphQL type's documentation follows.
//
// An unverified return line item.
type findImageByIdNodeUnverifiedReturnLineItem struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeUnverifiedReturnLineItem.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeUnverifiedReturnLineItem) GetTypename() *string { return v.Typename }

// findImageByIdNodeUrlRedirect includes the requested fields of the GraphQL type UrlRedirect.
// The GraphQL type's documentation follows.
//
// The URL redirect for the online store.
type findImageByIdNodeUrlRedirect struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeUrlRedirect.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeUrlRedirect) GetTypename() *string { return v.Typename }

// findImageByIdNodeUrlRedirectImport includes the requested fields of the GraphQL type UrlRedirectImport.
// The GraphQL type's documentation follows.
//
// A request to import a [`URLRedirect`](https://shopify.dev/api/admin-graphql/latest/objects/UrlRedirect) object
// into the Online Store channel. Apps can use this to query the state of an `UrlRedirectImport` request.
//
// For more information, see [`url-redirect`](https://help.shopify.com/en/manual/online-store/menus-and-links/url-redirect)s.
type findImageByIdNodeUrlRedirectImport struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeUrlRedirectImport.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeUrlRedirectImport) GetTypename() *string { return v.Typename }

// findImageByIdNodeValidation includes the requested fields of the GraphQL type Validation.
// The GraphQL type's documentation follows.
//
// A checkout server side validation installed on the shop.
type findImageByIdNodeValidation struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeValidation.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeValidation) GetTypename() *string { return v.Typename }

// findImageByIdNodeVideo includes the requested fields of the GraphQL type Video.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted video.
type findImageByIdNodeVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeVideo.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeVideo) GetTypename() *string { return v.Typename }

// findImageByIdNodeWebPixel includes the requested fields of the GraphQL type WebPixel.
// The GraphQL type's documentation follows.
//
// The `WebPixel` object enables you to manage JavaScript code snippets
// that run on an online store and collect
// [behavioral data](https://shopify.dev/docs/api/web-pixels-api/standard-events)
// for marketing campaign optimization and analytics.
//
// Learn how to create a
// [web pixel extension](https://shopify.dev/docs/apps/build/marketing-analytics/build-web-pixels)
// to subscribe your app to events that are emitted by Shopify.
type findImageByIdNodeWebPixel struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeWebPixel.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeWebPixel) GetTypename() *string { return v.Typename }

// findImageByIdNodeWebPresence includes the requested fields of the GraphQL type WebPresence.
// The GraphQL type's documentation follows.
//
// This can be a domain (e.g. `example.ca`), subdomain (e.g. `ca.example.com`), or subfolders of the primary
// domain (e.g. `example.com/en-ca`). Each web presence comprises one or more language
// variants.
//
// Note: while the domain/subfolders defined by a web presence are not applicable to
// custom storefronts, which must manage their own domains and routing, the languages chosen
// here do govern [the languages available on the Storefront
// API](https://shopify.dev/custom-storefronts/internationalization/multiple-languages) for the countries
// using this web presence.
type findImageByIdNodeWebPresence struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeWebPresence.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeWebPresence) GetTypename() *string { return v.Typename }

// findImageByIdNodeWebhookSubscription includes the requested fields of the GraphQL type WebhookSubscription.
// The GraphQL type's documentation follows.
//
// A webhook subscription is a persisted data object created by an app using the REST Admin API or GraphQL Admin API.
// It describes the topic that the app wants to receive, and a destination where
// Shopify should send webhooks of the specified topic.
// When an event for a given topic occurs, the webhook subscription sends a relevant payload to the destination.
// Learn more about the [webhooks system](https://shopify.dev/apps/webhooks).
type findImageByIdNodeWebhookSubscription struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImageByIdNodeWebhookSubscription.Typename, and is useful for accessing the field via an interface.
func (v *findImageByIdNodeWebhookSubscription) GetTypename() *string { return v.Typename }

// findImageByIdResponse is returned by findImageById on success.
type findImageByIdResponse struct {
	// Returns a specific node (any object that implements the
	// [Node](https://shopify.dev/api/admin-graphql/latest/interfaces/Node)
	// interface) by ID, in accordance with the
	// [Relay specification](https://relay.dev/docs/guides/graphql-server-specification/#object-identification).
	// This field is commonly used for refetching an object.
	Node *findImageByIdNode `json:"-"`
}

// GetNode returns findImageByIdResponse.Node, and is useful for accessing the field via an interface.
func (v *findImageByIdResponse) GetNode() *findImageByIdNode { return v.Node }

func (v *findImageByIdResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*findImageByIdResponse
		Node json.RawMessage `json:"node"`
		graphql.NoUnmarshalJSON
	}
	firstPass.findImageByIdResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Node
		src := firstPass.Node
		if len(src) != 0 && string(src) != "null" {
			*dst = new(findImageByIdNode)
			err = __unmarshalfindImageByIdNode(
				src, *dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal findImageByIdResponse.Node: %w", err)
			}
		}
	}
	return nil
}

type __premarshalfindImageByIdResponse struct {
	Node json.RawMessage `json:"node"`
}

func (v *findImageByIdResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *findImageByIdResponse) __premarshalJSON() (*__premarshalfindImageByIdResponse, error) {
	var retval __premarshalfindImageByIdResponse

	{

		dst := &retval.Node
		src := v.Node
		if src != nil {
			var err error
			*dst, err = __marshalfindImageByIdNode(
				src)
			if err != nil {
				return nil, fmt.Errorf(
					"unable to marshal findImageByIdResponse.Node: %w", err)
			}
		}
	}
	return &retval, nil
}

// findImagesFilesFileConnection includes the requested fields of the GraphQL type FileConnection.
// The GraphQL type's documentation follows.
//
// An auto-generated type for paginating through multiple Files.
type findImagesFilesFileConnection struct {
	// A list of nodes that are contained in FileEdge. You can fetch data about an
	// individual node, or you can follow the edges to fetch data about a collection
	// of related nodes. At each node, you specify the fields that you want to retrieve.
	Nodes []findImagesFilesFileConnectionNodesFile `json:"-"`
	// An object that’s used to retrieve [cursor
	// information](https://shopify.dev/api/usage/pagination-graphql) about the current page.
	PageInfo findImagesFilesFileConnectionPageInfo `json:"pageInfo"`
}

// GetNodes returns findImagesFilesFileConnection.Nodes, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnection) GetNodes() []findImagesFilesFileConnectionNodesFile {
	return v.Nodes
}

// GetPageInfo returns findImagesFilesFileConnection.PageInfo, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnection) GetPageInfo() findImagesFilesFileConnectionPageInfo {
	return v.PageInfo
}

func (v *findImagesFilesFileConnection) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*findImagesFilesFileConnection
		Nodes []json.RawMessage `json:"nodes"`
		graphql.NoUnmarshalJSON
	}
	firstPass.findImagesFilesFileConnection = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Nodes
		src := firstPass.Nodes
		*dst = make(
			[]findImagesFilesFileConnectionNodesFile,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			if len(src) != 0 && string(src) != "null" {
				err = __unmarshalfindImagesFilesFileConnectionNodesFile(
					src, dst)
				if err != nil {
					return fmt.Errorf(
						"unable to unmarshal findImagesFilesFileConnection.Nodes: %w", err)
				}
			}
		}
	}
	return nil
}

type __premarshalfindImagesFilesFileConnection struct {
	Nodes []json.RawMessage `json:"nodes"`

	PageInfo findImagesFilesFileConnectionPageInfo `json:"pageInfo"`
}

func (v *findImagesFilesFileConnection) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *findImagesFilesFileConnection) __premarshalJSON() (*__premarshalfindImagesFilesFileConnection, error) {
	var retval __premarshalfindImagesFilesFileConnection

	{

		dst := &retval.Nodes
		src := v.Nodes
		*dst = make(
			[]json.RawMessage,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			var err error
			*dst, err = __marshalfindImagesFilesFileConnectionNodesFile(
				&src)
			if err != nil {
				return nil, fmt.Errorf(
					"unable to marshal findImagesFilesFileConnection.Nodes: %w", err)
			}
		}
	}
	retval.PageInfo = v.PageInfo
	return &retval, nil
}

// findImagesFilesFileConnectionNodesExternalVideo includes the requested fields of the GraphQL type ExternalVideo.
// The GraphQL type's documentation follows.
//
// Represents a video hosted outside of Shopify.
type findImagesFilesFileConnectionNodesExternalVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImagesFilesFileConnectionNodesExternalVideo.Typename, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesExternalVideo) GetTypename() *string { return v.Typename }

// findImagesFilesFileConnectionNodesFile includes the requested fields of the GraphQL interface File.
//
// findImagesFilesFileConnectionNodesFile is implemented by the following types:
// findImagesFilesFileConnectionNodesExternalVideo
// findImagesFilesFileConnectionNodesGenericFile
// findImagesFilesFileConnectionNodesMediaImage
// findImagesFilesFileConnectionNodesModel3d
// findImagesFilesFileConnectionNodesVideo
// The GraphQL type's documentation follows.
//
// A file interface.
type findImagesFilesFileConnectionNodesFile interface {
	implementsGraphQLInterfacefindImagesFilesFileConnectionNodesFile()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() *string
}

func (v *findImagesFilesFileConnectionNodesExternalVideo) implementsGraphQLInterfacefindImagesFilesFileConnectionNodesFile() {
}
func (v *findImagesFilesFileConnectionNodesGenericFile) implementsGraphQLInterfacefindImagesFilesFileConnectionNodesFile() {
}
func (v *findImagesFilesFileConnectionNodesMediaImage) implementsGraphQLInterfacefindImagesFilesFileConnectionNodesFile() {
}
func (v *findImagesFilesFileConnectionNodesModel3d) implementsGraphQLInterfacefindImagesFilesFileConnectionNodesFile() {
}
func (v *findImagesFilesFileConnectionNodesVideo) implementsGraphQLInterfacefindImagesFilesFileConnectionNodesFile() {
}

func __unmarshalfindImagesFilesFileConnectionNodesFile(b []byte, v *findImagesFilesFileConnectionNodesFile) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "ExternalVideo":
		*v = new(findImagesFilesFileConnectionNodesExternalVideo)
		return json.Unmarshal(b, *v)
	case "GenericFile":
		*v = new(findImagesFilesFileConnectionNodesGenericFile)
		return json.Unmarshal(b, *v)
	case "MediaImage":
		*v = new(findImagesFilesFileConnectionNodesMediaImage)
		return json.Unmarshal(b, *v)
	case "Model3d":
		*v = new(findImagesFilesFileConnectionNodesModel3d)
		return json.Unmarshal(b, *v)
	case "Video":
		*v = new(findImagesFilesFileConnectionNodesVideo)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing File.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for findImagesFilesFileConnectionNodesFile: "%v"`, tn.TypeName)
	}
}

func __marshalfindImagesFilesFileConnectionNodesFile(v *findImagesFilesFileConnectionNodesFile) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *findImagesFilesFileConnectionNodesExternalVideo:
		typename = "ExternalVideo"

		result := struct {
			TypeName string `json:"__typename"`
			*findImagesFilesFileConnectionNodesExternalVideo
		}{typename, v}
		return json.Marshal(result)
	case *findImagesFilesFileConnectionNodesGenericFile:
		typename = "GenericFile"

		result := struct {
			TypeName string `json:"__typename"`
			*findImagesFilesFileConnectionNodesGenericFile
		}{typename, v}
		return json.Marshal(result)
	case *findImagesFilesFileConnectionNodesMediaImage:
		typename = "MediaImage"

		result := struct {
			TypeName string `json:"__typename"`
			*findImagesFilesFileConnectionNodesMediaImage
		}{typename, v}
		return json.Marshal(result)
	case *findImagesFilesFileConnectionNodesModel3d:
		typename = "Model3d"

		result := struct {
			TypeName string `json:"__typename"`
			*findImagesFilesFileConnectionNodesModel3d
		}{typename, v}
		return json.Marshal(result)
	case *findImagesFilesFileConnectionNodesVideo:
		typename = "Video"

		result := struct {
			TypeName string `json:"__typename"`
			*findImagesFilesFileConnectionNodesVideo
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for findImagesFilesFileConnectionNodesFile: "%T"`, v)
	}
}

// findImagesFilesFileConnectionNodesGenericFile includes the requested fields of the GraphQL type GenericFile.
// The GraphQL type's documentation follows.
//
// Represents any file other than HTML.
type findImagesFilesFileConnectionNodesGenericFile struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImagesFilesFileConnectionNodesGenericFile.Typename, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesGenericFile) GetTypename() *string { return v.Typename }

// findImagesFilesFileConnectionNodesMediaImage includes the requested fields of the GraphQL type MediaImage.
// The GraphQL type's documentation follows.
//
// The `MediaImage` object represents an image hosted on Shopify's
// [content delivery network (CDN)](https://shopify.dev/docs/storefronts/themes/best-practices/performance/platform#shopify-cdn).
// Shopify CDN is a content system that serves as the primary way to store,
// manage, and deliver visual content for products, variants, and other resources across the Shopify platform.
//
// The `MediaImage` object provides information to:
//
// - Store and display product and variant images across online stores, admin interfaces, and mobile apps.
// - Retrieve visual branding elements, including logos, banners, favicons, and background images in checkout flows.
// - Retrieve signed URLs for secure, time-limited access to original image files.
//
// Each `MediaImage` object provides both the processed image data (with automatic optimization and CDN delivery)
// and access to the original source file. The image processing is handled asynchronously, so images
// might not be immediately available after upload. The
// [`status`](https://shopify.dev/docs/api/admin-graphql/latest/objects/mediaimage#field-MediaImage.fields.status)
// field indicates when processing is complete and the image is ready for use.
//
// The `MediaImage` object implements the [`Media`](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/Media)
// interface alongside other media types, like videos and 3D models.
//
// Learn about
// managing media for [products](https://shopify.dev/docs/apps/build/online-store/product-media),
// [product variants](https://shopify.dev/docs/apps/build/online-store/product-variant-media), and
// [asynchronous media management](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components#asynchronous-media-management).
type findImagesFilesFileConnectionNodesMediaImage struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// Current status of the media.
	Status MediaStatus `json:"status"`
	// The MIME type of the image.
	MimeType *string `json:"mimeType"`
	// The image for the media. Returns `null` until `status` is `READY`.
	Image *findImagesFilesFileConnectionNodesMediaImageImage `json:"image"`
	// The original source of the image.
	OriginalSource *findImagesFilesFileConnectionNodesMediaImageOriginalSource `json:"originalSource"`
}

// GetTypename returns findImagesFilesFileConnectionNodesMediaImage.Typename, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesMediaImage) GetTypename() *string { return v.Typename }

// GetId returns findImagesFilesFileConnectionNodesMediaImage.Id, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesMediaImage) GetId() string { return v.Id }

// GetStatus returns findImagesFilesFileConnectionNodesMediaImage.Status, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesMediaImage) GetStatus() MediaStatus { return v.Status }

// GetMimeType returns findImagesFilesFileConnectionNodesMediaImage.MimeType, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesMediaImage) GetMimeType() *string { return v.MimeType }

// GetImage returns findImagesFilesFileConnectionNodesMediaImage.Image, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesMediaImage) GetImage() *findImagesFilesFileConnectionNodesMediaImageImage {
	return v.Image
}

// GetOriginalSource returns findImagesFilesFileConnectionNodesMediaImage.OriginalSource, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesMediaImage) GetOriginalSource() *findImagesFilesFileConnectionNodesMediaImageOriginalSource {
	return v.OriginalSource
}

// findImagesFilesFileConnectionNodesMediaImageImage includes the requested fields of the GraphQL type Image.
// The GraphQL type's documentation follows.
//
// Represents an image resource.
type findImagesFilesFileConnectionNodesMediaImageImage struct {
	// The original width of the image in pixels. Returns `null` if the image isn't hosted by Shopify.
	Width *int `json:"width"`
	// The original height of the image in pixels. Returns `null` if the image isn't hosted by Shopify.
	Height *int `json:"height"`
	// The location of the image as a URL.
	//
	// If no transform options are specified, then the original image will be preserved including any pre-applied transforms.
	//
	// All transformation options are considered "best-effort". Any transformation
	// that the original image type doesn't support will be ignored.
	//
	// If you need multiple variations of the same image, then you can use [GraphQL
	// aliases](https://graphql.org/learn/queries/#aliases).
	Url string `json:"url"`
	// The ThumbHash of the image.
	//
	// Useful to display placeholder images while the original image is loading.
	Thumbhash *string `json:"thumbhash"`
}

// GetWidth returns findImagesFilesFileConnectionNodesMediaImageImage.Width, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesMediaImageImage) GetWidth() *int { return v.Width }

// GetHeight returns findImagesFilesFileConnectionNodesMediaImageImage.Height, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesMediaImageImage) GetHeight() *int { return v.Height }

// GetUrl returns findImagesFilesFileConnectionNodesMediaImageImage.Url, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesMediaImageImage) GetUrl() string { return v.Url }

// GetThumbhash returns findImagesFilesFileConnectionNodesMediaImageImage.Thumbhash, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesMediaImageImage) GetThumbhash() *string {
	return v.Thumbhash
}

// findImagesFilesFileConnectionNodesMediaImageOriginalSource includes the requested fields of the GraphQL type MediaImageOriginalSource.
// The GraphQL type's documentation follows.
//
// The original source for an image.
type findImagesFilesFileConnectionNodesMediaImageOriginalSource struct {
	// The size of the original file in bytes.
	FileSize *int `json:"fileSize"`
	// The URL of the original image, valid only for a short period.
	Url *string `json:"url"`
}

// GetFileSize returns findImagesFilesFileConnectionNodesMediaImageOriginalSource.FileSize, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesMediaImageOriginalSource) GetFileSize() *int {
	return v.FileSize
}

// GetUrl returns findImagesFilesFileConnectionNodesMediaImageOriginalSource.Url, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesMediaImageOriginalSource) GetUrl() *string { return v.Url }

// findImagesFilesFileConnectionNodesModel3d includes the requested fields of the GraphQL type Model3d.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted 3D model.
type findImagesFilesFileConnectionNodesModel3d struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImagesFilesFileConnectionNodesModel3d.Typename, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesModel3d) GetTypename() *string { return v.Typename }

// findImagesFilesFileConnectionNodesVideo includes the requested fields of the GraphQL type Video.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted video.
type findImagesFilesFileConnectionNodesVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findImagesFilesFileConnectionNodesVideo.Typename, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionNodesVideo) GetTypename() *string { return v.Typename }

// findImagesFilesFileConnectionPageInfo includes the requested fields of the GraphQL type PageInfo.
// The GraphQL type's documentation follows.
//
// Returns information about pagination in a connection, in accordance with the
// [Relay specification](https://relay.dev/graphql/connections.htm#sec-undefined.PageInfo).
// For more information, please read our [GraphQL Pagination Usage Guide](https://shopify.dev/api/usage/pagination-graphql).
type findImagesFilesFileConnectionPageInfo struct {
	// The cursor corresponding to the last node in edges.
	EndCursor *string `json:"endCursor"`
}

// GetEndCursor returns findImagesFilesFileConnectionPageInfo.EndCursor, and is useful for accessing the field via an interface.
func (v *findImagesFilesFileConnectionPageInfo) GetEndCursor() *string { return v.EndCursor }

// findImagesResponse is returned by findImages on success.
type findImagesResponse struct {
	// Retrieves a paginated list of files that have been uploaded to a Shopify store. Files represent digital assets
	// that merchants can upload to their store for various purposes including product images, marketing materials,
	// documents, and brand assets.
	//
	// Use the `files` query to retrieve information associated with the following workflows:
	//
	// - [Managing product media and images](https://shopify.dev/docs/apps/build/online-store/product-media)
	// - [Theme development and asset management](https://shopify.dev/docs/storefronts/themes/store/success/brand-assets)
	// - Brand asset management and [checkout branding](https://shopify.dev/docs/apps/build/checkout/styling/add-favicon)
	//
	// Files can include multiple [content types](https://shopify.dev/docs/api/admin-graphql/latest/enums/FileContentType),
	// such as images, videos, 3D models, and generic files. Each file has
	// properties like dimensions, file size, alt text for accessibility, and upload status. Files can be filtered
	// by [media type](https://shopify.dev/docs/api/admin-graphql/latest/enums/MediaContentType) and can be associated with
	// [products](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product),
	// [themes](https://shopify.dev/docs/api/admin-graphql/latest/objects/OnlineStoreTheme),
	// and other store resources.
	Files findImagesFilesFileConnection `json:"files"`
}

// GetFiles returns findImagesResponse.Files, and is useful for accessing the field via an interface.
func (v *findImagesResponse) GetFiles() findImagesFilesFileConnection { return v.Files }

// findMasterImageFilesFileConnection includes the requested fields of the GraphQL type FileConnection.
// The GraphQL type's documentation follows.
//
// An auto-generated type for paginating through multiple Files.
type findMasterImageFilesFileConnection struct {
	// A list of nodes that are contained in FileEdge. You can fetch data about an
	// individual node, or you can follow the edges to fetch data about a collection
	// of related nodes. At each node, you specify the fields that you want to retrieve.
	Nodes []findMasterImageFilesFileConnectionNodesFile `json:"-"`
	// An object that’s used to retrieve [cursor
	// information](https://shopify.dev/api/usage/pagination-graphql) about the current page.
	PageInfo findMasterImageFilesFileConnectionPageInfo `json:"pageInfo"`
}

// GetNodes returns findMasterImageFilesFileConnection.Nodes, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnection) GetNodes() []findMasterImageFilesFileConnectionNodesFile {
	return v.Nodes
}

// GetPageInfo returns findMasterImageFilesFileConnection.PageInfo, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnection) GetPageInfo() findMasterImageFilesFileConnectionPageInfo {
	return v.PageInfo
}

func (v *findMasterImageFilesFileConnection) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*findMasterImageFilesFileConnection
		Nodes []json.RawMessage `json:"nodes"`
		graphql.NoUnmarshalJSON
	}
	firstPass.findMasterImageFilesFileConnection = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Nodes
		src := firstPass.Nodes
		*dst = make(
			[]findMasterImageFilesFileConnectionNodesFile,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			if len(src) != 0 && string(src) != "null" {
				err = __unmarshalfindMasterImageFilesFileConnectionNodesFile(
					src, dst)
				if err != nil {
					return fmt.Errorf(
						"unable to unmarshal findMasterImageFilesFileConnection.Nodes: %w", err)
				}
			}
		}
	}
	return nil
}

type __premarshalfindMasterImageFilesFileConnection struct {
	Nodes []json.RawMessage `json:"nodes"`

	PageInfo findMasterImageFilesFileConnectionPageInfo `json:"pageInfo"`
}

func (v *findMasterImageFilesFileConnection) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *findMasterImageFilesFileConnection) __premarshalJSON() (*__premarshalfindMasterImageFilesFileConnection, error) {
	var retval __premarshalfindMasterImageFilesFileConnection

	{

		dst := &retval.Nodes
		src := v.Nodes
		*dst = make(
			[]json.RawMessage,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			var err error
			*dst, err = __marshalfindMasterImageFilesFileConnectionNodesFile(
				&src)
			if err != nil {
				return nil, fmt.Errorf(
					"unable to marshal findMasterImageFilesFileConnection.Nodes: %w", err)
			}
		}
	}
	retval.PageInfo = v.PageInfo
	return &retval, nil
}

// findMasterImageFilesFileConnectionNodesExternalVideo includes the requested fields of the GraphQL type ExternalVideo.
// The GraphQL type's documentation follows.
//
// Represents a video hosted outside of Shopify.
type findMasterImageFilesFileConnectionNodesExternalVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findMasterImageFilesFileConnectionNodesExternalVideo.Typename, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnectionNodesExternalVideo) GetTypename() *string {
	return v.Typename
}

// findMasterImageFilesFileConnectionNodesFile includes the requested fields of the GraphQL interface File.
//
// findMasterImageFilesFileConnectionNodesFile is implemented by the following types:
// findMasterImageFilesFileConnectionNodesExternalVideo
// findMasterImageFilesFileConnectionNodesGenericFile
// findMasterImageFilesFileConnectionNodesMediaImage
// findMasterImageFilesFileConnectionNodesModel3d
// findMasterImageFilesFileConnectionNodesVideo
// The GraphQL type's documentation follows.
//
// A file interface.
type findMasterImageFilesFileConnectionNodesFile interface {
	implementsGraphQLInterfacefindMasterImageFilesFileConnectionNodesFile()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() *string
}

func (v *findMasterImageFilesFileConnectionNodesExternalVideo) implementsGraphQLInterfacefindMasterImageFilesFileConnectionNodesFile() {
}
func (v *findMasterImageFilesFileConnectionNodesGenericFile) implementsGraphQLInterfacefindMasterImageFilesFileConnectionNodesFile() {
}
func (v *findMasterImageFilesFileConnectionNodesMediaImage) implementsGraphQLInterfacefindMasterImageFilesFileConnectionNodesFile() {
}
func (v *findMasterImageFilesFileConnectionNodesModel3d) implementsGraphQLInterfacefindMasterImageFilesFileConnectionNodesFile() {
}
func (v *findMasterImageFilesFileConnectionNodesVideo) implementsGraphQLInterfacefindMasterImageFilesFileConnectionNodesFile() {
}

func __unmarshalfindMasterImageFilesFileConnectionNodesFile(b []byte, v *findMasterImageFilesFileConnectionNodesFile) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "ExternalVideo":
		*v = new(findMasterImageFilesFileConnectionNodesExternalVideo)
		return json.Unmarshal(b, *v)
	case "GenericFile":
		*v = new(findMasterImageFilesFileConnectionNodesGenericFile)
		return json.Unmarshal(b, *v)
	case "MediaImage":
		*v = new(findMasterImageFilesFileConnectionNodesMediaImage)
		return json.Unmarshal(b, *v)
	case "Model3d":
		*v = new(findMasterImageFilesFileConnectionNodesModel3d)
		return json.Unmarshal(b, *v)
	case "Video":
		*v = new(findMasterImageFilesFileConnectionNodesVideo)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing File.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for findMasterImageFilesFileConnectionNodesFile: "%v"`, tn.TypeName)
	}
}

func __marshalfindMasterImageFilesFileConnectionNodesFile(v *findMasterImageFilesFileConnectionNodesFile) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *findMasterImageFilesFileConnectionNodesExternalVideo:
		typename = "ExternalVideo"

		result := struct {
			TypeName string `json:"__typename"`
			*findMasterImageFilesFileConnectionNodesExternalVideo
		}{typename, v}
		return json.Marshal(result)
	case *findMasterImageFilesFileConnectionNodesGenericFile:
		typename = "GenericFile"

		result := struct {
			TypeName string `json:"__typename"`
			*findMasterImageFilesFileConnectionNodesGenericFile
		}{typename, v}
		return json.Marshal(result)
	case *findMasterImageFilesFileConnectionNodesMediaImage:
		typename = "MediaImage"

		result := struct {
			TypeName string `json:"__typename"`
			*findMasterImageFilesFileConnectionNodesMediaImage
		}{typename, v}
		return json.Marshal(result)
	case *findMasterImageFilesFileConnectionNodesModel3d:
		typename = "Model3d"

		result := struct {
			TypeName string `json:"__typename"`
			*findMasterImageFilesFileConnectionNodesModel3d
		}{typename, v}
		return json.Marshal(result)
	case *findMasterImageFilesFileConnectionNodesVideo:
		typename = "Video"

		result := struct {
			TypeName string `json:"__typename"`
			*findMasterImageFilesFileConnectionNodesVideo
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for findMasterImageFilesFileConnectionNodesFile: "%T"`, v)
	}
}

// findMasterImageFilesFileConnectionNodesGenericFile includes the requested fields of the GraphQL type GenericFile.
// The GraphQL type's documentation follows.
//
// Represents any file other than HTML.
type findMasterImageFilesFileConnectionNodesGenericFile struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findMasterImageFilesFileConnectionNodesGenericFile.Typename, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnectionNodesGenericFile) GetTypename() *string { return v.Typename }

// findMasterImageFilesFileConnectionNodesMediaImage includes the requested fields of the GraphQL type MediaImage.
// The GraphQL type's documentation follows.
//
// The `MediaImage` object represents an image hosted on Shopify's
// [content delivery network (CDN)](https://shopify.dev/docs/storefronts/themes/best-practices/performance/platform#shopify-cdn).
// Shopify CDN is a content system that serves as the primary way to store,
// manage, and deliver visual content for products, variants, and other resources across the Shopify platform.
//
// The `MediaImage` object provides information to:
//
// - Store and display product and variant images across online stores, admin interfaces, and mobile apps.
// - Retrieve visual branding elements, including logos, banners, favicons, and background images in checkout flows.
// - Retrieve signed URLs for secure, time-limited access to original image files.
//
// Each `MediaImage` object provides both the processed image data (with automatic optimization and CDN delivery)
// and access to the original source file. The image processing is handled asynchronously, so images
// might not be immediately available after upload. The
// [`status`](https://shopify.dev/docs/api/admin-graphql/latest/objects/mediaimage#field-MediaImage.fields.status)
// field indicates when processing is complete and the image is ready for use.
//
// The `MediaImage` object implements the [`Media`](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/Media)
// interface alongside other media types, like videos and 3D models.
//
// Learn about
// managing media for [products](https://shopify.dev/docs/apps/build/online-store/product-media),
// [product variants](https://shopify.dev/docs/apps/build/online-store/product-variant-media), and
// [asynchronous media management](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components#asynchronous-media-management).
type findMasterImageFilesFileConnectionNodesMediaImage struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// Current status of the media.
	Status MediaStatus `json:"status"`
	// The image for the media. Returns `null` until `status` is `READY`.
	Image *findMasterImageFilesFileConnectionNodesMediaImageImage `json:"image"`
}

// GetTypename returns findMasterImageFilesFileConnectionNodesMediaImage.Typename, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnectionNodesMediaImage) GetTypename() *string { return v.Typename }

// GetId returns findMasterImageFilesFileConnectionNodesMediaImage.Id, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnectionNodesMediaImage) GetId() string { return v.Id }

// GetStatus returns findMasterImageFilesFileConnectionNodesMediaImage.Status, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnectionNodesMediaImage) GetStatus() MediaStatus { return v.Status }

// GetImage returns findMasterImageFilesFileConnectionNodesMediaImage.Image, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnectionNodesMediaImage) GetImage() *findMasterImageFilesFileConnectionNodesMediaImageImage {
	return v.Image
}

// findMasterImageFilesFileConnectionNodesMediaImageImage includes the requested fields of the GraphQL type Image.
// The GraphQL type's documentation follows.
//
// Represents an image resource.
type findMasterImageFilesFileConnectionNodesMediaImageImage struct {
	// The location of the image as a URL.
	//
	// If no transform options are specified, then the original image will be preserved including any pre-applied transforms.
	//
	// All transformation options are considered "best-effort". Any transformation
	// that the original image type doesn't support will be ignored.
	//
	// If you need multiple variations of the same image, then you can use [GraphQL
	// aliases](https://graphql.org/learn/queries/#aliases).
	Url string `json:"url"`
}

// GetUrl returns findMasterImageFilesFileConnectionNodesMediaImageImage.Url, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnectionNodesMediaImageImage) GetUrl() string { return v.Url }

// findMasterImageFilesFileConnectionNodesModel3d includes the requested fields of the GraphQL type Model3d.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted 3D model.
type findMasterImageFilesFileConnectionNodesModel3d struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findMasterImageFilesFileConnectionNodesModel3d.Typename, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnectionNodesModel3d) GetTypename() *string { return v.Typename }

// findMasterImageFilesFileConnectionNodesVideo includes the requested fields of the GraphQL type Video.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted video.
type findMasterImageFilesFileConnectionNodesVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findMasterImageFilesFileConnectionNodesVideo.Typename, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnectionNodesVideo) GetTypename() *string { return v.Typename }

// findMasterImageFilesFileConnectionPageInfo includes the requested fields of the GraphQL type PageInfo.
// The GraphQL type's documentation follows.
//
// Returns information about pagination in a connection, in accordance with the
// [Relay specification](https://relay.dev/graphql/connections.htm#sec-undefined.PageInfo).
// For more information, please read our [GraphQL Pagination Usage Guide](https://shopify.dev/api/usage/pagination-graphql).
type findMasterImageFilesFileConnectionPageInfo struct {
	// The cursor corresponding to the last node in edges.
	EndCursor *string `json:"endCursor"`
	// Whether there are more pages to fetch following the current page.
	HasNextPage bool `json:"hasNextPage"`
}

// GetEndCursor returns findMasterImageFilesFileConnectionPageInfo.EndCursor, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnectionPageInfo) GetEndCursor() *string { return v.EndCursor }

// GetHasNextPage returns findMasterImageFilesFileConnectionPageInfo.HasNextPage, and is useful for accessing the field via an interface.
func (v *findMasterImageFilesFileConnectionPageInfo) GetHasNextPage() bool { return v.HasNextPage }

// findMasterImageResponse is returned by findMasterImage on success.
type findMasterImageResponse struct {
	// Retrieves a paginated list of files that have been uploaded to a Shopify store. Files represent digital assets
	// that merchants can upload to their store for various purposes including product images, marketing materials,
	// documents, and brand assets.
	//
	// Use the `files` query to retrieve information associated with the following workflows:
	//
	// - [Managing product media and images](https://shopify.dev/docs/apps/build/online-store/product-media)
	// - [Theme development and asset management](https://shopify.dev/docs/storefronts/themes/store/success/brand-assets)
	// - Brand asset management and [checkout branding](https://shopify.dev/docs/apps/build/checkout/styling/add-favicon)
	//
	// Files can include multiple [content types](https://shopify.dev/docs/api/admin-graphql/latest/enums/FileContentType),
	// such as images, videos, 3D models, and generic files. Each file has
	// properties like dimensions, file size, alt text for accessibility, and upload status. Files can be filtered
	// by [media type](https://shopify.dev/docs/api/admin-graphql/latest/enums/MediaContentType) and can be associated with
	// [products](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product),
	// [themes](https://shopify.dev/docs/api/admin-graphql/latest/objects/OnlineStoreTheme),
	// and other store resources.
	Files findMasterImageFilesFileConnection `json:"files"`
}

// GetFiles returns findMasterImageResponse.Files, and is useful for accessing the field via an interface.
func (v *findMasterImageResponse) GetFiles() findMasterImageFilesFileConnection { return v.Files }

// findProductImagesProductsProductConnection includes the requested fields of the GraphQL type ProductConnection.
// The GraphQL type's documentation follows.
//
// An auto-generated type for paginating through multiple Products.
type findProductImagesProductsProductConnection struct {
	// A list of nodes that are contained in ProductEdge. You can fetch data about an
	// individual node, or you can follow the edges to fetch data about a collection
	// of related nodes. At each node, you specify the fields that you want to retrieve.
	Nodes []findProductImagesProductsProductConnectionNodesProduct `json:"nodes"`
}

// GetNodes returns findProductImagesProductsProductConnection.Nodes, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnection) GetNodes() []findProductImagesProductsProductConnectionNodesProduct {
	return v.Nodes
}

// findProductImagesProductsProductConnectionNodesProduct includes the requested fields of the GraphQL type Product.
// The GraphQL type's documentation follows.
//
// The `Product` object lets you manage products in a merchant’s store.
//
// Products are the goods and services that merchants offer to customers. They can
// include various details such as title, description, price, images, and options
// such as size or color.
// You can use [product variants](https://shopify.dev/docs/api/admin-graphql/latest/objects/productvariant)
// to create or update different versions of the same product.
// You can also add or update product [media](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/media).
// Products can be organized by grouping them into a [collection](https://shopify.dev/docs/api/admin-graphql/latest/objects/collection).
//
// Learn more about working with [Shopify's product model](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components),
// including limitations and considerations.
type findProductImagesProductsProductConnectionNodesProduct struct {
	// A globally-unique ID.
	Id string `json:"id"`
	// The name for the product that displays to customers. The title is used to construct the product's handle.
	// For example, if a product is titled "Black Sunglasses", then the handle is `black-sunglasses`.
	Title string `json:"title"`
	// Whether the product has only a single variant with the default option and value.
	HasOnlyDefaultVariant bool `json:"hasOnlyDefaultVariant"`
	// The [media](https://shopify.dev/docs/apps/build/online-store/product-media)
	// associated with the product. Valid media are images, 3D models, videos.
	Media findProductImagesProductsProductConnectionNodesProductMediaMediaConnection `json:"media"`
}

// GetId returns findProductImagesProductsProductConnectionNodesProduct.Id, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProduct) GetId() string { return v.Id }

// GetTitle returns findProductImagesProductsProductConnectionNodesProduct.Title, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProduct) GetTitle() string { return v.Title }

// GetHasOnlyDefaultVariant returns findProductImagesProductsProductConnectionNodesProduct.HasOnlyDefaultVariant, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProduct) GetHasOnlyDefaultVariant() bool {
	return v.HasOnlyDefaultVariant
}

// GetMedia returns findProductImagesProductsProductConnectionNodesProduct.Media, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProduct) GetMedia() findProductImagesProductsProductConnectionNodesProductMediaMediaConnection {
	return v.Media
}

// findProductImagesProductsProductConnectionNodesProductMediaMediaConnection includes the requested fields of the GraphQL type MediaConnection.
// The GraphQL type's documentation follows.
//
// An auto-generated type for paginating through multiple Media.
type findProductImagesProductsProductConnectionNodesProductMediaMediaConnection struct {
	// A list of nodes that are contained in MediaEdge. You can fetch data about an
	// individual node, or you can follow the edges to fetch data about a collection
	// of related nodes. At each node, you specify the fields that you want to retrieve.
	Nodes []findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia `json:"-"`
}

// GetNodes returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnection.Nodes, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnection) GetNodes() []findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia {
	return v.Nodes
}

func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnection) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*findProductImagesProductsProductConnectionNodesProductMediaMediaConnection
		Nodes []json.RawMessage `json:"nodes"`
		graphql.NoUnmarshalJSON
	}
	firstPass.findProductImagesProductsProductConnectionNodesProductMediaMediaConnection = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Nodes
		src := firstPass.Nodes
		*dst = make(
			[]findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			if len(src) != 0 && string(src) != "null" {
				err = __unmarshalfindProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia(
					src, dst)
				if err != nil {
					return fmt.Errorf(
						"unable to unmarshal findProductImagesProductsProductConnectionNodesProductMediaMediaConnection.Nodes: %w", err)
				}
			}
		}
	}
	return nil
}

type __premarshalfindProductImagesProductsProductConnectionNodesProductMediaMediaConnection struct {
	Nodes []json.RawMessage `json:"nodes"`
}

func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnection) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnection) __premarshalJSON() (*__premarshalfindProductImagesProductsProductConnectionNodesProductMediaMediaConnection, error) {
	var retval __premarshalfindProductImagesProductsProductConnectionNodesProductMediaMediaConnection

	{

		dst := &retval.Nodes
		src := v.Nodes
		*dst = make(
			[]json.RawMessage,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			var err error
			*dst, err = __marshalfindProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia(
				&src)
			if err != nil {
				return nil, fmt.Errorf(
					"unable to marshal findProductImagesProductsProductConnectionNodesProductMediaMediaConnection.Nodes: %w", err)
			}
		}
	}
	return &retval, nil
}

// findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo includes the requested fields of the GraphQL type ExternalVideo.
// The GraphQL type's documentation follows.
//
// Represents a video hosted outside of Shopify.
type findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo.Typename, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo) GetTypename() *string {
	return v.Typename
}

// findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia includes the requested fields of the GraphQL interface Media.
//
// findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia is implemented by the following types:
// findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo
// findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage
// findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d
// findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo
// The GraphQL type's documentation follows.
//
// Represents a media interface.
type findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia interface {
	implementsGraphQLInterfacefindProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() *string
}

func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo) implementsGraphQLInterfacefindProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia() {
}
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) implementsGraphQLInterfacefindProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia() {
}
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d) implementsGraphQLInterfacefindProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia() {
}
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo) implementsGraphQLInterfacefindProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia() {
}

func __unmarshalfindProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia(b []byte, v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "ExternalVideo":
		*v = new(findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo)
		return json.Unmarshal(b, *v)
	case "MediaImage":
		*v = new(findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage)
		return json.Unmarshal(b, *v)
	case "Model3d":
		*v = new(findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d)
		return json.Unmarshal(b, *v)
	case "Video":
		*v = new(findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing Media.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia: "%v"`, tn.TypeName)
	}
}

func __marshalfindProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia(v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo:
		typename = "ExternalVideo"

		result := struct {
			TypeName string `json:"__typename"`
			*findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo
		}{typename, v}
		return json.Marshal(result)
	case *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage:
		typename = "MediaImage"

		result := struct {
			TypeName string `json:"__typename"`
			*findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage
		}{typename, v}
		return json.Marshal(result)
	case *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d:
		typename = "Model3d"

		result := struct {
			TypeName string `json:"__typename"`
			*findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d
		}{typename, v}
		return json.Marshal(result)
	case *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo:
		typename = "Video"

		result := struct {
			TypeName string `json:"__typename"`
			*findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia: "%T"`, v)
	}
}

// findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage includes the requested fields of the GraphQL type MediaImage.
// The GraphQL type's documentation follows.
//
// The `MediaImage` object represents an image hosted on Shopify's
// [content delivery network (CDN)](https://shopify.dev/docs/storefronts/themes/best-practices/performance/platform#shopify-cdn).
// Shopify CDN is a content system that serves as the primary way to store,
// manage, and deliver visual content for products, variants, and other resources across the Shopify platform.
//
// The `MediaImage` object provides information to:
//
// - Store and display product and variant images across online stores, admin interfaces, and mobile apps.
// - Retrieve visual branding elements, including logos, banners, favicons, and background images in checkout flows.
// - Retrieve signed URLs for secure, time-limited access to original image files.
//
// Each `MediaImage` object provides both the processed image data (with automatic optimization and CDN delivery)
// and access to the original source file. The image processing is handled asynchronously, so images
// might not be immediately available after upload. The
// [`status`](https://shopify.dev/docs/api/admin-graphql/latest/objects/mediaimage#field-MediaImage.fields.status)
// field indicates when processing is complete and the image is ready for use.
//
// The `MediaImage` object implements the [`Media`](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/Media)
// interface alongside other media types, like videos and 3D models.
//
// Learn about
// managing media for [products](https://shopify.dev/docs/apps/build/online-store/product-media),
// [product variants](https://shopify.dev/docs/apps/build/online-store/product-variant-media), and
// [asynchronous media management](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components#asynchronous-media-management).
type findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// Current status of the media.
	Status MediaStatus `json:"status"`
	// The MIME type of the image.
	MimeType *string `json:"mimeType"`
	// The image for the media. Returns `null` until `status` is `READY`.
	Image *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage `json:"image"`
	// The original source of the image.
	OriginalSource *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource `json:"originalSource"`
}

// GetTypename returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage.Typename, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) GetTypename() *string {
	return v.Typename
}

// GetId returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage.Id, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) GetId() string {
	return v.Id
}

// GetStatus returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage.Status, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) GetStatus() MediaStatus {
	return v.Status
}

// GetMimeType returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage.MimeType, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) GetMimeType() *string {
	return v.MimeType
}

// GetImage returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage.Image, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) GetImage() *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage {
	return v.Image
}

// GetOriginalSource returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage.OriginalSource, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) GetOriginalSource() *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource {
	return v.OriginalSource
}

// findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage includes the requested fields of the GraphQL type Image.
// The GraphQL type's documentation follows.
//
// Represents an image resource.
type findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage struct {
	// The original width of the image in pixels. Returns `null` if the image isn't hosted by Shopify.
	Width *int `json:"width"`
	// The original height of the image in pixels. Returns `null` if the image isn't hosted by Shopify.
	Height *int `json:"height"`
	// The location of the image as a URL.
	//
	// If no transform options are specified, then the original image will be preserved including any pre-applied transforms.
	//
	// All transformation options are considered "best-effort". Any transformation
	// that the original image type doesn't support will be ignored.
	//
	// If you need multiple variations of the same image, then you can use [GraphQL
	// aliases](https://graphql.org/learn/queries/#aliases).
	Url string `json:"url"`
}

// GetWidth returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage.Width, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage) GetWidth() *int {
	return v.Width
}

// GetHeight returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage.Height, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage) GetHeight() *int {
	return v.Height
}

// GetUrl returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage.Url, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage) GetUrl() string {
	return v.Url
}

// findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource includes the requested fields of the GraphQL type MediaImageOriginalSource.
// The GraphQL type's documentation follows.
//
// The original source for an image.
type findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource struct {
	// The size of the original file in bytes.
	FileSize *int `json:"fileSize"`
	// The URL of the original image, valid only for a short period.
	Url *string `json:"url"`
}

// GetFileSize returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource.FileSize, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource) GetFileSize() *int {
	return v.FileSize
}

// GetUrl returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource.Url, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource) GetUrl() *string {
	return v.Url
}

// findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d includes the requested fields of the GraphQL type Model3d.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted 3D model.
type findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d.Typename, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d) GetTypename() *string {
	return v.Typename
}

// findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo includes the requested fields of the GraphQL type Video.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted video.
type findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo.Typename, and is useful for accessing the field via an interface.
func (v *findProductImagesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo) GetTypename() *string {
	return v.Typename
}

// findProductImagesResponse is returned by findProductImages on success.
type findProductImagesResponse struct {
	// Retrieves a list of [products](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product)
	// in a store. Products are the items that merchants can sell in their store.
	//
	// Use the `products` query when you need to:
	//
	// - Build a browsing interface for a product catalog.
	// - Create product
	// [searching](https://shopify.dev/docs/api/usage/search-syntax), [sorting](https://shopify.dev/docs/api/admin-graphql/latest/queries/products#arguments-sortKey), and [filtering](https://shopify.dev/docs/api/admin-graphql/latest/queries/products#arguments-query) experiences.
	// - Implement product recommendations.
	// - Sync product data with external systems.
	//
	// The `products` query supports [pagination](https://shopify.dev/docs/api/usage/pagination-graphql)
	// to handle large product catalogs and [saved searches](https://shopify.dev/docs/api/admin-graphql/latest/queries/products#arguments-savedSearchId)
	// for frequently used product queries.
	//
	// The `products` query returns products with their associated metadata, including:
	//
	// - Basic product information (for example, title, description, vendor, and type)
	// - Product options and product variants, with their prices and inventory
	// - Media attachments (for example, images and videos)
	// - SEO metadata
	// - Product categories and tags
	// - Product availability and publishing statuses
	//
	// Learn more about working with [Shopify's product model](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components).
	Products findProductImagesProductsProductConnection `json:"products"`
}

// GetProducts returns findProductImagesResponse.Products, and is useful for accessing the field via an interface.
func (v *findProductImagesResponse) GetProducts() findProductImagesProductsProductConnection {
	return v.Products
}

// getProductVarsProduct includes the requested fields of the GraphQL type Product.
// The GraphQL type's documentation follows.
//
// The `Product` object lets you manage products in a merchant’s store.
//
// Products are the goods and services that merchants offer to customers. They can
// include various details such as title, description, price, images, and options
// such as size or color.
// You can use [product variants](https://shopify.dev/docs/api/admin-graphql/latest/objects/productvariant)
// to create or update different versions of the same product.
// You can also add or update product [media](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/media).
// Products can be organized by grouping them into a [collection](https://shopify.dev/docs/api/admin-graphql/latest/objects/collection).
//
// Learn more about working with [Shopify's product model](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components),
// including limitations and considerations.
type getProductVarsProduct struct {
	// A globally-unique ID.
	Id string `json:"id"`
	// Whether the product has only a single variant with the default option and value.
	HasOnlyDefaultVariant bool `json:"hasOnlyDefaultVariant"`
	// A list of [variants](https://shopify.dev/docs/api/admin-graphql/latest/objects/ProductVariant) associated with the product.
	// If querying a single product at the root, you can fetch up to 2000 variants.
	Variants getProductVarsProductVariantsProductVariantConnection `json:"variants"`
}

// GetId returns getProductVarsProduct.Id, and is useful for accessing the field via an interface.
func (v *getProductVarsProduct) GetId() string { return v.Id }

// GetHasOnlyDefaultVariant returns getProductVarsProduct.HasOnlyDefaultVariant, and is useful for accessing the field via an interface.
func (v *getProductVarsProduct) GetHasOnlyDefaultVariant() bool { return v.HasOnlyDefaultVariant }

// GetVariants returns getProductVarsProduct.Variants, and is useful for accessing the field via an interface.
func (v *getProductVarsProduct) GetVariants() getProductVarsProductVariantsProductVariantConnection {
	return v.Variants
}

// getProductVarsProductVariantsProductVariantConnection includes the requested fields of the GraphQL type ProductVariantConnection.
// The GraphQL type's documentation follows.
//
// An auto-generated type for paginating through multiple ProductVariants.
type getProductVarsProductVariantsProductVariantConnection struct {
	// A list of nodes that are contained in ProductVariantEdge. You can fetch data
	// about an individual node, or you can follow the edges to fetch data about a
	// collection of related nodes. At each node, you specify the fields that you
	// want to retrieve.
	Nodes []getProductVarsProductVariantsProductVariantConnectionNodesProductVariant `json:"nodes"`
	// An object that’s used to retrieve [cursor
	// information](https://shopify.dev/api/usage/pagination-graphql) about the current page.
	PageInfo getProductVarsProductVariantsProductVariantConnectionPageInfo `json:"pageInfo"`
}

// GetNodes returns getProductVarsProductVariantsProductVariantConnection.Nodes, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnection) GetNodes() []getProductVarsProductVariantsProductVariantConnectionNodesProductVariant {
	return v.Nodes
}

// GetPageInfo returns getProductVarsProductVariantsProductVariantConnection.PageInfo, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnection) GetPageInfo() getProductVarsProductVariantsProductVariantConnectionPageInfo {
	return v.PageInfo
}

// getProductVarsProductVariantsProductVariantConnectionNodesProductVariant includes the requested fields of the GraphQL type ProductVariant.
// The GraphQL type's documentation follows.
//
// The `ProductVariant` object represents a version of a
// [product](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product)
// that comes in more than one [option](https://shopify.dev/docs/api/admin-graphql/latest/objects/ProductOption),
// such as size or color. For example, if a merchant sells t-shirts with options for size and color, then a small,
// blue t-shirt would be one product variant and a large, blue t-shirt would be another.
//
// Use the `ProductVariant` object to manage the full lifecycle and configuration of a product's variants. Common
// use cases for using the `ProductVariant` object include:
//
// - Tracking inventory for each variant
// - Setting unique prices for each variant
// - Assigning barcodes and SKUs to connect variants to fulfillment services
// - Attaching variant-specific images and media
// - Setting delivery and tax requirements
// - Supporting product bundles, subscriptions, and selling plans
//
// A `ProductVariant` is associated with a parent
// [`Product`](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product) object.
// `ProductVariant` serves as the central link between a product's merchandising configuration, inventory,
// pricing, fulfillment, and sales channels within the GraphQL Admin API schema. Each variant
// can reference other GraphQL types such as:
//
// - [`InventoryItem`](https://shopify.dev/docs/api/admin-graphql/latest/objects/InventoryItem): Used for inventory tracking
// - [`Image`](https://shopify.dev/docs/api/admin-graphql/latest/objects/Image): Used for variant-specific images
// - [`SellingPlanGroup`](https://shopify.dev/docs/api/admin-graphql/latest/objects/SellingPlanGroup): Used for subscriptions and selling plans
//
// Learn more about [Shopify's product model](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components).
type getProductVarsProductVariantsProductVariantConnectionNodesProductVariant struct {
	// A globally-unique ID.
	Id string `json:"id"`
	// The media associated with the product variant.
	Media getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection `json:"media"`
}

// GetId returns getProductVarsProductVariantsProductVariantConnectionNodesProductVariant.Id, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariant) GetId() string {
	return v.Id
}

// GetMedia returns getProductVarsProductVariantsProductVariantConnectionNodesProductVariant.Media, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariant) GetMedia() getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection {
	return v.Media
}

// getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection includes the requested fields of the GraphQL type MediaConnection.
// The GraphQL type's documentation follows.
//
// An auto-generated type for paginating through multiple Media.
type getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection struct {
	// A list of nodes that are contained in MediaEdge. You can fetch data about an
	// individual node, or you can follow the edges to fetch data about a collection
	// of related nodes. At each node, you specify the fields that you want to retrieve.
	Nodes []getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia `json:"-"`
}

// GetNodes returns getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection.Nodes, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection) GetNodes() []getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia {
	return v.Nodes
}

func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection
		Nodes []json.RawMessage `json:"nodes"`
		graphql.NoUnmarshalJSON
	}
	firstPass.getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Nodes
		src := firstPass.Nodes
		*dst = make(
			[]getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			if len(src) != 0 && string(src) != "null" {
				err = __unmarshalgetProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia(
					src, dst)
				if err != nil {
					return fmt.Errorf(
						"unable to unmarshal getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection.Nodes: %w", err)
				}
			}
		}
	}
	return nil
}

type __premarshalgetProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection struct {
	Nodes []json.RawMessage `json:"nodes"`
}

func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection) __premarshalJSON() (*__premarshalgetProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection, error) {
	var retval __premarshalgetProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection

	{

		dst := &retval.Nodes
		src := v.Nodes
		*dst = make(
			[]json.RawMessage,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			var err error
			*dst, err = __marshalgetProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia(
				&src)
			if err != nil {
				return nil, fmt.Errorf(
					"unable to marshal getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnection.Nodes: %w", err)
			}
		}
	}
	return &retval, nil
}

// getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesExternalVideo includes the requested fields of the GraphQL type ExternalVideo.
// The GraphQL type's documentation follows.
//
// Represents a video hosted outside of Shopify.
type getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesExternalVideo struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
}

// GetTypename returns getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesExternalVideo.Typename, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesExternalVideo) GetTypename() *string {
	return v.Typename
}

// GetId returns getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesExternalVideo.Id, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesExternalVideo) GetId() string {
	return v.Id
}

// getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia includes the requested fields of the GraphQL interface Media.
//
// getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia is implemented by the following types:
// getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesExternalVideo
// getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMediaImage
// getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesModel3d
// getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesVideo
// The GraphQL type's documentation follows.
//
// Represents a media interface.
type getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia interface {
	implementsGraphQLInterfacegetProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() *string
	// GetId returns the interface-field "id" from its implementation.
	// The GraphQL interface field's documentation follows.
	//
	// A globally-unique ID.
	GetId() string
}

func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesExternalVideo) implementsGraphQLInterfacegetProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia() {
}
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMediaImage) implementsGraphQLInterfacegetProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia() {
}
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesModel3d) implementsGraphQLInterfacegetProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia() {
}
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesVideo) implementsGraphQLInterfacegetProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia() {
}

func __unmarshalgetProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia(b []byte, v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "ExternalVideo":
		*v = new(getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesExternalVideo)
		return json.Unmarshal(b, *v)
	case "MediaImage":
		*v = new(getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMediaImage)
		return json.Unmarshal(b, *v)
	case "Model3d":
		*v = new(getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesModel3d)
		return json.Unmarshal(b, *v)
	case "Video":
		*v = new(getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesVideo)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing Media.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia: "%v"`, tn.TypeName)
	}
}

func __marshalgetProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia(v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesExternalVideo:
		typename = "ExternalVideo"

		result := struct {
			TypeName string `json:"__typename"`
			*getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesExternalVideo
		}{typename, v}
		return json.Marshal(result)
	case *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMediaImage:
		typename = "MediaImage"

		result := struct {
			TypeName string `json:"__typename"`
			*getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMediaImage
		}{typename, v}
		return json.Marshal(result)
	case *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesModel3d:
		typename = "Model3d"

		result := struct {
			TypeName string `json:"__typename"`
			*getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesModel3d
		}{typename, v}
		return json.Marshal(result)
	case *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesVideo:
		typename = "Video"

		result := struct {
			TypeName string `json:"__typename"`
			*getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesVideo
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMedia: "%T"`, v)
	}
}

// getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMediaImage includes the requested fields of the GraphQL type MediaImage.
// The GraphQL type's documentation follows.
//
// The `MediaImage` object represents an image hosted on Shopify's
// [content delivery network (CDN)](https://shopify.dev/docs/storefronts/themes/best-practices/performance/platform#shopify-cdn).
// Shopify CDN is a content system that serves as the primary way to store,
// manage, and deliver visual content for products, variants, and other resources across the Shopify platform.
//
// The `MediaImage` object provides information to:
//
// - Store and display product and variant images across online stores, admin interfaces, and mobile apps.
// - Retrieve visual branding elements, including logos, banners, favicons, and background images in checkout flows.
// - Retrieve signed URLs for secure, time-limited access to original image files.
//
// Each `MediaImage` object provides both the processed image data (with automatic optimization and CDN delivery)
// and access to the original source file. The image processing is handled asynchronously, so images
// might not be immediately available after upload. The
// [`status`](https://shopify.dev/docs/api/admin-graphql/latest/objects/mediaimage#field-MediaImage.fields.status)
// field indicates when processing is complete and the image is ready for use.
//
// The `MediaImage` object implements the [`Media`](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/Media)
// interface alongside other media types, like videos and 3D models.
//
// Learn about
// managing media for [products](https://shopify.dev/docs/apps/build/online-store/product-media),
// [product variants](https://shopify.dev/docs/apps/build/online-store/product-variant-media), and
// [asynchronous media management](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components#asynchronous-media-management).
type getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMediaImage struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
}

// GetTypename returns getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMediaImage.Typename, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMediaImage) GetTypename() *string {
	return v.Typename
}

// GetId returns getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMediaImage.Id, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesMediaImage) GetId() string {
	return v.Id
}

// getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesModel3d includes the requested fields of the GraphQL type Model3d.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted 3D model.
type getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesModel3d struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
}

// GetTypename returns getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesModel3d.Typename, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesModel3d) GetTypename() *string {
	return v.Typename
}

// GetId returns getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesModel3d.Id, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesModel3d) GetId() string {
	return v.Id
}

// getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesVideo includes the requested fields of the GraphQL type Video.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted video.
type getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesVideo struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
}

// GetTypename returns getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesVideo.Typename, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesVideo) GetTypename() *string {
	return v.Typename
}

// GetId returns getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesVideo.Id, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionNodesProductVariantMediaMediaConnectionNodesVideo) GetId() string {
	return v.Id
}

// getProductVarsProductVariantsProductVariantConnectionPageInfo includes the requested fields of the GraphQL type PageInfo.
// The GraphQL type's documentation follows.
//
// Returns information about pagination in a connection, in accordance with the
// [Relay specification](https://relay.dev/graphql/connections.htm#sec-undefined.PageInfo).
// For more information, please read our [GraphQL Pagination Usage Guide](https://shopify.dev/api/usage/pagination-graphql).
type getProductVarsProductVariantsProductVariantConnectionPageInfo struct {
	// The cursor corresponding to the last node in edges.
	EndCursor *string `json:"endCursor"`
	// Whether there are more pages to fetch following the current page.
	HasNextPage bool `json:"hasNextPage"`
}

// GetEndCursor returns getProductVarsProductVariantsProductVariantConnectionPageInfo.EndCursor, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionPageInfo) GetEndCursor() *string {
	return v.EndCursor
}

// GetHasNextPage returns getProductVarsProductVariantsProductVariantConnectionPageInfo.HasNextPage, and is useful for accessing the field via an interface.
func (v *getProductVarsProductVariantsProductVariantConnectionPageInfo) GetHasNextPage() bool {
	return v.HasNextPage
}

// getProductVarsResponse is returned by getProductVars on success.
type getProductVarsResponse struct {
	// Retrieves a [product](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product) by its ID.
	// A product is an item that a merchant can sell in their store.
	//
	// Use the `product` query when you need to:
	//
	// - Access essential product data (for example, title, description, price, images, SEO metadata, and metafields).
	// - Build product detail pages and manage inventory.
	// - Handle international sales with localized pricing and content.
	// - Manage product variants and product options.
	//
	// Learn more about working with [Shopify's product model](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components).
	Product *getProductVarsProduct `json:"product"`
}

// GetProduct returns getProductVarsResponse.Product, and is useful for accessing the field via an interface.
func (v *getProductVarsResponse) GetProduct() *getProductVarsProduct { return v.Product }

// listFilesFilesFileConnection includes the requested fields of the GraphQL type FileConnection.
// The GraphQL type's documentation follows.
//
// An auto-generated type for paginating through multiple Files.
type listFilesFilesFileConnection struct {
	// A list of nodes that are contained in FileEdge. You can fetch data about an
	// individual node, or you can follow the edges to fetch data about a collection
	// of related nodes. At each node, you specify the fields that you want to retrieve.
	Nodes []listFilesFilesFileConnectionNodesFile `json:"-"`
	// An object that’s used to retrieve [cursor
	// information](https://shopify.dev/api/usage/pagination-graphql) about the current page.
	PageInfo listFilesFilesFileConnectionPageInfo `json:"pageInfo"`
}

// GetNodes returns listFilesFilesFileConnection.Nodes, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnection) GetNodes() []listFilesFilesFileConnectionNodesFile {
	return v.Nodes
}

// GetPageInfo returns listFilesFilesFileConnection.PageInfo, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnection) GetPageInfo() listFilesFilesFileConnectionPageInfo {
	return v.PageInfo
}

func (v *listFilesFilesFileConnection) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*listFilesFilesFileConnection
		Nodes []json.RawMessage `json:"nodes"`
		graphql.NoUnmarshalJSON
	}
	firstPass.listFilesFilesFileConnection = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Nodes
		src := firstPass.Nodes
		*dst = make(
			[]listFilesFilesFileConnectionNodesFile,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			if len(src) != 0 && string(src) != "null" {
				err = __unmarshallistFilesFilesFileConnectionNodesFile(
					src, dst)
				if err != nil {
					return fmt.Errorf(
						"unable to unmarshal listFilesFilesFileConnection.Nodes: %w", err)
				}
			}
		}
	}
	return nil
}

type __premarshallistFilesFilesFileConnection struct {
	Nodes []json.RawMessage `json:"nodes"`

	PageInfo listFilesFilesFileConnectionPageInfo `json:"pageInfo"`
}

func (v *listFilesFilesFileConnection) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *listFilesFilesFileConnection) __premarshalJSON() (*__premarshallistFilesFilesFileConnection, error) {
	var retval __premarshallistFilesFilesFileConnection

	{

		dst := &retval.Nodes
		src := v.Nodes
		*dst = make(
			[]json.RawMessage,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			var err error
			*dst, err = __marshallistFilesFilesFileConnectionNodesFile(
				&src)
			if err != nil {
				return nil, fmt.Errorf(
					"unable to marshal listFilesFilesFileConnection.Nodes: %w", err)
			}
		}
	}
	retval.PageInfo = v.PageInfo
	return &retval, nil
}

// listFilesFilesFileConnectionNodesExternalVideo includes the requested fields of the GraphQL type ExternalVideo.
// The GraphQL type's documentation follows.
//
// Represents a video hosted outside of Shopify.
type listFilesFilesFileConnectionNodesExternalVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns listFilesFilesFileConnectionNodesExternalVideo.Typename, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesExternalVideo) GetTypename() *string { return v.Typename }

// listFilesFilesFileConnectionNodesFile includes the requested fields of the GraphQL interface File.
//
// listFilesFilesFileConnectionNodesFile is implemented by the following types:
// listFilesFilesFileConnectionNodesExternalVideo
// listFilesFilesFileConnectionNodesGenericFile
// listFilesFilesFileConnectionNodesMediaImage
// listFilesFilesFileConnectionNodesModel3d
// listFilesFilesFileConnectionNodesVideo
// The GraphQL type's documentation follows.
//
// A file interface.
type listFilesFilesFileConnectionNodesFile interface {
	implementsGraphQLInterfacelistFilesFilesFileConnectionNodesFile()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() *string
}

func (v *listFilesFilesFileConnectionNodesExternalVideo) implementsGraphQLInterfacelistFilesFilesFileConnectionNodesFile() {
}
func (v *listFilesFilesFileConnectionNodesGenericFile) implementsGraphQLInterfacelistFilesFilesFileConnectionNodesFile() {
}
func (v *listFilesFilesFileConnectionNodesMediaImage) implementsGraphQLInterfacelistFilesFilesFileConnectionNodesFile() {
}
func (v *listFilesFilesFileConnectionNodesModel3d) implementsGraphQLInterfacelistFilesFilesFileConnectionNodesFile() {
}
func (v *listFilesFilesFileConnectionNodesVideo) implementsGraphQLInterfacelistFilesFilesFileConnectionNodesFile() {
}

func __unmarshallistFilesFilesFileConnectionNodesFile(b []byte, v *listFilesFilesFileConnectionNodesFile) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "ExternalVideo":
		*v = new(listFilesFilesFileConnectionNodesExternalVideo)
		return json.Unmarshal(b, *v)
	case "GenericFile":
		*v = new(listFilesFilesFileConnectionNodesGenericFile)
		return json.Unmarshal(b, *v)
	case "MediaImage":
		*v = new(listFilesFilesFileConnectionNodesMediaImage)
		return json.Unmarshal(b, *v)
	case "Model3d":
		*v = new(listFilesFilesFileConnectionNodesModel3d)
		return json.Unmarshal(b, *v)
	case "Video":
		*v = new(listFilesFilesFileConnectionNodesVideo)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing File.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for listFilesFilesFileConnectionNodesFile: "%v"`, tn.TypeName)
	}
}

func __marshallistFilesFilesFileConnectionNodesFile(v *listFilesFilesFileConnectionNodesFile) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *listFilesFilesFileConnectionNodesExternalVideo:
		typename = "ExternalVideo"

		result := struct {
			TypeName string `json:"__typename"`
			*listFilesFilesFileConnectionNodesExternalVideo
		}{typename, v}
		return json.Marshal(result)
	case *listFilesFilesFileConnectionNodesGenericFile:
		typename = "GenericFile"

		result := struct {
			TypeName string `json:"__typename"`
			*listFilesFilesFileConnectionNodesGenericFile
		}{typename, v}
		return json.Marshal(result)
	case *listFilesFilesFileConnectionNodesMediaImage:
		typename = "MediaImage"

		result := struct {
			TypeName string `json:"__typename"`
			*listFilesFilesFileConnectionNodesMediaImage
		}{typename, v}
		return json.Marshal(result)
	case *listFilesFilesFileConnectionNodesModel3d:
		typename = "Model3d"

		result := struct {
			TypeName string `json:"__typename"`
			*listFilesFilesFileConnectionNodesModel3d
		}{typename, v}
		return json.Marshal(result)
	case *listFilesFilesFileConnectionNodesVideo:
		typename = "Video"

		result := struct {
			TypeName string `json:"__typename"`
			*listFilesFilesFileConnectionNodesVideo
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for listFilesFilesFileConnectionNodesFile: "%T"`, v)
	}
}

// listFilesFilesFileConnectionNodesGenericFile includes the requested fields of the GraphQL type GenericFile.
// The GraphQL type's documentation follows.
//
// Represents any file other than HTML.
type listFilesFilesFileConnectionNodesGenericFile struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns listFilesFilesFileConnectionNodesGenericFile.Typename, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesGenericFile) GetTypename() *string { return v.Typename }

// listFilesFilesFileConnectionNodesMediaImage includes the requested fields of the GraphQL type MediaImage.
// The GraphQL type's documentation follows.
//
// The `MediaImage` object represents an image hosted on Shopify's
// [content delivery network (CDN)](https://shopify.dev/docs/storefronts/themes/best-practices/performance/platform#shopify-cdn).
// Shopify CDN is a content system that serves as the primary way to store,
// manage, and deliver visual content for products, variants, and other resources across the Shopify platform.
//
// The `MediaImage` object provides information to:
//
// - Store and display product and variant images across online stores, admin interfaces, and mobile apps.
// - Retrieve visual branding elements, including logos, banners, favicons, and background images in checkout flows.
// - Retrieve signed URLs for secure, time-limited access to original image files.
//
// Each `MediaImage` object provides both the processed image data (with automatic optimization and CDN delivery)
// and access to the original source file. The image processing is handled asynchronously, so images
// might not be immediately available after upload. The
// [`status`](https://shopify.dev/docs/api/admin-graphql/latest/objects/mediaimage#field-MediaImage.fields.status)
// field indicates when processing is complete and the image is ready for use.
//
// The `MediaImage` object implements the [`Media`](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/Media)
// interface alongside other media types, like videos and 3D models.
//
// Learn about
// managing media for [products](https://shopify.dev/docs/apps/build/online-store/product-media),
// [product variants](https://shopify.dev/docs/apps/build/online-store/product-variant-media), and
// [asynchronous media management](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components#asynchronous-media-management).
type listFilesFilesFileConnectionNodesMediaImage struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// The MIME type of the image.
	MimeType *string `json:"mimeType"`
	// Current status of the media.
	Status MediaStatus `json:"status"`
	// The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601)) when the file was last updated.
	UpdatedAt time.Time `json:"updatedAt"`
	// The image for the media. Returns `null` until `status` is `READY`.
	Image *listFilesFilesFileConnectionNodesMediaImageImage `json:"image"`
	// The original source of the image.
	OriginalSource *listFilesFilesFileConnectionNodesMediaImageOriginalSource `json:"originalSource"`
}

// GetTypename returns listFilesFilesFileConnectionNodesMediaImage.Typename, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesMediaImage) GetTypename() *string { return v.Typename }

// GetId returns listFilesFilesFileConnectionNodesMediaImage.Id, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesMediaImage) GetId() string { return v.Id }

// GetMimeType returns listFilesFilesFileConnectionNodesMediaImage.MimeType, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesMediaImage) GetMimeType() *string { return v.MimeType }

// GetStatus returns listFilesFilesFileConnectionNodesMediaImage.Status, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesMediaImage) GetStatus() MediaStatus { return v.Status }

// GetUpdatedAt returns listFilesFilesFileConnectionNodesMediaImage.UpdatedAt, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesMediaImage) GetUpdatedAt() time.Time { return v.UpdatedAt }

// GetImage returns listFilesFilesFileConnectionNodesMediaImage.Image, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesMediaImage) GetImage() *listFilesFilesFileConnectionNodesMediaImageImage {
	return v.Image
}

// GetOriginalSource returns listFilesFilesFileConnectionNodesMediaImage.OriginalSource, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesMediaImage) GetOriginalSource() *listFilesFilesFileConnectionNodesMediaImageOriginalSource {
	return v.OriginalSource
}

// listFilesFilesFileConnectionNodesMediaImageImage includes the requested fields of the GraphQL type Image.
// The GraphQL type's documentation follows.
//
// Represents an image resource.
type listFilesFilesFileConnectionNodesMediaImageImage struct {
	// The location of the image as a URL.
	//
	// If no transform options are specified, then the original image will be preserved including any pre-applied transforms.
	//
	// All transformation options are considered "best-effort". Any transformation
	// that the original image type doesn't support will be ignored.
	//
	// If you need multiple variations of the same image, then you can use [GraphQL
	// aliases](https://graphql.org/learn/queries/#aliases).
	Url string `json:"url"`
}

// GetUrl returns listFilesFilesFileConnectionNodesMediaImageImage.Url, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesMediaImageImage) GetUrl() string { return v.Url }

// listFilesFilesFileConnectionNodesMediaImageOriginalSource includes the requested fields of the GraphQL type MediaImageOriginalSource.
// The GraphQL type's documentation follows.
//
// The original source for an image.
type listFilesFilesFileConnectionNodesMediaImageOriginalSource struct {
	// The size of the original file in bytes.
	FileSize *int `json:"fileSize"`
}

// GetFileSize returns listFilesFilesFileConnectionNodesMediaImageOriginalSource.FileSize, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesMediaImageOriginalSource) GetFileSize() *int {
	return v.FileSize
}

// listFilesFilesFileConnectionNodesModel3d includes the requested fields of the GraphQL type Model3d.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted 3D model.
type listFilesFilesFileConnectionNodesModel3d struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns listFilesFilesFileConnectionNodesModel3d.Typename, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesModel3d) GetTypename() *string { return v.Typename }

// listFilesFilesFileConnectionNodesVideo includes the requested fields of the GraphQL type Video.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted video.
type listFilesFilesFileConnectionNodesVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns listFilesFilesFileConnectionNodesVideo.Typename, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionNodesVideo) GetTypename() *string { return v.Typename }

// listFilesFilesFileConnectionPageInfo includes the requested fields of the GraphQL type PageInfo.
// The GraphQL type's documentation follows.
//
// Returns information about pagination in a connection, in accordance with the
// [Relay specification](https://relay.dev/graphql/connections.htm#sec-undefined.PageInfo).
// For more information, please read our [GraphQL Pagination Usage Guide](https://shopify.dev/api/usage/pagination-graphql).
type listFilesFilesFileConnectionPageInfo struct {
	// Whether there are more pages to fetch following the current page.
	HasNextPage bool `json:"hasNextPage"`
	// The cursor corresponding to the last node in edges.
	EndCursor *string `json:"endCursor"`
}

// GetHasNextPage returns listFilesFilesFileConnectionPageInfo.HasNextPage, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionPageInfo) GetHasNextPage() bool { return v.HasNextPage }

// GetEndCursor returns listFilesFilesFileConnectionPageInfo.EndCursor, and is useful for accessing the field via an interface.
func (v *listFilesFilesFileConnectionPageInfo) GetEndCursor() *string { return v.EndCursor }

// listFilesResponse is returned by listFiles on success.
type listFilesResponse struct {
	// Retrieves a paginated list of files that have been uploaded to a Shopify store. Files represent digital assets
	// that merchants can upload to their store for various purposes including product images, marketing materials,
	// documents, and brand assets.
	//
	// Use the `files` query to retrieve information associated with the following workflows:
	//
	// - [Managing product media and images](https://shopify.dev/docs/apps/build/online-store/product-media)
	// - [Theme development and asset management](https://shopify.dev/docs/storefronts/themes/store/success/brand-assets)
	// - Brand asset management and [checkout branding](https://shopify.dev/docs/apps/build/checkout/styling/add-favicon)
	//
	// Files can include multiple [content types](https://shopify.dev/docs/api/admin-graphql/latest/enums/FileContentType),
	// such as images, videos, 3D models, and generic files. Each file has
	// properties like dimensions, file size, alt text for accessibility, and upload status. Files can be filtered
	// by [media type](https://shopify.dev/docs/api/admin-graphql/latest/enums/MediaContentType) and can be associated with
	// [products](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product),
	// [themes](https://shopify.dev/docs/api/admin-graphql/latest/objects/OnlineStoreTheme),
	// and other store resources.
	Files listFilesFilesFileConnection `json:"files"`
}

// GetFiles returns listFilesResponse.Files, and is useful for accessing the field via an interface.
func (v *listFilesResponse) GetFiles() listFilesFilesFileConnection { return v.Files }

// listProductFilesProductsProductConnection includes the requested fields of the GraphQL type ProductConnection.
// The GraphQL type's documentation follows.
//
// An auto-generated type for paginating through multiple Products.
type listProductFilesProductsProductConnection struct {
	// A list of nodes that are contained in ProductEdge. You can fetch data about an
	// individual node, or you can follow the edges to fetch data about a collection
	// of related nodes. At each node, you specify the fields that you want to retrieve.
	Nodes []listProductFilesProductsProductConnectionNodesProduct `json:"nodes"`
	// An object that’s used to retrieve [cursor
	// information](https://shopify.dev/api/usage/pagination-graphql) about the current page.
	PageInfo listProductFilesProductsProductConnectionPageInfo `json:"pageInfo"`
}

// GetNodes returns listProductFilesProductsProductConnection.Nodes, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnection) GetNodes() []listProductFilesProductsProductConnectionNodesProduct {
	return v.Nodes
}

// GetPageInfo returns listProductFilesProductsProductConnection.PageInfo, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnection) GetPageInfo() listProductFilesProductsProductConnectionPageInfo {
	return v.PageInfo
}

// listProductFilesProductsProductConnectionNodesProduct includes the requested fields of the GraphQL type Product.
// The GraphQL type's documentation follows.
//
// The `Product` object lets you manage products in a merchant’s store.
//
// Products are the goods and services that merchants offer to customers. They can
// include various details such as title, description, price, images, and options
// such as size or color.
// You can use [product variants](https://shopify.dev/docs/api/admin-graphql/latest/objects/productvariant)
// to create or update different versions of the same product.
// You can also add or update product [media](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/media).
// Products can be organized by grouping them into a [collection](https://shopify.dev/docs/api/admin-graphql/latest/objects/collection).
//
// Learn more about working with [Shopify's product model](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components),
// including limitations and considerations.
type listProductFilesProductsProductConnectionNodesProduct struct {
	// A globally-unique ID.
	Id string `json:"id"`
	// The name for the product that displays to customers. The title is used to construct the product's handle.
	// For example, if a product is titled "Black Sunglasses", then the handle is `black-sunglasses`.
	Title string `json:"title"`
	// Whether the product has only a single variant with the default option and value.
	HasOnlyDefaultVariant bool `json:"hasOnlyDefaultVariant"`
	// The date and time when the product was last modified.
	// A product's `updatedAt` value can change for different reasons. For example, if an order
	// is placed for a product that has inventory tracking set up, then the inventory adjustment
	// is counted as an update.
	UpdatedAt time.Time `json:"updatedAt"`
	// The [media](https://shopify.dev/docs/apps/build/online-store/product-media)
	// associated with the product. Valid media are images, 3D models, videos.
	Media listProductFilesProductsProductConnectionNodesProductMediaMediaConnection `json:"media"`
}

// GetId returns listProductFilesProductsProductConnectionNodesProduct.Id, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProduct) GetId() string { return v.Id }

// GetTitle returns listProductFilesProductsProductConnectionNodesProduct.Title, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProduct) GetTitle() string { return v.Title }

// GetHasOnlyDefaultVariant returns listProductFilesProductsProductConnectionNodesProduct.HasOnlyDefaultVariant, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProduct) GetHasOnlyDefaultVariant() bool {
	return v.HasOnlyDefaultVariant
}

// GetUpdatedAt returns listProductFilesProductsProductConnectionNodesProduct.UpdatedAt, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProduct) GetUpdatedAt() time.Time {
	return v.UpdatedAt
}

// GetMedia returns listProductFilesProductsProductConnectionNodesProduct.Media, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProduct) GetMedia() listProductFilesProductsProductConnectionNodesProductMediaMediaConnection {
	return v.Media
}

// listProductFilesProductsProductConnectionNodesProductMediaMediaConnection includes the requested fields of the GraphQL type MediaConnection.
// The GraphQL type's documentation follows.
//
// An auto-generated type for paginating through multiple Media.
type listProductFilesProductsProductConnectionNodesProductMediaMediaConnection struct {
	// A list of nodes that are contained in MediaEdge. You can fetch data about an
	// individual node, or you can follow the edges to fetch data about a collection
	// of related nodes. At each node, you specify the fields that you want to retrieve.
	Nodes []listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia `json:"-"`
}

// GetNodes returns listProductFilesProductsProductConnectionNodesProductMediaMediaConnection.Nodes, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnection) GetNodes() []listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia {
	return v.Nodes
}

func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnection) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*listProductFilesProductsProductConnectionNodesProductMediaMediaConnection
		Nodes []json.RawMessage `json:"nodes"`
		graphql.NoUnmarshalJSON
	}
	firstPass.listProductFilesProductsProductConnectionNodesProductMediaMediaConnection = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Nodes
		src := firstPass.Nodes
		*dst = make(
			[]listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			if len(src) != 0 && string(src) != "null" {
				err = __unmarshallistProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia(
					src, dst)
				if err != nil {
					return fmt.Errorf(
						"unable to unmarshal listProductFilesProductsProductConnectionNodesProductMediaMediaConnection.Nodes: %w", err)
				}
			}
		}
	}
	return nil
}

type __premarshallistProductFilesProductsProductConnectionNodesProductMediaMediaConnection struct {
	Nodes []json.RawMessage `json:"nodes"`
}

func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnection) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnection) __premarshalJSON() (*__premarshallistProductFilesProductsProductConnectionNodesProductMediaMediaConnection, error) {
	var retval __premarshallistProductFilesProductsProductConnectionNodesProductMediaMediaConnection

	{

		dst := &retval.Nodes
		src := v.Nodes
		*dst = make(
			[]json.RawMessage,
			len(src))
		for i, src := range src {
			dst := &(*dst)[i]
			var err error
			*dst, err = __marshallistProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia(
				&src)
			if err != nil {
				return nil, fmt.Errorf(
					"unable to marshal listProductFilesProductsProductConnectionNodesProductMediaMediaConnection.Nodes: %w", err)
			}
		}
	}
	return &retval, nil
}

// listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo includes the requested fields of the GraphQL type ExternalVideo.
// The GraphQL type's documentation follows.
//
// Represents a video hosted outside of Shopify.
type listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo.Typename, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo) GetTypename() *string {
	return v.Typename
}

// listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia includes the requested fields of the GraphQL interface Media.
//
// listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia is implemented by the following types:
// listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo
// listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage
// listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d
// listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo
// The GraphQL type's documentation follows.
//
// Represents a media interface.
type listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia interface {
	implementsGraphQLInterfacelistProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() *string
}

func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo) implementsGraphQLInterfacelistProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia() {
}
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) implementsGraphQLInterfacelistProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia() {
}
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d) implementsGraphQLInterfacelistProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia() {
}
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo) implementsGraphQLInterfacelistProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia() {
}

func __unmarshallistProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia(b []byte, v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "ExternalVideo":
		*v = new(listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo)
		return json.Unmarshal(b, *v)
	case "MediaImage":
		*v = new(listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage)
		return json.Unmarshal(b, *v)
	case "Model3d":
		*v = new(listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d)
		return json.Unmarshal(b, *v)
	case "Video":
		*v = new(listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing Media.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia: "%v"`, tn.TypeName)
	}
}

func __marshallistProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia(v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo:
		typename = "ExternalVideo"

		result := struct {
			TypeName string `json:"__typename"`
			*listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesExternalVideo
		}{typename, v}
		return json.Marshal(result)
	case *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage:
		typename = "MediaImage"

		result := struct {
			TypeName string `json:"__typename"`
			*listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage
		}{typename, v}
		return json.Marshal(result)
	case *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d:
		typename = "Model3d"

		result := struct {
			TypeName string `json:"__typename"`
			*listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d
		}{typename, v}
		return json.Marshal(result)
	case *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo:
		typename = "Video"

		result := struct {
			TypeName string `json:"__typename"`
			*listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMedia: "%T"`, v)
	}
}

// listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage includes the requested fields of the GraphQL type MediaImage.
// The GraphQL type's documentation follows.
//
// The `MediaImage` object represents an image hosted on Shopify's
// [content delivery network (CDN)](https://shopify.dev/docs/storefronts/themes/best-practices/performance/platform#shopify-cdn).
// Shopify CDN is a content system that serves as the primary way to store,
// manage, and deliver visual content for products, variants, and other resources across the Shopify platform.
//
// The `MediaImage` object provides information to:
//
// - Store and display product and variant images across online stores, admin interfaces, and mobile apps.
// - Retrieve visual branding elements, including logos, banners, favicons, and background images in checkout flows.
// - Retrieve signed URLs for secure, time-limited access to original image files.
//
// Each `MediaImage` object provides both the processed image data (with automatic optimization and CDN delivery)
// and access to the original source file. The image processing is handled asynchronously, so images
// might not be immediately available after upload. The
// [`status`](https://shopify.dev/docs/api/admin-graphql/latest/objects/mediaimage#field-MediaImage.fields.status)
// field indicates when processing is complete and the image is ready for use.
//
// The `MediaImage` object implements the [`Media`](https://shopify.dev/docs/api/admin-graphql/latest/interfaces/Media)
// interface alongside other media types, like videos and 3D models.
//
// Learn about
// managing media for [products](https://shopify.dev/docs/apps/build/online-store/product-media),
// [product variants](https://shopify.dev/docs/apps/build/online-store/product-variant-media), and
// [asynchronous media management](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components#asynchronous-media-management).
type listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage struct {
	Typename *string `json:"__typename"`
	// A globally-unique ID.
	Id string `json:"id"`
	// Current status of the media.
	Status MediaStatus `json:"status"`
	// The image for the media. Returns `null` until `status` is `READY`.
	Image *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage `json:"image"`
	// The original source of the image.
	OriginalSource *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource `json:"originalSource"`
}

// GetTypename returns listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage.Typename, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) GetTypename() *string {
	return v.Typename
}

// GetId returns listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage.Id, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) GetId() string {
	return v.Id
}

// GetStatus returns listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage.Status, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) GetStatus() MediaStatus {
	return v.Status
}

// GetImage returns listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage.Image, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) GetImage() *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage {
	return v.Image
}

// GetOriginalSource returns listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage.OriginalSource, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImage) GetOriginalSource() *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource {
	return v.OriginalSource
}

// listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage includes the requested fields of the GraphQL type Image.
// The GraphQL type's documentation follows.
//
// Represents an image resource.
type listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage struct {
	// The location of the image as a URL.
	//
	// If no transform options are specified, then the original image will be preserved including any pre-applied transforms.
	//
	// All transformation options are considered "best-effort". Any transformation
	// that the original image type doesn't support will be ignored.
	//
	// If you need multiple variations of the same image, then you can use [GraphQL
	// aliases](https://graphql.org/learn/queries/#aliases).
	Url string `json:"url"`
}

// GetUrl returns listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage.Url, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageImage) GetUrl() string {
	return v.Url
}

// listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource includes the requested fields of the GraphQL type MediaImageOriginalSource.
// The GraphQL type's documentation follows.
//
// The original source for an image.
type listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource struct {
	// The size of the original file in bytes.
	FileSize *int `json:"fileSize"`
}

// GetFileSize returns listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource.FileSize, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesMediaImageOriginalSource) GetFileSize() *int {
	return v.FileSize
}

// listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d includes the requested fields of the GraphQL type Model3d.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted 3D model.
type listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d.Typename, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesModel3d) GetTypename() *string {
	return v.Typename
}

// listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo includes the requested fields of the GraphQL type Video.
// The GraphQL type's documentation follows.
//
// Represents a Shopify hosted video.
type listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo struct {
	Typename *string `json:"__typename"`
}

// GetTypename returns listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo.Typename, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionNodesProductMediaMediaConnectionNodesVideo) GetTypename() *string {
	return v.Typename
}

// listProductFilesProductsProductConnectionPageInfo includes the requested fields of the GraphQL type PageInfo.
// The GraphQL type's documentation follows.
//
// Returns information about pagination in a connection, in accordance with the
// [Relay specification](https://relay.dev/graphql/connections.htm#sec-undefined.PageInfo).
// For more information, please read our [GraphQL Pagination Usage Guide](https://shopify.dev/api/usage/pagination-graphql).
type listProductFilesProductsProductConnectionPageInfo struct {
	// Whether there are more pages to fetch following the current page.
	HasNextPage bool `json:"hasNextPage"`
	// The cursor corresponding to the last node in edges.
	EndCursor *string `json:"endCursor"`
}

// GetHasNextPage returns listProductFilesProductsProductConnectionPageInfo.HasNextPage, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionPageInfo) GetHasNextPage() bool {
	return v.HasNextPage
}

// GetEndCursor returns listProductFilesProductsProductConnectionPageInfo.EndCursor, and is useful for accessing the field via an interface.
func (v *listProductFilesProductsProductConnectionPageInfo) GetEndCursor() *string {
	return v.EndCursor
}

// listProductFilesResponse is returned by listProductFiles on success.
type listProductFilesResponse struct {
	// Retrieves a list of [products](https://shopify.dev/docs/api/admin-graphql/latest/objects/Product)
	// in a store. Products are the items that merchants can sell in their store.
	//
	// Use the `products` query when you need to:
	//
	// - Build a browsing interface for a product catalog.
	// - Create product
	// [searching](https://shopify.dev/docs/api/usage/search-syntax), [sorting](https://shopify.dev/docs/api/admin-graphql/latest/queries/products#arguments-sortKey), and [filtering](https://shopify.dev/docs/api/admin-graphql/latest/queries/products#arguments-query) experiences.
	// - Implement product recommendations.
	// - Sync product data with external systems.
	//
	// The `products` query supports [pagination](https://shopify.dev/docs/api/usage/pagination-graphql)
	// to handle large product catalogs and [saved searches](https://shopify.dev/docs/api/admin-graphql/latest/queries/products#arguments-savedSearchId)
	// for frequently used product queries.
	//
	// The `products` query returns products with their associated metadata, including:
	//
	// - Basic product information (for example, title, description, vendor, and type)
	// - Product options and product variants, with their prices and inventory
	// - Media attachments (for example, images and videos)
	// - SEO metadata
	// - Product categories and tags
	// - Product availability and publishing statuses
	//
	// Learn more about working with [Shopify's product model](https://shopify.dev/docs/apps/build/graphql/migrate/new-product-model/product-model-components).
	Products listProductFilesProductsProductConnection `json:"products"`
}

// GetProducts returns listProductFilesResponse.Products, and is useful for accessing the field via an interface.
func (v *listProductFilesResponse) GetProducts() listProductFilesProductsProductConnection {
	return v.Products
}

// relinkProductAttachProductVariantAppendMediaPayload includes the requested fields of the GraphQL type ProductVariantAppendMediaPayload.
// The GraphQL type's documentation follows.
//
// Return type for `productVariantAppendMedia` mutation.
type relinkProductAttachProductVariantAppendMediaPayload struct {
	// The list of errors that occurred from executing the mutation.
	UserErrors []relinkProductAttachProductVariantAppendMediaPayloadUserErrorsMediaUserError `json:"userErrors"`
}

// GetUserErrors returns relinkProductAttachProductVariantAppendMediaPayload.UserErrors, and is useful for accessing the field via an interface.
func (v *relinkProductAttachProductVariantAppendMediaPayload) GetUserErrors() []relinkProductAttachProductVariantAppendMediaPayloadUserErrorsMediaUserError {
	return v.UserErrors
}

// relinkProductAttachProductVariantAppendMediaPayloadUserErrorsMediaUserError includes the requested fields of the GraphQL type MediaUserError.
// The GraphQL type's documentation follows.
//
// Represents an error that happens during execution of a Media query or mutation.
type relinkProductAttachProductVariantAppendMediaPayloadUserErrorsMediaUserError struct {
	// The error message.
	Message string `json:"message"`
}

// GetMessage returns relinkProductAttachProductVariantAppendMediaPayloadUserErrorsMediaUserError.Message, and is useful for accessing the field via an interface.
func (v *relinkProductAttachProductVariantAppendMediaPayloadUserErrorsMediaUserError) GetMessage() string {
	return v.Message
}

// relinkProductLinkFilesFileUpdatePayload includes the requested fields of the GraphQL type FileUpdatePayload.
// The GraphQL type's documentation follows.
//
// Return type for `fileUpdate` mutation.
type relinkProductLinkFilesFileUpdatePayload struct {
	// The list of errors that occurred from executing the mutation.
	UserErrors []relinkProductLinkFilesFileUpdatePayloadUserErrorsFilesUserError `json:"userErrors"`
}

// GetUserErrors returns relinkProductLinkFilesFileUpdatePayload.UserErrors, and is useful for accessing the field via an interface.
func (v *relinkProductLinkFilesFileUpdatePayload) GetUserErrors() []relinkProductLinkFilesFileUpdatePayloadUserErrorsFilesUserError {
	return v.UserErrors
}

// relinkProductLinkFilesFileUpdatePayloadUserErrorsFilesUserError includes the requested fields of the GraphQL type FilesUserError.
// The GraphQL type's documentation follows.
//
// An error that happens during the execution of a Files API query or mutation.
type relinkProductLinkFilesFileUpdatePayloadUserErrorsFilesUserError struct {
	// The error message.
	Message string `json:"message"`
}

// GetMessage returns relinkProductLinkFilesFileUpdatePayloadUserErrorsFilesUserError.Message, and is useful for accessing the field via an interface.
func (v *relinkProductLinkFilesFileUpdatePayloadUserErrorsFilesUserError) GetMessage() string {
	return v.Message
}

// relinkProductProductReorderMediaProductReorderMediaPayload includes the requested fields of the GraphQL type ProductReorderMediaPayload.
// The GraphQL type's documentation follows.
//
// Return type for `productReorderMedia` mutation.
type relinkProductProductReorderMediaProductReorderMediaPayload struct {
	// The list of errors that occurred from executing the mutation.
	MediaUserErrors []relinkProductProductReorderMediaProductReorderMediaPayloadMediaUserErrorsMediaUserError `json:"mediaUserErrors"`
}

// GetMediaUserErrors returns relinkProductProductReorderMediaProductReorderMediaPayload.MediaUserErrors, and is useful for accessing the field via an interface.
func (v *relinkProductProductReorderMediaProductReorderMediaPayload) GetMediaUserErrors() []relinkProductProductReorderMediaProductReorderMediaPayloadMediaUserErrorsMediaUserError {
	return v.MediaUserErrors
}

// relinkProductProductReorderMediaProductReorderMediaPayloadMediaUserErrorsMediaUserError includes the requested fields of the GraphQL type MediaUserError.
// The GraphQL type's documentation follows.
//
// Represents an error that happens during execution of a Media query or mutation.
type relinkProductProductReorderMediaProductReorderMediaPayloadMediaUserErrorsMediaUserError struct {
	// The error message.
	Message string `json:"message"`
}

// GetMessage returns relinkProductProductReorderMediaProductReorderMediaPayloadMediaUserErrorsMediaUserError.Message, and is useful for accessing the field via an interface.
func (v *relinkProductProductReorderMediaProductReorderMediaPayloadMediaUserErrorsMediaUserError) GetMessage() string {
	return v.Message
}

// relinkProductResponse is returned by relinkProduct on success.
type relinkProductResponse struct {
	// Updates properties, content, and metadata associated with an existing file
	// asset that has already been uploaded to Shopify.
	//
	// Use the `fileUpdate` mutation to modify various aspects of files already stored in your store.
	// Files can be updated individually or in batches.
	//
	// The `fileUpdate` mutation supports updating multiple file properties:
	//
	// - **Alt text**: Update accessibility descriptions for images and other media.
	// - **File content**: Replace image or generic file content while maintaining the same URL.
	// - **Filename**: Modify file names (extension must match the original).
	// - **Product references**: Add or remove associations between files and products. Removing file-product associations
	// deletes the file from the product's media gallery and clears the image from any product variants that were using it.
	//
	// The mutation handles different file types with specific capabilities:
	//
	// - **Images**: Update preview images, original source, filename, and alt text.
	// - **Generic files**: Update original source, filename, and alt text.
	// - **Videos and 3D models**: Update alt text and product references.
	//
	// > Note:
	// > Files must be in `ready` state before they can be updated. The mutation includes file locking to prevent
	// > conflicts during updates. You can't simultaneously update both `originalSource` and `previewImageSource`.
	//
	// After updating files, you can use related mutations for additional file management:
	//
	// - [`fileCreate`](https://shopify.dev/docs/api/admin-graphql/latest/mutations/fileCreate):
	// Create new file assets from external URLs or staged uploads.
	// - [`fileDelete`](https://shopify.dev/docs/api/admin-graphql/latest/mutations/fileDelete):
	// Remove files from your store when they are no longer needed.
	//
	// Learn how to manage
	// [product media and file assets](https://shopify.dev/docs/apps/build/online-store/product-media)
	// in your app.
	LinkFiles *relinkProductLinkFilesFileUpdatePayload `json:"linkFiles"`
	// Asynchronously reorders the media attached to a product.
	ProductReorderMedia *relinkProductProductReorderMediaProductReorderMediaPayload `json:"productReorderMedia"`
	// Appends media from a product to variants of the product.
	Attach *relinkProductAttachProductVariantAppendMediaPayload `json:"attach"`
}

// GetLinkFiles returns relinkProductResponse.LinkFiles, and is useful for accessing the field via an interface.
func (v *relinkProductResponse) GetLinkFiles() *relinkProductLinkFilesFileUpdatePayload {
	return v.LinkFiles
}

// GetProductReorderMedia returns relinkProductResponse.ProductReorderMedia, and is useful for accessing the field via an interface.
func (v *relinkProductResponse) GetProductReorderMedia() *relinkProductProductReorderMediaProductReorderMediaPayload {
	return v.ProductReorderMedia
}

// GetAttach returns relinkProductResponse.Attach, and is useful for accessing the field via an interface.
func (v *relinkProductResponse) GetAttach() *relinkProductAttachProductVariantAppendMediaPayload {
	return v.Attach
}

// The query executed by callGetFileByNameFirst.
const callGetFileByNameFirst_Operation = `
query callGetFileByNameFirst ($query: String!) {
	files(first: 50, query: $query, sortKey: FILENAME) {
		nodes {
			__typename
			... on MediaImage {
				id
				image {
					url
				}
			}
		}
		pageInfo {
			endCursor
		}
	}
}
`

func callGetFileByNameFirst(
	ctx_ context.Context,
	client_ graphql.Client,
	query string,
) (data_ *callGetFileByNameFirstResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "callGetFileByNameFirst",
		Query:  callGetFileByNameFirst_Operation,
		Variables: &__callGetFileByNameFirstInput{
			Query: query,
		},
	}

	data_ = &callGetFileByNameFirstResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by callGetFileByNameNext.
const callGetFileByNameNext_Operation = `
query callGetFileByNameNext ($query: String!, $cursor: String) {
	files(first: 50, query: $query, sortKey: FILENAME, after: $cursor) {
		nodes {
			__typename
			... on MediaImage {
				id
				image {
					url
				}
			}
		}
		pageInfo {
			endCursor
		}
	}
}
`

func callGetFileByNameNext(
	ctx_ context.Context,
	client_ graphql.Client,
	query string,
	cursor *string,
) (data_ *callGetFileByNameNextResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "callGetFileByNameNext",
		Query:  callGetFileByNameNext_Operation,
		Variables: &__callGetFileByNameNextInput{
			Query:  query,
			Cursor: cursor,
		},
	}

	data_ = &callGetFileByNameNextResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by fileArchive.
const fileArchive_Operation = `
mutation fileArchive ($files: [FileUpdateInput!]!) {
	fileUpdate(files: $files) {
		files {
			__typename
			id
			alt
			fileStatus
		}
		userErrors {
			message
		}
	}
}
`

// ------------------------------------------------
func fileArchive(
	ctx_ context.Context,
	client_ graphql.Client,
	files []FileUpdateInput,
) (data_ *fileArchiveResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "fileArchive",
		Query:  fileArchive_Operation,
		Variables: &__fileArchiveInput{
			Files: files,
		},
	}

	data_ = &fileArchiveResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by fileDelete.
const fileDelete_Operation = `
mutation fileDelete ($fileIds: [ID!]!) {
	fileDelete(fileIds: $fileIds) {
		deletedFileIds
	}
}
`

// ------------------------------------------------
func fileDelete(
	ctx_ context.Context,
	client_ graphql.Client,
	fileIds []string,
) (data_ *fileDeleteResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "fileDelete",
		Query:  fileDelete_Operation,
		Variables: &__fileDeleteInput{
			FileIds: fileIds,
		},
	}

	data_ = &fileDeleteResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by findImageById.
const findImageById_Operation = `
query findImageById ($id: ID!) {
	node(id: $id) {
		__typename
		... on MediaImage {
			id
			status
			mimeType
			image {
				width
				height
				url
				thumbhash
			}
			originalSource {
				url
				fileSize
			}
		}
	}
}
`

func findImageById(
	ctx_ context.Context,
	client_ graphql.Client,
	id string,
) (data_ *findImageByIdResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "findImageById",
		Query:  findImageById_Operation,
		Variables: &__findImageByIdInput{
			Id: id,
		},
	}

	data_ = &findImageByIdResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by findImages.
const findImages_Operation = `
query findImages ($query: String!, $sortKey: FileSortKeys!, $limit: Int!, $cursor: String) {
	files(first: $limit, query: $query, sortKey: $sortKey, after: $cursor) {
		nodes {
			__typename
			... on MediaImage {
				id
				status
				mimeType
				image {
					width
					height
					url
					thumbhash
				}
				originalSource {
					fileSize
					url
				}
			}
		}
		pageInfo {
			endCursor
		}
	}
}
`

// ------------------------------------------------
func findImages(
	ctx_ context.Context,
	client_ graphql.Client,
	query string,
	sortKey FileSortKeys,
	limit int,
	cursor *string,
) (data_ *findImagesResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "findImages",
		Query:  findImages_Operation,
		Variables: &__findImagesInput{
			Query:   query,
			SortKey: sortKey,
			Limit:   limit,
			Cursor:  cursor,
		},
	}

	data_ = &findImagesResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by findMasterImage.
const findMasterImage_Operation = `
query findMasterImage ($query: String!, $cursor: String) {
	files(first: 250, query: $query, sortKey: FILENAME, after: $cursor) {
		nodes {
			__typename
			... on MediaImage {
				id
				status
				image {
					url
				}
			}
		}
		pageInfo {
			endCursor
			hasNextPage
		}
	}
}
`

// ------------------------------------------------
func findMasterImage(
	ctx_ context.Context,
	client_ graphql.Client,
	query string,
	cursor *string,
) (data_ *findMasterImageResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "findMasterImage",
		Query:  findMasterImage_Operation,
		Variables: &__findMasterImageInput{
			Query:  query,
			Cursor: cursor,
		},
	}

	data_ = &findMasterImageResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by findProductImages.
const findProductImages_Operation = `
query findProductImages ($query: String!, $limit: Int!) {
	products(query: $query, first: $limit, sortKey: ID, reverse: false) {
		nodes {
			id
			title
			hasOnlyDefaultVariant
			media(first: 250, query: "media_type:IMAGE") {
				nodes {
					__typename
					... on MediaImage {
						id
						status
						mimeType
						image {
							width
							height
							url
						}
						originalSource {
							fileSize
							url
						}
					}
				}
			}
		}
	}
}
`

func findProductImages(
	ctx_ context.Context,
	client_ graphql.Client,
	query string,
	limit int,
) (data_ *findProductImagesResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "findProductImages",
		Query:  findProductImages_Operation,
		Variables: &__findProductImagesInput{
			Query: query,
			Limit: limit,
		},
	}

	data_ = &findProductImagesResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by getProductVars.
const getProductVars_Operation = `
query getProductVars ($id: ID!, $limit: Int!, $cursor: String) {
	product(id: $id) {
		id
		hasOnlyDefaultVariant
		variants(first: $limit, after: $cursor, sortKey: ID) {
			nodes {
				id
				media(first: 1) {
					nodes {
						__typename
						id
					}
				}
			}
			pageInfo {
				endCursor
				hasNextPage
			}
		}
	}
}
`

// ------------------------------------------------
func getProductVars(
	ctx_ context.Context,
	client_ graphql.Client,
	id string,
	limit int,
	cursor *string,
) (data_ *getProductVarsResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "getProductVars",
		Query:  getProductVars_Operation,
		Variables: &__getProductVarsInput{
			Id:     id,
			Limit:  limit,
			Cursor: cursor,
		},
	}

	data_ = &getProductVarsResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by listFiles.
const listFiles_Operation = `
query listFiles ($query: String!, $sortKey: FileSortKeys!, $limit: Int!, $cursor: String) {
	files(first: $limit, query: $query, sortKey: $sortKey, after: $cursor, reverse: false) {
		nodes {
			__typename
			... on MediaImage {
				id
				mimeType
				status
				updatedAt
				image {
					url
				}
				originalSource {
					fileSize
				}
			}
		}
		pageInfo {
			hasNextPage
			endCursor
		}
	}
}
`

// ------------------------------------------------
func listFiles(
	ctx_ context.Context,
	client_ graphql.Client,
	query string,
	sortKey FileSortKeys,
	limit int,
	cursor *string,
) (data_ *listFilesResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "listFiles",
		Query:  listFiles_Operation,
		Variables: &__listFilesInput{
			Query:   query,
			SortKey: sortKey,
			Limit:   limit,
			Cursor:  cursor,
		},
	}

	data_ = &listFilesResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The query executed by listProductFiles.
const listProductFiles_Operation = `
query listProductFiles ($query: String!, $cursor: String, $limit: Int!) {
	products(query: $query, first: $limit, sortKey: UPDATED_AT, after: $cursor, reverse: false) {
		nodes {
			id
			title
			hasOnlyDefaultVariant
			updatedAt
			media(first: 250, query: "media_type:IMAGE") {
				nodes {
					__typename
					... on MediaImage {
						id
						status
						image {
							url
						}
						originalSource {
							fileSize
						}
					}
				}
			}
		}
		pageInfo {
			hasNextPage
			endCursor
		}
	}
}
`

// ------------------------------------------------
func listProductFiles(
	ctx_ context.Context,
	client_ graphql.Client,
	query string,
	cursor *string,
	limit int,
) (data_ *listProductFilesResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "listProductFiles",
		Query:  listProductFiles_Operation,
		Variables: &__listProductFilesInput{
			Query:  query,
			Cursor: cursor,
			Limit:  limit,
		},
	}

	data_ = &listProductFilesResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}

// The mutation executed by relinkProduct.
const relinkProduct_Operation = `
mutation relinkProduct ($productId: ID!, $fileInput: [FileUpdateInput!]!, $moveInput: [MoveInput!]!, $attachMedia: [ProductVariantAppendMediaInput!]!) {
	linkFiles: fileUpdate(files: $fileInput) {
		userErrors {
			message
		}
	}
	productReorderMedia(id: $productId, moves: $moveInput) {
		mediaUserErrors {
			message
		}
	}
	attach: productVariantAppendMedia(productId: $productId, variantMedia: $attachMedia) {
		userErrors {
			message
		}
	}
}
`

// ------------------------------------------------
func relinkProduct(
	ctx_ context.Context,
	client_ graphql.Client,
	productId string,
	fileInput []FileUpdateInput,
	moveInput []MoveInput,
	attachMedia []ProductVariantAppendMediaInput,
) (data_ *relinkProductResponse, err_ error) {
	req_ := &graphql.Request{
		OpName: "relinkProduct",
		Query:  relinkProduct_Operation,
		Variables: &__relinkProductInput{
			ProductId:   productId,
			FileInput:   fileInput,
			MoveInput:   moveInput,
			AttachMedia: attachMedia,
		},
	}

	data_ = &relinkProductResponse{}
	resp_ := &graphql.Response{Data: data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return data_, err_
}
